import { z } from 'zod'

// Auth Schemas
export const LoginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  organizationSlug: z.string().optional(),
  mfaCode: z.string().optional(),
  rememberMe: z.boolean().optional()
})

export const RegisterSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      'Password must contain uppercase, lowercase, number and special character'),
  confirmPassword: z.string(),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  organizationName: z.string().min(1, 'Organization name is required'),
  organizationSlug: z.string()
    .min(3, 'Organization slug must be at least 3 characters')
    .regex(/^[a-z0-9-]+$/, 'Organization slug can only contain lowercase letters, numbers, and hyphens')
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

export const ForgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address')
})

export const ResetPasswordSchema = z.object({
  token: z.string(),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      'Password must contain uppercase, lowercase, number and special character'),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

export const VerifyEmailSchema = z.object({
  token: z.string()
})

export const EnableMFASchema = z.object({
  secret: z.string(),
  code: z.string().length(6, 'MFA code must be 6 digits')
})

export const DisableMFASchema = z.object({
  password: z.string(),
  code: z.string().length(6, 'MFA code must be 6 digits')
})

// Organization Schemas
export const OrganizationCreateSchema = z.object({
  name: z.string().min(1, 'Organization name is required'),
  slug: z.string()
    .min(3, 'Organization slug must be at least 3 characters')
    .regex(/^[a-z0-9-]+$/, 'Organization slug can only contain lowercase letters, numbers, and hyphens'),
  settings: z.object({
    timezone: z.string().default('UTC'),
    language: z.string().default('en'),
    dateFormat: z.string().default('MM/dd/yyyy')
  }).optional()
})

export const OrganizationUpdateSchema = z.object({
  name: z.string().min(1, 'Organization name is required').optional(),
  settings: z.object({
    timezone: z.string(),
    language: z.string(),
    dateFormat: z.string()
  }).optional(),
  branding: z.object({
    logo: z.string().optional(),
    primaryColor: z.string().optional(),
    secondaryColor: z.string().optional()
  }).optional()
})

// User Management Schemas
export const UserInviteSchema = z.object({
  email: z.string().email('Invalid email address'),
  roleIds: z.array(z.string()).min(1, 'At least one role is required'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required')
})

export const UserUpdateSchema = z.object({
  profile: z.object({
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    avatar: z.string().optional(),
    timezone: z.string().optional()
  }).optional(),
  isActive: z.boolean().optional()
})

export const UserRoleUpdateSchema = z.object({
  roleIds: z.array(z.string()),
  scope: z.record(z.any()).optional()
})

// Permission Schemas
export const PermissionCheckSchema = z.object({
  userId: z.string(),
  resource: z.string(),
  action: z.string(),
  scope: z.record(z.any()).optional()
})

export const RoleCreateSchema = z.object({
  name: z.string().min(1, 'Role name is required'),
  level: z.enum(['SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER', 'VIEWER']),
  permissions: z.array(z.string()),
  description: z.string().optional()
})

export const RoleUpdateSchema = z.object({
  name: z.string().min(1, 'Role name is required').optional(),
  permissions: z.array(z.string()).optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional()
})

// API Key Schemas
export const ApiKeyCreateSchema = z.object({
  name: z.string().min(1, 'API key name is required'),
  permissions: z.array(z.string()),
  expiresAt: z.string().datetime().optional()
})

// Session Schemas
export const SessionCreateSchema = z.object({
  userId: z.string(),
  deviceInfo: z.object({
    browser: z.string().optional(),
    os: z.string().optional(),
    device: z.string().optional()
  }).optional(),
  ipAddress: z.string(),
  userAgent: z.string().optional()
})

// Type exports
export type LoginInput = z.infer<typeof LoginSchema>
export type RegisterInput = z.infer<typeof RegisterSchema>
export type ForgotPasswordInput = z.infer<typeof ForgotPasswordSchema>
export type ResetPasswordInput = z.infer<typeof ResetPasswordSchema>
export type VerifyEmailInput = z.infer<typeof VerifyEmailSchema>
export type EnableMFAInput = z.infer<typeof EnableMFASchema>
export type DisableMFAInput = z.infer<typeof DisableMFASchema>
export type OrganizationCreateInput = z.infer<typeof OrganizationCreateSchema>
export type OrganizationUpdateInput = z.infer<typeof OrganizationUpdateSchema>
export type UserInviteInput = z.infer<typeof UserInviteSchema>
export type UserUpdateInput = z.infer<typeof UserUpdateSchema>
export type UserRoleUpdateInput = z.infer<typeof UserRoleUpdateSchema>
export type PermissionCheckInput = z.infer<typeof PermissionCheckSchema>
export type RoleCreateInput = z.infer<typeof RoleCreateSchema>
export type RoleUpdateInput = z.infer<typeof RoleUpdateSchema>
export type ApiKeyCreateInput = z.infer<typeof ApiKeyCreateSchema>
export type SessionCreateInput = z.infer<typeof SessionCreateSchema>