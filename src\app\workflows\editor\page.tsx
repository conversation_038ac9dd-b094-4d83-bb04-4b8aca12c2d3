"use client";

import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useWorkflow } from "@/components/providers/workflow-provider";
import WorkflowDesigner from "@/components/workflows/WorkflowDesigner";
import AppLayout from "@/components/layout/AppLayout";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowLeft, Save } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import Link from "next/link";

export default function WorkflowEditorPage() {
  const searchParams = useSearchParams();
  const workflowId = searchParams.get("id");
  const { workflows, getWorkflow, updateWorkflow, executeWorkflow, isLoading } = useWorkflow();
  const [workflow, setWorkflow] = useState<any>(null);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (workflowId) {
      const workflowData = workflows.find(w => w.id === workflowId);
      if (workflowData) {
        setWorkflow(workflowData);
      }
    }
  }, [workflowId, workflows]);

  const handleSave = async (nodes: any[], connections: any[]) => {
    if (!workflowId || !workflow) return;
    
    setIsSaving(true);
    
    try {
      const updatedWorkflow = await updateWorkflow(workflowId, {
        nodes,
        connections,
        updatedAt: Date.now()
      });
      
      toast({
        title: "Workflow saved",
        description: "Your workflow has been saved successfully.",
      });
      
      setWorkflow(updatedWorkflow);
    } catch (error) {
      console.error("Error saving workflow:", error);
      toast({
        title: "Error saving workflow",
        description: "There was an error saving your workflow. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleExecute = async () => {
    if (!workflowId) return;
    
    try {
      const execution = await executeWorkflow(workflowId, "current-user");
      
      toast({
        title: "Workflow execution started",
        description: `Execution ID: ${execution.id}`,
      });
    } catch (error) {
      console.error("Error executing workflow:", error);
      toast({
        title: "Error executing workflow",
        description: "There was an error executing your workflow. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="sm" asChild className="mr-4">
            <Link href="/workflows">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Workflows
            </Link>
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>
        <Skeleton className="h-[calc(100vh-200px)] w-full" />
      </AppLayout>
    );
  }

  if (!workflow && workflowId) {
    return (
      <AppLayout>
        <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)]">
          <h2 className="text-2xl font-bold mb-2">Workflow Not Found</h2>
          <p className="text-muted-foreground mb-6">
            The workflow you're looking for doesn't exist or you don't have permission to access it.
          </p>
          <Button asChild>
            <Link href="/workflows">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Workflows
            </Link>
          </Button>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout fullWidth>
      <div className="flex items-center mb-4">
        <Button variant="ghost" size="sm" asChild className="mr-4">
          <Link href="/workflows">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Workflows
          </Link>
        </Button>
        <h1 className="text-2xl font-bold">{workflow?.name || "New Workflow"}</h1>
        {isSaving && (
          <div className="ml-4 flex items-center text-sm text-muted-foreground">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving...
          </div>
        )}
      </div>
      
      <div className="h-[calc(100vh-120px)]">
        <WorkflowDesigner
          initialNodes={workflow?.nodes || []}
          initialConnections={workflow?.connections || []}
          onSave={handleSave}
          onExecute={handleExecute}
        />
      </div>
    </AppLayout>
  );
}