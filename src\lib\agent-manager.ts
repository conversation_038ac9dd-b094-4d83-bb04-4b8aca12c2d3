import { Database, SessionManager, QuotaManager } from './database';
import { getAPIX } from './apix';
import { Agent, AgentType, MemoryConfig, ExecutionResult, Session } from './types/agent';
import { authService } from './auth';
import { v4 as uuidv4 } from 'uuid';

export class AgentManager {
  private db: Database;
  private sessionManager: SessionManager;
  private quotaManager: QuotaManager;
  private apix = getAPIX();

  constructor(organizationId: string) {
    this.db = new Database(organizationId);
    this.sessionManager = new SessionManager(organizationId);
    this.quotaManager = new QuotaManager(organizationId);
  }

  // Create new agent
  async createAgent(
    agentData: Omit<Agent, 'id' | 'createdAt' | 'updatedAt' | 'version'>,
    userId: string
  ): Promise<Agent> {
    await this.quotaManager.checkQuota('agents', 1);

    const agent: Agent = {
      ...agentData,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1,
      createdBy: userId,
    };

    const savedAgent = await this.db.orgInsert('agents', {
      id: agent.id,
      name: agent.name,
      description: agent.description,
      type: agent.type,
      model: agent.model,
      system_prompt: agent.systemPrompt,
      tools: agent.tools,
      memory_config: agent.memory,
      provider_config: agent.providerConfig,
      is_active: agent.isActive,
      created_by: agent.createdBy,
      metadata: agent.metadata || {},
      permissions: agent.permissions || {},
      version: agent.version,
    });

    await this.quotaManager.consumeQuota('agents', 1);

    // Emit creation event
    await this.apix.publishEvent({
      type: 'agent_created',
      entityType: 'agent',
      entityId: agent.id,
      organizationId: agentData.organizationId,
      userId,
      data: { agent: savedAgent },
    });

    return this.mapDbToAgent(savedAgent);
  }

  // Get agent by ID
  async getAgent(agentId: string): Promise<Agent | null> {
    const agents = await this.db.orgQuery('agents', 'id = $1', [agentId]);
    return agents.length > 0 ? this.mapDbToAgent(agents[0]) : null;
  }

  // List agents with filtering and pagination
  async listAgents(
    filters: {
      type?: AgentType;
      isActive?: boolean;
      search?: string;
    } = {},
    pagination: { limit?: number; offset?: number } = {}
  ): Promise<{ agents: Agent[]; total: number }> {
    const { limit = 50, offset = 0 } = pagination;
    let whereClause = '';
    const params: any[] = [];

    if (filters.type) {
      whereClause += ` AND type = $${params.length + 1}`;
      params.push(filters.type);
    }

    if (filters.isActive !== undefined) {
      whereClause += ` AND is_active = $${params.length + 1}`;
      params.push(filters.isActive);
    }

    if (filters.search) {
      whereClause += ` AND (name ILIKE $${params.length + 1} OR description ILIKE $${params.length + 1})`;
      params.push(`%${filters.search}%`);
    }

    const agents = await this.db.orgQuery(
      'agents',
      `${whereClause} ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`,
      [...params, limit, offset]
    );

    const countResult = await this.db.orgQuery(
      'agents',
      `${whereClause}`,
      params,
      'COUNT(*) as count'
    );

    return {
      agents: agents.map(this.mapDbToAgent),
      total: parseInt(countResult[0].count),
    };
  }

  // Update agent
  async updateAgent(
    agentId: string,
    updates: Partial<Omit<Agent, 'id' | 'createdAt' | 'organizationId' | 'createdBy'>>,
    userId: string
  ): Promise<Agent> {
    const updateData: any = {
      ...updates,
      updated_at: new Date(),
      version: updates.version || 1,
    };

    // Map field names to database columns
    if (updates.systemPrompt !== undefined) {
      updateData.system_prompt = updates.systemPrompt;
      delete updateData.systemPrompt;
    }
    if (updates.memory !== undefined) {
      updateData.memory_config = updates.memory;
      delete updateData.memory;
    }
    if (updates.providerConfig !== undefined) {
      updateData.provider_config = updates.providerConfig;
      delete updateData.providerConfig;
    }
    if (updates.isActive !== undefined) {
      updateData.is_active = updates.isActive;
      delete updateData.isActive;
    }

    const updatedAgent = await this.db.orgUpdate('agents', agentId, updateData);

    // Emit update event
    await this.apix.publishEvent({
      type: 'agent_updated',
      entityType: 'agent',
      entityId: agentId,
      organizationId: this.db['organizationId'],
      userId,
      data: { updates, agent: updatedAgent },
    });

    return this.mapDbToAgent(updatedAgent);
  }

  // Delete agent
  async deleteAgent(agentId: string, userId: string): Promise<boolean> {
    // Check if agent has active sessions
    const activeSessions = await this.db.orgQuery(
      'sessions',
      'agent_id = $1 AND status = $2',
      [agentId, 'active']
    );

    if (activeSessions.length > 0) {
      throw new Error('Cannot delete agent with active sessions');
    }

    const deleted = await this.db.orgDelete('agents', agentId);

    if (deleted) {
      // Emit deletion event
      await this.apix.publishEvent({
        type: 'agent_deleted',
        entityType: 'agent',
        entityId: agentId,
        organizationId: this.db['organizationId'],
        userId,
        data: { agentId },
      });
    }

    return deleted;
  }

  // Execute agent with real-time streaming
  async executeAgent(
    agentId: string,
    input: string,
    userId: string,
    sessionId?: string,
    context: Record<string, any> = {}
  ): Promise<{ sessionId: string; executionId: string }> {
    const agent = await this.getAgent(agentId);
    if (!agent) {
      throw new Error('Agent not found');
    }

    if (!agent.isActive) {
      throw new Error('Agent is not active');
    }

    // Check quotas
    await this.quotaManager.checkQuota('agent_executions', 1);

    // Create or get session
    const finalSessionId = sessionId || await this.sessionManager.createSession(
      userId,
      'agent',
      { agentId, ...context }
    );

    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Emit execution started event
    await this.apix.publishEvent({
      type: 'agent:execution:started',
      entityType: 'agent',
      entityId: agentId,
      organizationId: agent.organizationId,
      userId,
      data: { 
        input, 
        sessionId: finalSessionId, 
        executionId,
        context 
      },
    });

    // Start async execution
    this.processAgentExecution(agent, input, finalSessionId, executionId, userId, context);

    // Track usage
    await this.quotaManager.trackUsage('agent_executions', agentId, 1);

    return { sessionId: finalSessionId, executionId };
  }

  // Process agent execution (async)
  private async processAgentExecution(
    agent: Agent,
    input: string,
    sessionId: string,
    executionId: string,
    userId: string,
    context: Record<string, any>
  ): Promise<void> {
    const startTime = Date.now();
    let tokenUsage = { input: 0, output: 0, total: 0 };
    let cost = 0;

    try {
      // Get session memory
      const session = await this.sessionManager.getSession(sessionId);
      const memory = session?.memory || {};

      // Emit thinking status
      await this.apix.publishEvent({
        type: 'thinking_status',
        entityType: 'agent',
        entityId: agent.id,
        organizationId: agent.organizationId,
        userId,
        data: { 
          sessionId, 
          executionId, 
          status: 'analyzing_input',
          message: 'Analyzing your request...' 
        },
      });

      // Simulate AI processing with real-time updates
      const processingSteps = [
        { status: 'processing_context', message: 'Processing context and memory...', delay: 800 },
        { status: 'selecting_tools', message: 'Selecting appropriate tools...', delay: 600 },
        { status: 'generating_response', message: 'Generating response...', delay: 1200 },
      ];

      for (const step of processingSteps) {
        await new Promise(resolve => setTimeout(resolve, step.delay));
        
        await this.apix.publishEvent({
          type: 'thinking_status',
          entityType: 'agent',
          entityId: agent.id,
          organizationId: agent.organizationId,
          userId,
          data: { 
            sessionId, 
            executionId, 
            status: step.status,
            message: step.message 
          },
        });
      }

      // Simulate streaming response
      const responseChunks = [
        'I understand your request. Let me help you with that.',
        ' Based on the context provided, I can see that you need assistance with',
        ' the specific task you mentioned. Here\'s what I recommend:',
        '\n\n1. First, let me analyze the current situation',
        '\n2. Then I\'ll provide you with actionable steps',
        '\n3. Finally, I\'ll help you implement the solution',
        '\n\nLet me start by gathering the necessary information...'
      ];

      let fullResponse = '';
      for (let i = 0; i < responseChunks.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));
        
        const chunk = responseChunks[i];
        fullResponse += chunk;
        
        await this.apix.publishEvent({
          type: 'text_chunk',
          entityType: 'agent',
          entityId: agent.id,
          organizationId: agent.organizationId,
          userId,
          data: { 
            sessionId, 
            executionId, 
            chunk,
            fullResponse: fullResponse.substring(0, fullResponse.length)
          },
        });
      }

      // Simulate token usage and cost calculation
      tokenUsage = {
        input: input.length * 0.25, // Rough estimation
        output: fullResponse.length * 0.25,
        total: 0
      };
      tokenUsage.total = tokenUsage.input + tokenUsage.output;
      cost = tokenUsage.total * 0.00002; // $0.00002 per token

      // Update session memory
      await this.sessionManager.updateSessionMemory(sessionId, {
        ...memory,
        lastInput: input,
        lastOutput: fullResponse,
        conversationHistory: [
          ...(memory.conversationHistory || []),
          { role: 'user', content: input, timestamp: new Date() },
          { role: 'assistant', content: fullResponse, timestamp: new Date() }
        ].slice(-20) // Keep last 20 messages
      });

      // Store execution result
      await this.db.orgInsert('agent_executions', {
        id: executionId,
        agent_id: agent.id,
        session_id: sessionId,
        user_id: userId,
        input,
        output: fullResponse,
        status: 'success',
        token_usage: tokenUsage,
        cost,
        execution_time: Date.now() - startTime,
        started_at: new Date(startTime),
        completed_at: new Date(),
        metadata: { context }
      });

      // Emit completion event
      await this.apix.publishEvent({
        type: 'agent:execution:completed',
        entityType: 'agent',
        entityId: agent.id,
        organizationId: agent.organizationId,
        userId,
        data: { 
          sessionId, 
          executionId,
          result: fullResponse,
          tokenUsage,
          cost,
          executionTime: Date.now() - startTime
        },
      });

    } catch (error) {
      // Store error result
      await this.db.orgInsert('agent_executions', {
        id: executionId,
        agent_id: agent.id,
        session_id: sessionId,
        user_id: userId,
        input,
        status: 'error',
        error: error.message,
        execution_time: Date.now() - startTime,
        started_at: new Date(startTime),
        completed_at: new Date(),
        metadata: { context }
      });

      // Emit error event
      await this.apix.publishEvent({
        type: 'error_occurred',
        entityType: 'agent',
        entityId: agent.id,
        organizationId: agent.organizationId,
        userId,
        data: { 
          sessionId, 
          executionId,
          error: error.message 
        },
      });
    }
  }

  // Get agent execution history
  async getExecutionHistory(
    agentId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ executions: any[]; total: number }> {
    const executions = await this.db.orgQuery(
      'agent_executions',
      'agent_id = $1 ORDER BY started_at DESC LIMIT $2 OFFSET $3',
      [agentId, limit, offset]
    );

    const countResult = await this.db.orgQuery(
      'agent_executions',
      'agent_id = $1',
      [agentId],
      'COUNT(*) as count'
    );

    return {
      executions,
      total: parseInt(countResult[0].count),
    };
  }

  // Get agent analytics
  async getAgentAnalytics(
    agentId: string,
    timeRange: { start: Date; end: Date }
  ): Promise<{
    totalExecutions: number;
    successRate: number;
    avgExecutionTime: number;
    totalTokens: number;
    totalCost: number;
    executionsByDay: Array<{ date: string; count: number }>;
  }> {
    const analytics = await this.db.query(`
      SELECT 
        COUNT(*) as total_executions,
        AVG(CASE WHEN status = 'success' THEN 1.0 ELSE 0.0 END) as success_rate,
        AVG(execution_time) as avg_execution_time,
        SUM((token_usage->>'total')::int) as total_tokens,
        SUM(cost) as total_cost,
        DATE(started_at) as execution_date,
        COUNT(*) as daily_count
      FROM agent_executions 
      WHERE organization_id = $1 
        AND agent_id = $2 
        AND started_at >= $3 
        AND started_at <= $4
      GROUP BY DATE(started_at)
      ORDER BY execution_date DESC
    `, [this.db['organizationId'], agentId, timeRange.start, timeRange.end]);

    const summary = analytics.length > 0 ? analytics[0] : {
      total_executions: 0,
      success_rate: 0,
      avg_execution_time: 0,
      total_tokens: 0,
      total_cost: 0
    };

    return {
      totalExecutions: parseInt(summary.total_executions) || 0,
      successRate: parseFloat(summary.success_rate) || 0,
      avgExecutionTime: parseFloat(summary.avg_execution_time) || 0,
      totalTokens: parseInt(summary.total_tokens) || 0,
      totalCost: parseFloat(summary.total_cost) || 0,
      executionsByDay: analytics.map(row => ({
        date: row.execution_date,
        count: parseInt(row.daily_count)
      }))
    };
  }

  // Helper method to map database record to Agent type
  private mapDbToAgent(dbRecord: any): Agent {
    return {
      id: dbRecord.id,
      name: dbRecord.name,
      description: dbRecord.description,
      type: dbRecord.type,
      model: dbRecord.model,
      systemPrompt: dbRecord.system_prompt,
      tools: dbRecord.tools || [],
      memory: dbRecord.memory_config || { enabled: false, type: 'none', maxTokens: 0 },
      providerConfig: dbRecord.provider_config || { primary: '', fallbacks: [], routing: { strategy: 'latency' } },
      isActive: dbRecord.is_active,
      organizationId: dbRecord.organization_id,
      createdBy: dbRecord.created_by,
      createdAt: dbRecord.created_at,
      updatedAt: dbRecord.updated_at,
      version: dbRecord.version,
      metadata: dbRecord.metadata || {},
      permissions: dbRecord.permissions || {}
    };
  }
}

// Singleton pattern for agent manager
const agentManagers = new Map<string, AgentManager>();

export function getAgentManager(organizationId: string): AgentManager {
  if (!agentManagers.has(organizationId)) {
    agentManagers.set(organizationId, new AgentManager(organizationId));
  }
  return agentManagers.get(organizationId)!;
}