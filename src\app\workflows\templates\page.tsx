"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { ArrowRight, Check, Copy, Download, FileText, Filter, Search, Star, Tag } from "lucide-react";
import AppLayout from "@/components/layout/AppLayout";
import { useRouter } from "next/navigation";
import { useWorkflow } from "@/components/providers/workflow-provider";

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  complexity: "beginner" | "intermediate" | "advanced";
  popularity: number;
  author: {
    name: string;
    avatar?: string;
  };
  preview?: string;
  nodes: number;
  connections: number;
  createdAt: string;
  updatedAt: string;
}

export default function WorkflowTemplatesPage() {
  const router = useRouter();
  const { templates, createFromTemplate } = useWorkflow();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importSuccess, setImportSuccess] = useState(false);
  const [customName, setCustomName] = useState("");

  // Sample template data
  const templateData: WorkflowTemplate[] = [
    {
      id: "customer-support",
      name: "Customer Support Assistant",
      description: "Automated customer support workflow with ticket classification, response generation, and human escalation",
      category: "support",
      tags: ["customer service", "ticket management", "escalation"],
      complexity: "beginner",
      popularity: 4.8,
      author: {
        name: "SynapseAI Team",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=team"
      },
      preview: "https://images.unsplash.com/photo-1556745757-8d76bdb6984b?w=800&q=80",
      nodes: 6,
      connections: 7,
      createdAt: "2023-10-15",
      updatedAt: "2024-02-20"
    },
    {
      id: "content-generation",
      name: "Content Generation Pipeline",
      description: "Generate blog posts, social media content, and marketing copy with AI review and approval workflow",
      category: "marketing",
      tags: ["content", "blog", "social media"],
      complexity: "intermediate",
      popularity: 4.5,
      author: {
        name: "Marketing AI",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=marketing"
      },
      preview: "https://images.unsplash.com/photo-1499750310107-5fef28a66643?w=800&q=80",
      nodes: 8,
      connections: 10,
      createdAt: "2023-11-05",
      updatedAt: "2024-03-10"
    },
    {
      id: "data-processing",
      name: "Data Processing & Analysis",
      description: "Extract, transform, and analyze data from multiple sources with visualization and reporting",
      category: "data",
      tags: ["ETL", "analysis", "reporting"],
      complexity: "advanced",
      popularity: 4.7,
      author: {
        name: "Data Science Hub",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=data"
      },
      preview: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&q=80",
      nodes: 12,
      connections: 15,
      createdAt: "2023-09-20",
      updatedAt: "2024-04-05"
    },
    {
      id: "document-qa",
      name: "Document Q&A System",
      description: "Upload documents and ask questions with semantic search and citation tracking",
      category: "knowledge",
      tags: ["documents", "search", "Q&A"],
      complexity: "intermediate",
      popularity: 4.6,
      author: {
        name: "Knowledge Systems",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=knowledge"
      },
      preview: "https://images.unsplash.com/photo-1614332287897-cdc485fa562d?w=800&q=80",
      nodes: 7,
      connections: 8,
      createdAt: "2023-12-10",
      updatedAt: "2024-03-25"
    },
    {
      id: "sales-assistant",
      name: "Sales Assistant Workflow",
      description: "Lead qualification, personalized outreach, and follow-up scheduling with CRM integration",
      category: "sales",
      tags: ["sales", "CRM", "outreach"],
      complexity: "beginner",
      popularity: 4.4,
      author: {
        name: "Sales Automation",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=sales"
      },
      preview: "https://images.unsplash.com/photo-1552581234-26160f608093?w=800&q=80",
      nodes: 5,
      connections: 6,
      createdAt: "2024-01-15",
      updatedAt: "2024-04-10"
    },
    {
      id: "research-assistant",
      name: "Research Assistant",
      description: "Literature review, summarization, and insight generation for academic and business research",
      category: "research",
      tags: ["academic", "literature", "summarization"],
      complexity: "advanced",
      popularity: 4.9,
      author: {
        name: "Research AI",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=research"
      },
      preview: "https://images.unsplash.com/photo-1532619675605-1ede6c2ed2b0?w=800&q=80",
      nodes: 10,
      connections: 12,
      createdAt: "2023-08-05",
      updatedAt: "2024-02-15"
    },
    {
      id: "code-assistant",
      name: "Code Assistant Workflow",
      description: "Code generation, review, testing, and documentation with version control integration",
      category: "development",
      tags: ["coding", "testing", "documentation"],
      complexity: "advanced",
      popularity: 4.7,
      author: {
        name: "Dev Tools AI",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=dev"
      },
      preview: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&q=80",
      nodes: 9,
      connections: 11,
      createdAt: "2023-10-25",
      updatedAt: "2024-03-30"
    },
    {
      id: "hr-onboarding",
      name: "HR Onboarding Process",
      description: "Automated employee onboarding with document collection, training assignment, and progress tracking",
      category: "hr",
      tags: ["onboarding", "training", "HR"],
      complexity: "intermediate",
      popularity: 4.5,
      author: {
        name: "HR Solutions",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=hr"
      },
      preview: "https://images.unsplash.com/photo-1521791136064-7986c2920216?w=800&q=80",
      nodes: 7,
      connections: 9,
      createdAt: "2023-11-30",
      updatedAt: "2024-04-15"
    }
  ];

  // Categories derived from templates
  const categories = [
    { id: "all", name: "All Templates" },
    ...Array.from(new Set(templateData.map(t => t.category))).map(category => ({
      id: category,
      name: category.charAt(0).toUpperCase() + category.slice(1)
    }))
  ];

  // Filter templates based on search and category
  const filteredTemplates = templateData.filter(template => {
    const matchesSearch = searchQuery === "" || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const handleTemplateSelect = (template: WorkflowTemplate) => {
    setSelectedTemplate(template);
    setCustomName(template.name);
    setIsTemplateDialogOpen(true);
  };

  const handleImportTemplate = async () => {
    if (!selectedTemplate) return;
    
    setIsImporting(true);
    
    try {
      // Use the actual workflow manager to create from template
      const systemTemplateId = selectedTemplate.id;
      const workflow = await createFromTemplate(systemTemplateId, customName);
      
      setImportSuccess(true);
      
      // Reset after showing success message
      setTimeout(() => {
        setIsTemplateDialogOpen(false);
        setImportSuccess(false);
        setSelectedTemplate(null);
        
        // Navigate to the workflow editor
        router.push(`/workflows/editor?id=${workflow.id}`);
      }, 2000);
    } catch (error) {
      console.error("Error importing template:", error);
      setIsImporting(false);
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case "beginner": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "intermediate": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "advanced": return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Workflow Templates</h2>
            <p className="text-muted-foreground">
              Browse and use pre-built workflow templates to accelerate your development
            </p>
          </div>
          <div className="flex items-center gap-2">
            <div className="relative w-full md:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search templates..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all" value={selectedCategory} onValueChange={setSelectedCategory}>
          <TabsList className="mb-4 overflow-auto">
            {categories.map(category => (
              <TabsTrigger key={category.id} value={category.id}>
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map(template => (
              <Card key={template.id} className="overflow-hidden flex flex-col">
                {template.preview && (
                  <div className="h-40 overflow-hidden">
                    <img 
                      src={template.preview} 
                      alt={template.name} 
                      className="w-full h-full object-cover transition-transform hover:scale-105"
                    />
                  </div>
                )}
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                    <div className="flex items-center">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                      <span className="text-sm">{template.popularity}</span>
                    </div>
                  </div>
                  <CardDescription className="line-clamp-2">
                    {template.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pb-3 flex-grow">
                  <div className="flex flex-wrap gap-1 mb-3">
                    <Badge variant="outline" className={getComplexityColor(template.complexity)}>
                      {template.complexity}
                    </Badge>
                    {template.tags.slice(0, 2).map(tag => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {template.tags.length > 2 && (
                      <Badge variant="secondary" className="text-xs">
                        +{template.tags.length - 2}
                      </Badge>
                    )}
                  </div>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <FileText className="h-3.5 w-3.5 mr-1" />
                      <span>{template.nodes} nodes</span>
                    </div>
                    <div>
                      Updated {new Date(template.updatedAt).toLocaleDateString()}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-0 border-t">
                  <div className="flex justify-between items-center w-full">
                    <div className="flex items-center">
                      <div className="w-6 h-6 rounded-full overflow-hidden mr-2">
                        <img 
                          src={template.author.avatar || "https://api.dicebear.com/7.x/avataaars/svg?seed=default"} 
                          alt={template.author.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <span className="text-xs text-muted-foreground">{template.author.name}</span>
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => handleTemplateSelect(template)}>
                      Use Template
                      <ArrowRight className="ml-2 h-3.5 w-3.5" />
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>

          {filteredTemplates.length === 0 && (
            <div className="text-center py-12">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
                <Search className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">No templates found</h3>
              <p className="text-muted-foreground max-w-md mx-auto">
                We couldn't find any templates matching your search criteria. Try adjusting your search or browse all templates.
              </p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => {
                  setSearchQuery("");
                  setSelectedCategory("all");
                }}
              >
                Clear filters
              </Button>
            </div>
          )}
        </Tabs>
      </div>

      {/* Template Details Dialog */}
      {selectedTemplate && (
        <Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{selectedTemplate.name}</DialogTitle>
              <DialogDescription>
                Import this template to create a new workflow
              </DialogDescription>
            </DialogHeader>
            
            {!importSuccess ? (
              <>
                <div className="grid gap-4 py-4">
                  {selectedTemplate.preview && (
                    <div className="h-48 overflow-hidden rounded-md">
                      <img 
                        src={selectedTemplate.preview} 
                        alt={selectedTemplate.name} 
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  
                  <p className="text-sm">{selectedTemplate.description}</p>
                  
                  <div className="flex flex-wrap gap-1 my-2">
                    <Badge variant="outline" className={getComplexityColor(selectedTemplate.complexity)}>
                      {selectedTemplate.complexity}
                    </Badge>
                    {selectedTemplate.tags.map(tag => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Nodes</p>
                      <p>{selectedTemplate.nodes}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Connections</p>
                      <p>{selectedTemplate.connections}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Created</p>
                      <p>{new Date(selectedTemplate.createdAt).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Updated</p>
                      <p>{new Date(selectedTemplate.updatedAt).toLocaleDateString()}</p>
                    </div>
                  </div>
                  
                  <div className="grid gap-2 mt-2">
                    <Label htmlFor="workflow-name">Workflow Name</Label>
                    <Input 
                      id="workflow-name" 
                      value={customName}
                      onChange={(e) => setCustomName(e.target.value)}
                    />
                  </div>
                </div>
                
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsTemplateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleImportTemplate} disabled={isImporting || !customName.trim()}>
                    {isImporting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Importing...
                      </>
                    ) : (
                      <>
                        <Copy className="mr-2 h-4 w-4" />
                        Import Template
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </>
            ) : (
              <div className="py-6 flex flex-col items-center justify-center">
                <div className="rounded-full bg-green-100 p-3 mb-4">
                  <Check className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-lg font-medium text-center">Template Imported!</h3>
                <p className="text-center text-muted-foreground mt-2">
                  Your new workflow "{customName}" has been created successfully.
                </p>
                <Button className="mt-6" onClick={() => setIsTemplateDialogOpen(false)}>
                  <ArrowRight className="mr-2 h-4 w-4" />
                  Go to Workflow Editor
                </Button>
              </div>
            )}
          </DialogContent>
        </Dialog>
      )}
    </AppLayout>
  );
}