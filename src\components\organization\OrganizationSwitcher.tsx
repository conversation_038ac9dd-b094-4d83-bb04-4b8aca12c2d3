"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/components/auth/AuthProvider";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Building2,
  ChevronDown,
  Plus,
  Settings,
  Users,
  Crown,
  Check,
  Loader2,
} from "lucide-react";

interface Organization {
  id: string;
  name: string;
  slug: string;
  subscription: "FREE" | "STARTER" | "PROFESSIONAL" | "ENTERPRISE";
  features: string[];
  quotas: Record<string, number>;
  memberCount?: number;
  role?: string;
}

export default function OrganizationSwitcher() {
  const { user, organization: currentOrg, switchOrganization } = useAuth();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(false);
  const [switchingTo, setSwitchingTo] = useState<string | null>(null);

  const API_BASE = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

  useEffect(() => {
    fetchUserOrganizations();
  }, []);

  const fetchUserOrganizations = async () => {
    try {
      const token = localStorage.getItem("accessToken");
      const response = await fetch(`${API_BASE}/users/organizations`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setOrganizations(data);
      }
    } catch (error) {
      console.error("Failed to fetch organizations:", error);
    }
  };

  const handleSwitchOrganization = async (orgSlug: string) => {
    if (orgSlug === currentOrg?.slug) return;

    setSwitchingTo(orgSlug);
    setLoading(true);

    try {
      await switchOrganization(orgSlug);
    } catch (error) {
      console.error("Failed to switch organization:", error);
    } finally {
      setSwitchingTo(null);
      setLoading(false);
    }
  };

  const getSubscriptionBadge = (subscription: string) => {
    const variants = {
      FREE: "outline",
      STARTER: "secondary",
      PROFESSIONAL: "default",
      ENTERPRISE: "destructive",
    } as const;

    return variants[subscription as keyof typeof variants] || "outline";
  };

  const getSubscriptionColor = (subscription: string) => {
    const colors = {
      FREE: "text-gray-500",
      STARTER: "text-blue-500",
      PROFESSIONAL: "text-purple-500",
      ENTERPRISE: "text-red-500",
    };

    return colors[subscription as keyof typeof colors] || "text-gray-500";
  };

  if (!currentOrg) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="w-full justify-between h-auto p-3 hover:bg-accent/50"
          disabled={loading}
        >
          <div className="flex items-center space-x-3">
            <Avatar className="w-8 h-8">
              <AvatarImage
                src={`https://api.dicebear.com/7.x/initials/svg?seed=${currentOrg.name}`}
              />
              <AvatarFallback>
                <Building2 className="w-4 h-4" />
              </AvatarFallback>
            </Avatar>
            <div className="text-left">
              <p className="font-medium text-sm">{currentOrg.name}</p>
              <p className="text-xs text-muted-foreground">
                {currentOrg.subscription} Plan
              </p>
            </div>
          </div>
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <ChevronDown className="w-4 h-4" />
          )}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent className="w-80" align="start">
        <div className="p-2">
          <p className="text-sm font-medium mb-2">Switch Organization</p>

          {/* Current Organization */}
          <div className="mb-2">
            <div className="flex items-center justify-between p-2 rounded-md bg-accent/50">
              <div className="flex items-center space-x-3">
                <Avatar className="w-8 h-8">
                  <AvatarImage
                    src={`https://api.dicebear.com/7.x/initials/svg?seed=${currentOrg.name}`}
                  />
                  <AvatarFallback>
                    <Building2 className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-sm">{currentOrg.name}</p>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={getSubscriptionBadge(currentOrg.subscription)}
                      className="text-xs"
                    >
                      {currentOrg.subscription}
                    </Badge>
                    {user?.roles?.some((r) => r.level === "ORG_ADMIN") && (
                      <Crown className="w-3 h-3 text-yellow-500" />
                    )}
                  </div>
                </div>
              </div>
              <Check className="w-4 h-4 text-green-500" />
            </div>
          </div>

          {/* Other Organizations */}
          {organizations
            .filter((org) => org.id !== currentOrg.id)
            .map((org) => (
              <DropdownMenuItem
                key={org.id}
                className="p-2 cursor-pointer"
                onClick={() => handleSwitchOrganization(org.slug)}
                disabled={switchingTo === org.slug}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-8 h-8">
                      <AvatarImage
                        src={`https://api.dicebear.com/7.x/initials/svg?seed=${org.name}`}
                      />
                      <AvatarFallback>
                        <Building2 className="w-4 h-4" />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-sm">{org.name}</p>
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant={getSubscriptionBadge(org.subscription)}
                          className="text-xs"
                        >
                          {org.subscription}
                        </Badge>
                        {org.memberCount && (
                          <span className="text-xs text-muted-foreground">
                            {org.memberCount} members
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  {switchingTo === org.slug && (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  )}
                </div>
              </DropdownMenuItem>
            ))}

          <DropdownMenuSeparator />

          {/* Actions */}
          <DropdownMenuItem className="p-2">
            <Plus className="w-4 h-4 mr-2" />
            Create Organization
          </DropdownMenuItem>

          <DropdownMenuItem className="p-2">
            <Settings className="w-4 h-4 mr-2" />
            Organization Settings
          </DropdownMenuItem>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
