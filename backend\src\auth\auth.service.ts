import { Injectable, UnauthorizedException, ConflictException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { LoginDto, RegisterDto, RefreshTokenDto } from './dto/auth.dto';
import * as bcrypt from 'bcryptjs';
import { User, Organization } from '@prisma/client';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {}

  async register(dto: RegisterDto) {
    const existingUser = await this.prisma.user.findUnique({
      where: { email: dto.email },
    });

    if (existingUser) {
      throw new ConflictException('User already exists');
    }

    const hashedPassword = await bcrypt.hash(dto.password, 12);

    let organization: Organization;
    
    if (dto.organizationSlug) {
      organization = await this.prisma.organization.findUnique({
        where: { slug: dto.organizationSlug },
      });
      
      if (!organization) {
        throw new UnauthorizedException('Organization not found');
      }
    } else {
      organization = await this.prisma.organization.create({
        data: {
          name: dto.organizationName || `${dto.email.split('@')[0]}'s Organization`,
          slug: `org-${Date.now()}`,
          settings: {
            timezone: 'UTC',
            language: 'en',
            dateFormat: 'YYYY-MM-DD',
          },
          branding: {},
          features: ['agents', 'workflows', 'tools'],
          quotas: {
            agents: 10,
            workflows: 5,
            tools: 20,
          },
          subscription: 'FREE',
        },
      });
    }

    const user = await this.prisma.user.create({
      data: {
        email: dto.email,
        passwordHash: hashedPassword,
        organizationId: organization.id,
        profile: {
          firstName: dto.firstName,
          lastName: dto.lastName,
          avatar: null,
        },
      },
    });

    const defaultRole = await this.prisma.role.findFirst({
      where: { name: 'ORG_ADMIN', isSystem: true },
    });

    if (defaultRole) {
      await this.prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: defaultRole.id,
        },
      });
    }

    const tokens = await this.generateTokens(user.id, organization.id);
    await this.createSession(user.id, tokens.refreshToken, dto.deviceInfo);

    return {
      user: {
        id: user.id,
        email: user.email,
        profile: user.profile,
        organizationId: user.organizationId,
      },
      organization: {
        id: organization.id,
        name: organization.name,
        slug: organization.slug,
      },
      ...tokens,
    };
  }

  async login(dto: LoginDto) {
    const user = await this.prisma.user.findUnique({
      where: { email: dto.email },
      include: {
        organization: true,
        roles: {
          include: { role: true },
        },
      },
    });

    if (!user || !user.passwordHash) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await bcrypt.compare(dto.password, user.passwordHash);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (dto.organizationSlug && user.organization.slug !== dto.organizationSlug) {
      throw new UnauthorizedException('Invalid organization');
    }

    if (user.mfaEnabled && !dto.mfaCode) {
      return { requiresMfa: true };
    }

    if (user.mfaEnabled && dto.mfaCode) {
      const isValidMfa = this.verifyMfaCode(user.mfaSecret, dto.mfaCode);
      if (!isValidMfa) {
        throw new UnauthorizedException('Invalid MFA code');
      }
    }

    await this.prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() },
    });

    const tokens = await this.generateTokens(user.id, user.organizationId);
    await this.createSession(user.id, tokens.refreshToken, dto.deviceInfo);

    return {
      user: {
        id: user.id,
        email: user.email,
        profile: user.profile,
        organizationId: user.organizationId,
        roles: user.roles.map(ur => ur.role),
      },
      organization: {
        id: user.organization.id,
        name: user.organization.name,
        slug: user.organization.slug,
      },
      ...tokens,
    };
  }

  async refreshToken(dto: RefreshTokenDto) {
    const session = await this.prisma.session.findUnique({
      where: { refreshToken: dto.refreshToken },
      include: { user: { include: { organization: true } } },
    });

    if (!session || session.expiresAt < new Date()) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const tokens = await this.generateTokens(session.userId, session.user.organizationId);
    
    await this.prisma.session.update({
      where: { id: session.id },
      data: {
        token: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      },
    });

    return tokens;
  }

  async logout(refreshToken: string) {
    await this.prisma.session.delete({
      where: { refreshToken },
    });
  }

  private async generateTokens(userId: string, organizationId: string) {
    const payload = { sub: userId, organizationId };
    
    const accessToken = this.jwtService.sign(payload, { expiresIn: '15m' });
    const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

    return { accessToken, refreshToken };
  }

  private async createSession(userId: string, refreshToken: string, deviceInfo: any) {
    return this.prisma.session.create({
      data: {
        userId,
        token: refreshToken,
        refreshToken,
        deviceInfo: deviceInfo || {},
        ipAddress: '0.0.0.0',
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      },
    });
  }

  private verifyMfaCode(secret: string, code: string): boolean {
    // Implementation for TOTP verification
    return true; // Placeholder
  }
}