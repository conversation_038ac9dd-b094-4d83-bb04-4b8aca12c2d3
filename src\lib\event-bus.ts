import { io, Socket } from 'socket.io-client';

export interface EventBusMessage {
  id: string;
  type: string;
  channel: string;
  data: any;
  timestamp: number;
  userId?: string;
  sessionId?: string;
}

export interface EventBusConfig {
  url: string;
  reconnectionAttempts: number;
  reconnectionDelay: number;
  timeout: number;
  compression: boolean;
}

export type EventChannel = 
  | 'agent-events'
  | 'tool-events' 
  | 'workflow-events'
  | 'provider-events'
  | 'system-events';

export type EventType = 
  | 'tool_call_start'
  | 'tool_call_result'
  | 'tool_call_error'
  | 'thinking_status'
  | 'text_chunk'
  | 'state_update'
  | 'request_user_input'
  | 'session_start'
  | 'session_end'
  | 'error_occurred'
  | 'fallback_triggered'
  | 'agent_created'
  | 'agent_updated'
  | 'agent_deleted'
  | 'workflow_started'
  | 'workflow_completed'
  | 'workflow_failed'
  | 'provider_health_check'
  | 'provider_fallback';

class EventBus {
  private socket: Socket | null = null;
  private config: EventBusConfig;
  private messageQueue: EventBusMessage[] = [];
  private isConnected = false;
  private reconnectAttempts = 0;
  private eventHandlers = new Map<string, Set<(message: EventBusMessage) => void>>();
  private latencyScores = new Map<string, number>();
  private eventHistory: EventBusMessage[] = [];

  constructor(config: Partial<EventBusConfig> = {}) {
    this.config = {
      url: process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:3001',
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      timeout: 30000,
      compression: true,
      ...config
    };
  }

  async connect(): Promise<void> {
    if (this.socket?.connected) return;

    return new Promise((resolve, reject) => {
      this.socket = io(this.config.url, {
        transports: ['websocket'],
        timeout: this.config.timeout,
        compression: this.config.compression,
        reconnectionAttempts: this.config.reconnectionAttempts,
        reconnectionDelay: this.config.reconnectionDelay
      });

      this.socket.on('connect', () => {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.flushMessageQueue();
        resolve();
      });

      this.socket.on('disconnect', () => {
        this.isConnected = false;
      });

      this.socket.on('connect_error', (error) => {
        this.reconnectAttempts++;
        if (this.reconnectAttempts >= this.config.reconnectionAttempts) {
          reject(error);
        }
      });

      this.socket.on('message', (message: EventBusMessage) => {
        this.handleIncomingMessage(message);
      });

      this.socket.on('pong', (latency: number) => {
        this.updateLatencyScore(latency);
      });
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  subscribe(channel: EventChannel, handler: (message: EventBusMessage) => void): () => void {
    const key = channel;
    if (!this.eventHandlers.has(key)) {
      this.eventHandlers.set(key, new Set());
    }
    this.eventHandlers.get(key)!.add(handler);

    if (this.socket?.connected) {
      this.socket.emit('subscribe', { channel });
    }

    return () => {
      this.eventHandlers.get(key)?.delete(handler);
      if (this.eventHandlers.get(key)?.size === 0) {
        this.eventHandlers.delete(key);
        if (this.socket?.connected) {
          this.socket.emit('unsubscribe', { channel });
        }
      }
    };
  }

  publish(channel: EventChannel, type: EventType, data: any, options: { 
    userId?: string; 
    sessionId?: string; 
    priority?: 'low' | 'normal' | 'high';
    retry?: boolean;
  } = {}): void {
    const message: EventBusMessage = {
      id: this.generateId(),
      type,
      channel,
      data,
      timestamp: Date.now(),
      userId: options.userId,
      sessionId: options.sessionId
    };

    if (this.isConnected && this.socket) {
      this.socket.emit('publish', message);
    } else {
      if (options.retry !== false) {
        this.messageQueue.push(message);
      }
    }

    this.eventHistory.push(message);
    if (this.eventHistory.length > 1000) {
      this.eventHistory = this.eventHistory.slice(-1000);
    }
  }

  joinRoom(roomId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('join-room', { roomId });
    }
  }

  leaveRoom(roomId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('leave-room', { roomId });
    }
  }

  getLatencyScore(): number {
    const scores = Array.from(this.latencyScores.values());
    return scores.length > 0 ? scores.reduce((a, b) => a + b) / scores.length : 0;
  }

  getEventHistory(channel?: EventChannel, limit = 100): EventBusMessage[] {
    let history = this.eventHistory;
    if (channel) {
      history = history.filter(msg => msg.channel === channel);
    }
    return history.slice(-limit);
  }

  replayEvents(channel: EventChannel, fromTimestamp: number): void {
    if (this.socket?.connected) {
      this.socket.emit('replay-events', { channel, fromTimestamp });
    }
  }

  private handleIncomingMessage(message: EventBusMessage): void {
    const handlers = this.eventHandlers.get(message.channel);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Error handling event:', error);
        }
      });
    }
  }

  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.socket?.connected) {
      const message = this.messageQueue.shift()!;
      this.socket.emit('publish', message);
    }
  }

  private updateLatencyScore(latency: number): void {
    const timestamp = Date.now();
    this.latencyScores.set(timestamp.toString(), latency);
    
    const cutoff = timestamp - 60000; // Keep last minute
    for (const [key] of this.latencyScores) {
      if (parseInt(key) < cutoff) {
        this.latencyScores.delete(key);
      }
    }
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const eventBus = new EventBus();

export default EventBus;