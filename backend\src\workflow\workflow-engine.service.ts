import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AgentService } from '../agent/agent.service';
import { ToolService } from '../tool/tool.service';

export interface WorkflowNode {
  id: string;
  type: 'agent' | 'tool' | 'condition' | 'trigger' | 'action';
  config: any;
  position: { x: number; y: number };
}

export interface WorkflowEdge {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  condition?: string;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  input: any;
  output?: any;
  currentNodeId?: string;
  nodeResults: Map<string, any>;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
}

@Injectable()
export class WorkflowEngineService {
  private activeExecutions = new Map<string, WorkflowExecution>();

  constructor(
    private prisma: PrismaService,
    private agentService: AgentService,
    private toolService: ToolService,
  ) {}

  async executeWorkflow(
    workflowId: string,
    organizationId: string,
    input: any,
    userId: string,
  ): Promise<WorkflowExecution> {
    const workflow = await this.prisma.workflow.findFirst({
      where: { id: workflowId, organizationId },
      include: { nodes: true, edges: true },
    });

    if (!workflow) {
      throw new BadRequestException('Workflow not found');
    }

    if (!workflow.isActive) {
      throw new BadRequestException('Workflow is not active');
    }

    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const execution: WorkflowExecution = {
      id: executionId,
      workflowId,
      status: 'running',
      input,
      nodeResults: new Map(),
      startedAt: new Date(),
    };

    this.activeExecutions.set(executionId, execution);

    // Store execution in database
    await this.prisma.workflowExecution.create({
      data: {
        id: executionId,
        workflowId,
        status: 'RUNNING',
        input,
        logs: [],
        startedAt: execution.startedAt,
      },
    });

    try {
      // Find trigger nodes
      const triggerNodes = workflow.nodes.filter(node => 
        JSON.parse(node.config as string).type === 'trigger'
      );

      if (triggerNodes.length === 0) {
        throw new Error('No trigger nodes found');
      }

      // Execute from trigger nodes
      for (const triggerNode of triggerNodes) {
        await this.executeNode(execution, workflow, triggerNode.id, input, organizationId, userId);
      }

      execution.status = 'completed';
      execution.completedAt = new Date();

      // Update database
      await this.prisma.workflowExecution.update({
        where: { id: executionId },
        data: {
          status: 'COMPLETED',
          output: execution.output,
          completedAt: execution.completedAt,
        },
      });

    } catch (error) {
      execution.status = 'failed';
      execution.error = error.message;
      execution.completedAt = new Date();

      await this.prisma.workflowExecution.update({
        where: { id: executionId },
        data: {
          status: 'FAILED',
          output: { error: error.message },
          completedAt: execution.completedAt,
        },
      });
    } finally {
      this.activeExecutions.delete(executionId);
    }

    return execution;
  }

  private async executeNode(
    execution: WorkflowExecution,
    workflow: any,
    nodeId: string,
    inputData: any,
    organizationId: string,
    userId: string,
  ): Promise<any> {
    const node = workflow.nodes.find(n => n.id === nodeId);
    if (!node) {
      throw new Error(`Node ${nodeId} not found`);
    }

    execution.currentNodeId = nodeId;
    const config = JSON.parse(node.config as string);

    let result: any;

    switch (config.type) {
      case 'agent':
        result = await this.executeAgentNode(config, inputData, organizationId);
        break;
      case 'tool':
        result = await this.executeToolNode(config, inputData, organizationId);
        break;
      case 'condition':
        result = await this.executeConditionNode(config, inputData);
        break;
      case 'action':
        result = await this.executeActionNode(config, inputData);
        break;
      case 'trigger':
        result = inputData;
        break;
      default:
        throw new Error(`Unknown node type: ${config.type}`);
    }

    execution.nodeResults.set(nodeId, result);

    // Execute connected nodes
    const outgoingEdges = workflow.edges.filter(edge => edge.sourceNodeId === nodeId);
    
    for (const edge of outgoingEdges) {
      const shouldExecute = this.evaluateEdgeCondition(edge, result);
      if (shouldExecute) {
        await this.executeNode(execution, workflow, edge.targetNodeId, result, organizationId, userId);
      }
    }

    return result;
  }

  private async executeAgentNode(config: any, inputData: any, organizationId: string): Promise<any> {
    if (!config.agentId) {
      throw new Error('Agent ID not specified in node config');
    }

    const message = typeof inputData === 'string' ? inputData : JSON.stringify(inputData);
    
    const result = await this.agentService.chat(
      config.agentId,
      organizationId,
      message,
    );

    return {
      type: 'agent',
      agentId: config.agentId,
      input: message,
      output: result.response,
      cost: result.cost,
      executionTime: result.executionTime,
    };
  }

  private async executeToolNode(config: any, inputData: any, organizationId: string): Promise<any> {
    if (!config.toolId) {
      throw new Error('Tool ID not specified in node config');
    }

    const result = await this.toolService.execute(
      config.toolId,
      organizationId,
      inputData,
    );

    return {
      type: 'tool',
      toolId: config.toolId,
      input: inputData,
      output: result.result,
      success: result.success,
      cost: result.cost,
      executionTime: result.executionTime,
    };
  }

  private async executeConditionNode(config: any, inputData: any): Promise<any> {
    const condition = config.condition;
    if (!condition) {
      throw new Error('Condition not specified');
    }

    const result = this.evaluateCondition(condition, inputData);
    
    return {
      type: 'condition',
      condition,
      input: inputData,
      result,
    };
  }

  private async executeActionNode(config: any, inputData: any): Promise<any> {
    const action = config.action;
    
    switch (action) {
      case 'log':
        console.log('Workflow Log:', inputData);
        return { type: 'log', message: 'Logged successfully', input: inputData };
      
      case 'transform':
        const transformed = this.transformData(inputData, config.transformation);
        return { type: 'transform', input: inputData, output: transformed };
      
      case 'delay':
        const delay = config.delay || 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return { type: 'delay', delay, input: inputData };
      
      default:
        throw new Error(`Unknown action: ${action}`);
    }
  }

  private evaluateEdgeCondition(edge: any, data: any): boolean {
    if (!edge.condition) return true;
    
    return this.evaluateCondition(edge.condition, data);
  }

  private evaluateCondition(condition: string, data: any): boolean {
    try {
      // Simple condition evaluation - in production, use a proper expression evaluator
      const func = new Function('data', `return ${condition}`);
      return Boolean(func(data));
    } catch (error) {
      console.warn('Condition evaluation failed:', error.message);
      return false;
    }
  }

  private transformData(data: any, transformation: any): any {
    if (!transformation) return data;
    
    // Simple data transformation - extend as needed
    if (transformation.type === 'map') {
      return transformation.mapping.reduce((acc, map) => {
        acc[map.target] = this.getNestedValue(data, map.source);
        return acc;
      }, {});
    }
    
    return data;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  async getExecution(executionId: string): Promise<WorkflowExecution | null> {
    const active = this.activeExecutions.get(executionId);
    if (active) return active;

    const dbExecution = await this.prisma.workflowExecution.findUnique({
      where: { id: executionId },
    });

    if (!dbExecution) return null;

    return {
      id: dbExecution.id,
      workflowId: dbExecution.workflowId,
      status: dbExecution.status.toLowerCase() as any,
      input: dbExecution.input,
      output: dbExecution.output,
      nodeResults: new Map(),
      startedAt: dbExecution.startedAt,
      completedAt: dbExecution.completedAt,
    };
  }

  async getWorkflowExecutions(workflowId: string, organizationId: string) {
    return this.prisma.workflowExecution.findMany({
      where: {
        workflowId,
        workflow: { organizationId },
      },
      orderBy: { startedAt: 'desc' },
      take: 50,
    });
  }
}