import { z } from 'zod';

// Agent Types Schema
export const AgentTypeSchema = z.enum([
  'standalone',
  'tool-driven', 
  'hybrid',
  'multi-task',
  'multi-provider',
  'collaborative'
]);

// Memory Configuration Schema
export const MemoryConfigSchema = z.object({
  enabled: z.boolean(),
  type: z.enum(['conversation', 'summarization', 'vector', 'none']),
  maxTokens: z.number().min(100).max(32000),
  summarizationThreshold: z.number().optional(),
  vectorDimensions: z.number().optional(),
  retentionDays: z.number().optional()
});

// Tool Schema
export const ToolSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  category: z.enum(['retrieval', 'function', 'creative', 'api', 'database']),
  schema: z.record(z.any()),
  enabled: z.boolean(),
  config: z.record(z.any()).optional()
});

// Provider Configuration Schema
export const ProviderConfigSchema = z.object({
  primary: z.string(),
  fallbacks: z.array(z.string()),
  routing: z.object({
    strategy: z.enum(['latency', 'cost', 'capability', 'round-robin']),
    weights: z.record(z.number()).optional()
  }),
  costLimits: z.object({
    daily: z.number().optional(),
    monthly: z.number().optional(),
    perRequest: z.number().optional()
  }).optional()
});

// Agent Schema
export const AgentSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  description: z.string().max(500),
  type: AgentTypeSchema,
  model: z.string(),
  systemPrompt: z.string(),
  tools: z.array(z.string()),
  memory: MemoryConfigSchema,
  providerConfig: ProviderConfigSchema,
  isActive: z.boolean(),
  organizationId: z.string().uuid(),
  createdBy: z.string().uuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
  version: z.number(),
  metadata: z.record(z.any()).optional(),
  permissions: z.object({
    canEdit: z.array(z.string()),
    canView: z.array(z.string()),
    canExecute: z.array(z.string())
  }).optional()
});

// Session Schema
export const SessionSchema = z.object({
  id: z.string().uuid(),
  agentId: z.string().uuid(),
  userId: z.string().uuid(),
  organizationId: z.string().uuid(),
  status: z.enum(['active', 'paused', 'completed', 'error']),
  context: z.record(z.any()),
  memory: z.array(z.object({
    role: z.enum(['user', 'assistant', 'system', 'tool']),
    content: z.string(),
    timestamp: z.date(),
    metadata: z.record(z.any()).optional()
  })),
  startedAt: z.date(),
  lastActivity: z.date(),
  endedAt: z.date().optional(),
  tokenUsage: z.object({
    input: z.number(),
    output: z.number(),
    total: z.number()
  }),
  cost: z.number().optional()
});

// Execution Result Schema
export const ExecutionResultSchema = z.object({
  success: z.boolean(),
  result: z.any(),
  error: z.string().optional(),
  tokenUsage: z.object({
    input: z.number(),
    output: z.number(),
    total: z.number()
  }).optional(),
  executionTime: z.number(),
  provider: z.string(),
  model: z.string(),
  cost: z.number().optional(),
  metadata: z.record(z.any()).optional()
});

// Event Schemas for APIX
export const AgentEventSchema = z.object({
  type: z.enum([
    'agent_created',
    'agent_updated', 
    'agent_deleted',
    'session_started',
    'session_ended',
    'message_received',
    'message_sent',
    'tool_call_start',
    'tool_call_result',
    'tool_call_error',
    'thinking_status',
    'text_chunk',
    'state_update',
    'request_user_input',
    'error_occurred',
    'fallback_triggered'
  ]),
  agentId: z.string().uuid(),
  sessionId: z.string().uuid().optional(),
  userId: z.string().uuid(),
  organizationId: z.string().uuid(),
  data: z.record(z.any()),
  timestamp: z.date(),
  metadata: z.record(z.any()).optional()
});

// Type exports
export type AgentType = z.infer<typeof AgentTypeSchema>;
export type MemoryConfig = z.infer<typeof MemoryConfigSchema>;
export type Tool = z.infer<typeof ToolSchema>;
export type ProviderConfig = z.infer<typeof ProviderConfigSchema>;
export type Agent = z.infer<typeof AgentSchema>;
export type Session = z.infer<typeof SessionSchema>;
export type ExecutionResult = z.infer<typeof ExecutionResultSchema>;
export type AgentEvent = z.infer<typeof AgentEventSchema>;

// AI Model definitions
export interface AIModel {
  id: string;
  name: string;
  provider: string;
  capabilities: string[];
  contextWindow: number;
  costPer1kTokens: {
    input: number;
    output: number;
  };
  maxOutputTokens: number;
  supportsStreaming: boolean;
  supportsFunctionCalling: boolean;
  supportsVision: boolean;
}

// Provider definitions
export interface Provider {
  id: string;
  name: string;
  baseUrl: string;
  apiKeyRequired: boolean;
  models: AIModel[];
  healthStatus: 'healthy' | 'degraded' | 'down';
  latency: number;
  errorRate: number;
}