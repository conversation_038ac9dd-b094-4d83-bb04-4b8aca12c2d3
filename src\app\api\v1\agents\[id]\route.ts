import { NextRequest, NextResponse } from 'next/server';
import { requireResourceAccess } from '@/lib/auth';
import { AgentManager } from '@/lib/agents';

export const GET = requireResourceAccess('agents', 'read')(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const authContext = (request as any).auth;
    const agentManager = new AgentManager(authContext.organization.id);
    
    const agent = await agentManager.getAgent(params.id);
    
    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: agent
    });

  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'Failed to fetch agent' },
      { status: 500 }
    );
  }
});

export const PUT = requireResourceAccess('agents', 'update')(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const authContext = (request as any).auth;
    const updates = await request.json();
    
    const agentManager = new AgentManager(authContext.organization.id);
    
    const agent = await agentManager.updateAgent(
      params.id,
      updates,
      authContext.user.id
    );

    return NextResponse.json({
      success: true,
      data: agent
    });

  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'Failed to update agent' },
      { status: 500 }
    );
  }
});

export const DELETE = requireResourceAccess('agents', 'delete')(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const authContext = (request as any).auth;
    const agentManager = new AgentManager(authContext.organization.id);
    
    const success = await agentManager.deleteAgent(params.id, authContext.user.id);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Agent deleted successfully'
    });

  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'Failed to delete agent' },
      { status: 500 }
    );
  }
});