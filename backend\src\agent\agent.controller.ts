import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { AgentService } from './agent.service';
import { CreateAgentDto, UpdateAgentDto, ChatDto } from './dto/agent.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Agents')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('agents')
export class AgentController {
  constructor(private agentService: AgentService) {}

  @Post()
  @ApiOperation({ summary: 'Create new agent' })
  create(@Request() req, @Body() dto: CreateAgentDto) {
    return this.agentService.create(req.user.organizationId, dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all agents' })
  findAll(@Request() req) {
    return this.agentService.findAll(req.user.organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get agent by ID' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.agentService.findOne(id, req.user.organizationId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update agent' })
  update(@Param('id') id: string, @Request() req, @Body() dto: UpdateAgentDto) {
    return this.agentService.update(id, req.user.organizationId, dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete agent' })
  remove(@Param('id') id: string, @Request() req) {
    return this.agentService.remove(id, req.user.organizationId);
  }

  @Post(':id/chat')
  @ApiOperation({ summary: 'Chat with agent' })
  async chat(@Param('id') id: string, @Request() req, @Body() dto: ChatDto) {
    return this.agentService.chat(
      id, 
      req.user.organizationId, 
      dto.message, 
      dto.sessionId || null
    );
  }

  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute agent with streaming' })
  async execute(@Param('id') id: string, @Request() req, @Body() dto: ChatDto) {
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Start async execution
    this.agentService.chat(
      id,
      req.user.organizationId,
      dto.message,
      dto.sessionId
    ).catch(error => {
      console.error('Agent execution failed:', error);
    });

    return {
      executionId,
      status: 'started',
      message: 'Agent execution started. Connect to WebSocket for real-time updates.',
    };
  }

  @Get(':id/analytics')
  @ApiOperation({ summary: 'Get agent analytics' })
  async getAnalytics(@Param('id') id: string, @Request() req) {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const now = new Date();
    
    const analytics = await this.agentService.getAnalytics(id, req.user.organizationId, {
      start: thirtyDaysAgo,
      end: now,
    });
    
    return analytics;
  }

  @Get(':id/executions')
  @ApiOperation({ summary: 'Get agent execution history' })
  async getExecutions(@Param('id') id: string, @Request() req) {
    return this.agentService.getExecutionHistory(id, req.user.organizationId);
  }
}