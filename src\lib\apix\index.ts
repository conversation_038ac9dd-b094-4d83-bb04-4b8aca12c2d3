import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { redis, Database } from '../database';
import jwt from 'jsonwebtoken';

export interface APIXEvent {
  type: string;
  entityType?: string;
  entityId?: string;
  organizationId: string;
  userId?: string;
  data: Record<string, any>;
  timestamp: string;
}

export class APIXEngine {
  private io: SocketIOServer;
  private eventHandlers: Map<string, Function[]> = new Map();

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "*",
        methods: ["GET", "POST"]
      },
      transports: ['websocket', 'polling']
    });

    this.setupAuthentication();
    this.setupEventHandlers();
    this.setupRedisSubscription();
  }

  private setupAuthentication(): void {
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
        
        // Verify user and organization access
        const db = new Database();
        const user = await db.query(
          'SELECT u.*, om.organization_id, om.role FROM users u JOIN organization_members om ON u.id = om.user_id WHERE u.id = $1',
          [decoded.userId]
        );

        if (user.length === 0) {
          return next(new Error('Invalid user'));
        }

        socket.data = {
          userId: user[0].id,
          organizationId: user[0].organization_id,
          role: user[0].role,
          email: user[0].email
        };

        // Join organization room
        socket.join(`org:${user[0].organization_id}`);
        socket.join(`user:${user[0].id}`);

        next();
      } catch (error) {
        next(new Error('Authentication failed'));
      }
    });
  }

  private setupEventHandlers(): void {
    this.io.on('connection', (socket) => {
      console.log(`User ${socket.data.userId} connected to org ${socket.data.organizationId}`);

      // Handle client subscriptions
      socket.on('subscribe', (eventTypes: string[]) => {
        eventTypes.forEach(eventType => {
          socket.join(`event:${eventType}`);
        });
      });

      socket.on('unsubscribe', (eventTypes: string[]) => {
        eventTypes.forEach(eventType => {
          socket.leave(`event:${eventType}`);
        });
      });

      // Handle real-time agent execution
      socket.on('agent:execute', async (data) => {
        try {
          await this.handleAgentExecution(socket, data);
        } catch (error) {
          socket.emit('error', { message: error.message });
        }
      });

      // Handle tool execution
      socket.on('tool:execute', async (data) => {
        try {
          await this.handleToolExecution(socket, data);
        } catch (error) {
          socket.emit('error', { message: error.message });
        }
      });

      // Handle hybrid workflow execution
      socket.on('hybrid:execute', async (data) => {
        try {
          await this.handleHybridExecution(socket, data);
        } catch (error) {
          socket.emit('error', { message: error.message });
        }
      });

      socket.on('disconnect', () => {
        console.log(`User ${socket.data.userId} disconnected`);
      });
    });
  }

  private setupRedisSubscription(): void {
    const subscriber = redis.duplicate();
    
    subscriber.subscribe('apix:events', (err) => {
      if (err) {
        console.error('Redis subscription error:', err);
      }
    });

    subscriber.on('message', (channel, message) => {
      if (channel === 'apix:events') {
        try {
          const event: APIXEvent = JSON.parse(message);
          this.broadcastEvent(event);
        } catch (error) {
          console.error('Error processing Redis event:', error);
        }
      }
    });
  }

  // Publish event to Redis for distribution
  async publishEvent(event: APIXEvent): Promise<void> {
    event.timestamp = new Date().toISOString();
    
    // Store in database for persistence and replay
    const db = new Database(event.organizationId);
    await db.orgInsert('analytics_events', {
      event_type: event.type,
      entity_type: event.entityType,
      entity_id: event.entityId,
      user_id: event.userId,
      data: event.data,
    });

    // Publish to Redis for real-time distribution
    await redis.publish('apix:events', JSON.stringify(event));
  }

  // Broadcast event to connected clients
  private broadcastEvent(event: APIXEvent): void {
    // Send to organization room
    this.io.to(`org:${event.organizationId}`).emit('event', event);
    
    // Send to specific event type subscribers
    this.io.to(`event:${event.type}`).emit('event', event);

    // Send to specific user if specified
    if (event.userId) {
      this.io.to(`user:${event.userId}`).emit('event', event);
    }

    // Execute registered event handlers
    const handlers = this.eventHandlers.get(event.type) || [];
    handlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error(`Error in event handler for ${event.type}:`, error);
      }
    });
  }

  // Register event handler
  onEvent(eventType: string, handler: (event: APIXEvent) => void): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  // Agent execution handler
  private async handleAgentExecution(socket: any, data: any): Promise<void> {
    const { agentId, input, sessionId } = data;
    const { organizationId, userId } = socket.data;

    // Emit execution started event
    await this.publishEvent({
      type: 'agent:execution:started',
      entityType: 'agent',
      entityId: agentId,
      organizationId,
      userId,
      data: { input, sessionId }
    });

    // Stream execution progress
    socket.emit('agent:execution:progress', {
      agentId,
      sessionId,
      status: 'processing',
      message: 'Agent execution started...'
    });

    // Here you would integrate with your agent execution engine
    // For now, simulating streaming response
    const executionId = `exec_${Date.now()}`;
    
    // Simulate streaming chunks
    setTimeout(() => {
      socket.emit('agent:execution:chunk', {
        executionId,
        chunk: 'Processing your request...',
        type: 'thinking'
      });
    }, 500);

    setTimeout(() => {
      socket.emit('agent:execution:chunk', {
        executionId,
        chunk: 'Analyzing input and context...',
        type: 'thinking'
      });
    }, 1000);

    setTimeout(async () => {
      socket.emit('agent:execution:completed', {
        executionId,
        result: 'Agent execution completed successfully',
        usage: { tokens: 150, cost: 0.003 }
      });

      // Emit completion event
      await this.publishEvent({
        type: 'agent:execution:completed',
        entityType: 'agent',
        entityId: agentId,
        organizationId,
        userId,
        data: { executionId, sessionId, usage: { tokens: 150, cost: 0.003 } }
      });
    }, 2000);
  }

  // Tool execution handler
  private async handleToolExecution(socket: any, data: any): Promise<void> {
    const { toolId, parameters, sessionId } = data;
    const { organizationId, userId } = socket.data;

    await this.publishEvent({
      type: 'tool:execution:started',
      entityType: 'tool',
      entityId: toolId,
      organizationId,
      userId,
      data: { parameters, sessionId }
    });

    socket.emit('tool:execution:progress', {
      toolId,
      sessionId,
      status: 'executing',
      message: 'Tool execution in progress...'
    });

    // Simulate tool execution
    setTimeout(async () => {
      const result = {
        success: true,
        data: { message: 'Tool executed successfully' },
        executionTime: 1.2
      };

      socket.emit('tool:execution:completed', {
        toolId,
        sessionId,
        result
      });

      await this.publishEvent({
        type: 'tool:execution:completed',
        entityType: 'tool',
        entityId: toolId,
        organizationId,
        userId,
        data: { result, sessionId }
      });
    }, 1500);
  }

  // Hybrid workflow execution handler
  private async handleHybridExecution(socket: any, data: any): Promise<void> {
    const { hybridId, input, sessionId } = data;
    const { organizationId, userId } = socket.data;

    await this.publishEvent({
      type: 'hybrid:execution:started',
      entityType: 'hybrid',
      entityId: hybridId,
      organizationId,
      userId,
      data: { input, sessionId }
    });

    // Simulate hybrid workflow steps
    const steps = [
      'Initializing workflow...',
      'Executing agent reasoning...',
      'Calling external tool...',
      'Processing tool results...',
      'Generating final response...'
    ];

    for (let i = 0; i < steps.length; i++) {
      setTimeout(() => {
        socket.emit('hybrid:execution:step', {
          hybridId,
          sessionId,
          step: i + 1,
          totalSteps: steps.length,
          message: steps[i]
        });
      }, (i + 1) * 800);
    }

    setTimeout(async () => {
      socket.emit('hybrid:execution:completed', {
        hybridId,
        sessionId,
        result: 'Hybrid workflow completed successfully',
        usage: { tokens: 300, toolCalls: 2, cost: 0.008 }
      });

      await this.publishEvent({
        type: 'hybrid:execution:completed',
        entityType: 'hybrid',
        entityId: hybridId,
        organizationId,
        userId,
        data: { sessionId, usage: { tokens: 300, toolCalls: 2, cost: 0.008 } }
      });
    }, steps.length * 800 + 500);
  }

  // Get event history for replay
  async getEventHistory(
    organizationId: string,
    eventTypes?: string[],
    limit: number = 100,
    offset: number = 0
  ): Promise<APIXEvent[]> {
    const db = new Database(organizationId);
    
    let whereClause = '';
    const params: any[] = [];
    
    if (eventTypes && eventTypes.length > 0) {
      whereClause = 'AND event_type = ANY($1)';
      params.push(eventTypes);
    }

    const events = await db.orgQuery(
      'analytics_events',
      `${whereClause} ORDER BY timestamp DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`,
      [...params, limit, offset]
    );

    return events.map(event => ({
      type: event.event_type,
      entityType: event.entity_type,
      entityId: event.entity_id,
      organizationId: event.organization_id,
      userId: event.user_id,
      data: event.data,
      timestamp: event.timestamp
    }));
  }
}

// Singleton instance
let apixEngine: APIXEngine | null = null;

export function initializeAPIX(server: HTTPServer): APIXEngine {
  if (!apixEngine) {
    apixEngine = new APIXEngine(server);
  }
  return apixEngine;
}

export function getAPIX(): APIXEngine {
  if (!apixEngine) {
    throw new Error('APIX Engine not initialized');
  }
  return apixEngine;
}