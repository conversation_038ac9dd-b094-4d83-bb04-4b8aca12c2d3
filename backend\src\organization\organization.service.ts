import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class OrganizationService {
  constructor(private prisma: PrismaService) {}

  async create(data: {
    name: string;
    slug: string;
    adminEmail: string;
    settings: any;
  }) {
    const organization = await this.prisma.organization.create({
      data: {
        name: data.name,
        slug: data.slug,
        settings: data.settings,
        branding: {},
        features: ['agents', 'workflows', 'tools', 'providers'],
        quotas: {
          agents: 50,
          workflows: 25,
          tools: 100,
          apiCalls: 10000,
          storage: '10GB',
        },
        subscription: 'FREE',
      },
    });

    return organization;
  }

  async findBySlug(slug: string) {
    return this.prisma.organization.findUnique({
      where: { slug },
      include: {
        users: {
          include: {
            roles: {
              include: { role: true },
            },
          },
        },
      },
    });
  }

  async findById(id: string) {
    return this.prisma.organization.findUnique({
      where: { id },
      include: {
        users: true,
        agents: true,
        workflows: true,
        tools: true,
        providers: true,
      },
    });
  }

  async updateSettings(id: string, settings: any) {
    return this.prisma.organization.update({
      where: { id },
      data: { settings },
    });
  }

  async updateBranding(id: string, branding: any) {
    return this.prisma.organization.update({
      where: { id },
      data: { branding },
    });
  }

  async getUsageStats(organizationId: string) {
    const [agents, workflows, tools, executions] = await Promise.all([
      this.prisma.agent.count({ where: { organizationId } }),
      this.prisma.workflow.count({ where: { organizationId } }),
      this.prisma.tool.count({ where: { organizationId } }),
      this.prisma.workflowExecution.count({
        where: { workflow: { organizationId } },
      }),
    ]);

    return {
      agents,
      workflows,
      tools,
      executions,
      timestamp: new Date(),
    };
  }

  async checkQuotas(organizationId: string, resource: string) {
    const org = await this.findById(organizationId);
    if (!org) return false;

    const quotas = org.quotas as any;
    const usage = await this.getUsageStats(organizationId);

    switch (resource) {
      case 'agents':
        return usage.agents < quotas.agents;
      case 'workflows':
        return usage.workflows < quotas.workflows;
      case 'tools':
        return usage.tools < quotas.tools;
      default:
        return true;
    }
  }
}