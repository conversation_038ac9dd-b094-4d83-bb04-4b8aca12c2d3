import { Database, QuotaManager } from '../database';
import { getAPIX } from '../apix';

export interface AIProvider {
  id: string;
  name: string;
  type: 'openai' | 'claude' | 'gemini' | 'mistral' | 'groq' | 'custom';
  config: Record<string, any>;
  credentials: Record<string, any>;
  is_active: boolean;
  priority: number;
  cost_per_token: number;
  capabilities: string[];
  performance_metrics: {
    avg_latency: number;
    success_rate: number;
    uptime: number;
  };
}

export interface ProviderSelection {
  provider: AIProvider;
  reason: string;
  score: number;
}

export class ProviderManager {
  private db: Database;
  private quotaManager: QuotaManager;
  private organizationId: string;

  constructor(organizationId: string) {
    this.organizationId = organizationId;
    this.db = new Database(organizationId);
    this.quotaManager = new QuotaManager(organizationId);
  }

  // Add new AI provider
  async addProvider(
    name: string,
    type: string,
    config: Record<string, any>,
    credentials: Record<string, any>,
    costPerToken: number = 0,
    userId: string
  ): Promise<AIProvider> {
    // Encrypt credentials before storing
    const encryptedCredentials = this.encryptCredentials(credentials);

    const provider = await this.db.orgInsert('providers', {
      name,
      type,
      config,
      credentials: encryptedCredentials,
      cost_per_token: costPerToken,
      is_active: true,
      priority: 0,
    });

    // Emit provider created event
    const apix = getAPIX();
    await apix.publishEvent({
      type: 'provider:created',
      entityType: 'provider',
      entityId: provider.id,
      organizationId: this.organizationId,
      userId,
      data: { name, type }
    });

    return this.formatProvider(provider);
  }

  // Get all active providers
  async getActiveProviders(): Promise<AIProvider[]> {
    const providers = await this.db.orgQuery(
      'providers',
      'is_active = true ORDER BY priority DESC, cost_per_token ASC'
    );

    return providers.map(p => this.formatProvider(p));
  }

  // Smart provider selection algorithm
  async selectProvider(
    requirements: {
      model?: string;
      maxTokens?: number;
      capabilities?: string[];
      maxCost?: number;
      preferredLatency?: number;
    } = {}
  ): Promise<ProviderSelection> {
    const providers = await this.getActiveProviders();
    
    if (providers.length === 0) {
      throw new Error('No active providers available');
    }

    // Score each provider based on multiple factors
    const scoredProviders = providers.map(provider => {
      let score = 0;
      let reasons: string[] = [];

      // Cost factor (lower cost = higher score)
      if (requirements.maxCost) {
        if (provider.cost_per_token <= requirements.maxCost) {
          score += 30;
          reasons.push('within budget');
        } else {
          score -= 50;
          reasons.push('exceeds budget');
        }
      } else {
        score += (1 / (provider.cost_per_token + 0.001)) * 10; // Favor lower cost
      }

      // Performance factor
      score += provider.performance_metrics.success_rate * 20;
      score += (1 / (provider.performance_metrics.avg_latency + 0.1)) * 15;
      score += provider.performance_metrics.uptime * 10;

      // Capability matching
      if (requirements.capabilities) {
        const matchedCapabilities = requirements.capabilities.filter(cap =>
          provider.capabilities.includes(cap)
        );
        score += (matchedCapabilities.length / requirements.capabilities.length) * 25;
        
        if (matchedCapabilities.length === requirements.capabilities.length) {
          reasons.push('all capabilities supported');
        }
      }

      // Priority boost
      score += provider.priority * 5;

      return {
        provider,
        score,
        reason: reasons.join(', ') || 'best available option'
      };
    });

    // Sort by score and return the best
    scoredProviders.sort((a, b) => b.score - a.score);
    
    const selected = scoredProviders[0];
    
    // Emit provider selection event
    const apix = getAPIX();
    await apix.publishEvent({
      type: 'provider:selected',
      entityType: 'provider',
      entityId: selected.provider.id,
      organizationId: this.organizationId,
      data: { 
        reason: selected.reason,
        score: selected.score,
        requirements
      }
    });

    return selected;
  }

  // Execute AI completion with automatic provider selection and failover
  async executeCompletion(
    prompt: string,
    options: {
      model?: string;
      maxTokens?: number;
      temperature?: number;
      stream?: boolean;
      requirements?: any;
    } = {}
  ): Promise<any> {
    const selection = await this.selectProvider(options.requirements);
    let currentProvider = selection.provider;
    let attempts = 0;
    const maxAttempts = 3;

    while (attempts < maxAttempts) {
      try {
        // Check quota before execution
        await this.quotaManager.checkQuota('ai_tokens', options.maxTokens || 1000);

        const result = await this.callProvider(currentProvider, prompt, options);
        
        // Track usage
        await this.quotaManager.trackUsage(
          'ai_tokens',
          currentProvider.id,
          result.usage.total_tokens,
          result.usage.total_tokens * currentProvider.cost_per_token,
          {
            provider: currentProvider.name,
            model: options.model || 'default',
            prompt_tokens: result.usage.prompt_tokens,
            completion_tokens: result.usage.completion_tokens
          }
        );

        // Update provider performance metrics
        await this.updateProviderMetrics(currentProvider.id, true, result.latency);

        // Emit completion event
        const apix = getAPIX();
        await apix.publishEvent({
          type: 'provider:completion:success',
          entityType: 'provider',
          entityId: currentProvider.id,
          organizationId: this.organizationId,
          data: {
            usage: result.usage,
            latency: result.latency,
            model: options.model
          }
        });

        return result;

      } catch (error) {
        attempts++;
        
        // Update provider performance metrics
        await this.updateProviderMetrics(currentProvider.id, false);

        // Emit failure event
        const apix = getAPIX();
        await apix.publishEvent({
          type: 'provider:completion:failed',
          entityType: 'provider',
          entityId: currentProvider.id,
          organizationId: this.organizationId,
          data: {
            error: error.message,
            attempt: attempts
          }
        });

        if (attempts >= maxAttempts) {
          throw new Error(`All provider attempts failed. Last error: ${error.message}`);
        }

        // Try next best provider
        const providers = await this.getActiveProviders();
        const remainingProviders = providers.filter(p => p.id !== currentProvider.id);
        
        if (remainingProviders.length === 0) {
          throw new Error('No fallback providers available');
        }

        currentProvider = remainingProviders[0]; // Simple fallback, could be smarter
        
        // Emit failover event
        await apix.publishEvent({
          type: 'provider:failover',
          entityType: 'provider',
          entityId: currentProvider.id,
          organizationId: this.organizationId,
          data: {
            from_provider: selection.provider.id,
            to_provider: currentProvider.id,
            attempt: attempts
          }
        });
      }
    }
  }

  // Call specific provider
  private async callProvider(
    provider: AIProvider,
    prompt: string,
    options: any
  ): Promise<any> {
    const startTime = Date.now();
    
    try {
      let result;
      
      switch (provider.type) {
        case 'openai':
          result = await this.callOpenAI(provider, prompt, options);
          break;
        case 'claude':
          result = await this.callClaude(provider, prompt, options);
          break;
        case 'gemini':
          result = await this.callGemini(provider, prompt, options);
          break;
        case 'mistral':
          result = await this.callMistral(provider, prompt, options);
          break;
        case 'groq':
          result = await this.callGroq(provider, prompt, options);
          break;
        default:
          throw new Error(`Unsupported provider type: ${provider.type}`);
      }

      const latency = Date.now() - startTime;
      return { ...result, latency };

    } catch (error) {
      const latency = Date.now() - startTime;
      throw { ...error, latency };
    }
  }

  // OpenAI integration
  private async callOpenAI(provider: AIProvider, prompt: string, options: any): Promise<any> {
    const credentials = this.decryptCredentials(provider.credentials);
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${credentials.api_key}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: options.model || 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: options.maxTokens || 1000,
        temperature: options.temperature || 0.7,
        stream: options.stream || false,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      content: data.choices[0].message.content,
      usage: data.usage,
      model: data.model,
      provider: 'openai'
    };
  }

  // Claude integration
  private async callClaude(provider: AIProvider, prompt: string, options: any): Promise<any> {
    const credentials = this.decryptCredentials(provider.credentials);
    
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': credentials.api_key,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify({
        model: options.model || 'claude-3-sonnet-20240229',
        max_tokens: options.maxTokens || 1000,
        messages: [{ role: 'user', content: prompt }],
        temperature: options.temperature || 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`Claude API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      content: data.content[0].text,
      usage: data.usage,
      model: data.model,
      provider: 'claude'
    };
  }

  // Placeholder implementations for other providers
  private async callGemini(provider: AIProvider, prompt: string, options: any): Promise<any> {
    // Implementation for Google Gemini
    throw new Error('Gemini integration not implemented yet');
  }

  private async callMistral(provider: AIProvider, prompt: string, options: any): Promise<any> {
    // Implementation for Mistral
    throw new Error('Mistral integration not implemented yet');
  }

  private async callGroq(provider: AIProvider, prompt: string, options: any): Promise<any> {
    // Implementation for Groq
    throw new Error('Groq integration not implemented yet');
  }

  // Update provider performance metrics
  private async updateProviderMetrics(
    providerId: string,
    success: boolean,
    latency?: number
  ): Promise<void> {
    // This would typically update running averages in the database
    // For now, just a placeholder
    console.log(`Updating metrics for provider ${providerId}: success=${success}, latency=${latency}`);
  }

  // Encrypt credentials (simplified - use proper encryption in production)
  private encryptCredentials(credentials: Record<string, any>): Record<string, any> {
    // In production, use proper encryption like AES-256
    return credentials; // Placeholder
  }

  // Decrypt credentials
  private decryptCredentials(encryptedCredentials: Record<string, any>): Record<string, any> {
    // In production, decrypt the credentials
    return encryptedCredentials; // Placeholder
  }

  // Format provider for API response
  private formatProvider(provider: any): AIProvider {
    return {
      id: provider.id,
      name: provider.name,
      type: provider.type,
      config: provider.config,
      credentials: {}, // Never expose credentials
      is_active: provider.is_active,
      priority: provider.priority,
      cost_per_token: provider.cost_per_token,
      capabilities: provider.config.capabilities || [],
      performance_metrics: provider.config.performance_metrics || {
        avg_latency: 0,
        success_rate: 1.0,
        uptime: 1.0
      }
    };
  }

  // Get provider analytics
  async getProviderAnalytics(timeRange: string = '7d'): Promise<any> {
    const analytics = await this.db.query(
      `SELECT 
         provider_id,
         COUNT(*) as total_requests,
         AVG(CAST(data->>'latency' AS FLOAT)) as avg_latency,
         SUM(CASE WHEN event_type LIKE '%success%' THEN 1 ELSE 0 END) as successful_requests,
         SUM(CAST(data->>'usage'->>'total_tokens' AS INTEGER)) as total_tokens,
         SUM(CAST(data->>'cost' AS FLOAT)) as total_cost
       FROM analytics_events 
       WHERE organization_id = $1 
         AND event_type LIKE 'provider:%'
         AND timestamp > NOW() - INTERVAL '${timeRange}'
       GROUP BY provider_id`,
      [this.organizationId]
    );

    return analytics;
  }
}