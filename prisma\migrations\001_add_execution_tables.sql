-- Add agent executions table
CREATE TABLE IF NOT EXISTS agent_executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id VARCHAR NOT NULL,
  organization_id VARCHAR NOT NULL,
  user_id VARCHAR,
  session_id VARCHAR,
  input TEXT NOT NULL,
  output TEXT,
  error TEXT,
  token_usage JSONB,
  cost DECIMAL(10,6) DEFAULT 0,
  execution_time INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP
);

-- Add tool executions table
CREATE TABLE IF NOT EXISTS tool_executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tool_id VARCHAR NOT NULL,
  organization_id VARCHAR NOT NULL,
  user_id VARCHAR,
  session_id VARCHAR,
  input JSONB NOT NULL,
  output JSONB,
  error TEXT,
  execution_time INTEGER DEFAULT 0,
  cost DECIMAL(10,6) DEFAULT 0,
  status VARCHAR(20) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP
);

-- Add billing usage table
CREATE TABLE IF NOT EXISTS billing_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id VARCHAR NOT NULL,
  resource_type VARCHAR(50) NOT NULL,
  resource_id VARCHAR,
  quantity INTEGER DEFAULT 1,
  cost DECIMAL(10,6) DEFAULT 0,
  metadata JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Add quotas table
CREATE TABLE IF NOT EXISTS quotas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id VARCHAR NOT NULL,
  resource_type VARCHAR(50) NOT NULL,
  limit_value INTEGER NOT NULL,
  current_usage INTEGER DEFAULT 0,
  reset_period VARCHAR(20) DEFAULT 'monthly',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(organization_id, resource_type)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_agent_executions_org_agent ON agent_executions(organization_id, agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_executions_created ON agent_executions(created_at);
CREATE INDEX IF NOT EXISTS idx_tool_executions_org_tool ON tool_executions(organization_id, tool_id);
CREATE INDEX IF NOT EXISTS idx_tool_executions_created ON tool_executions(created_at);
CREATE INDEX IF NOT EXISTS idx_billing_usage_org_type ON billing_usage(organization_id, resource_type);
CREATE INDEX IF NOT EXISTS idx_billing_usage_created ON billing_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_quotas_org_type ON quotas(organization_id, resource_type);