import { prisma } from '@/lib/prisma'
import { RoleLevel } from '@prisma/client'

export interface Permission {
  resource: string
  action: string
  scope?: Record<string, any>
}

export class RBACService {
  // System permissions
  static readonly PERMISSIONS = {
    // Organization management
    'organization:read': 'Read organization details',
    'organization:update': 'Update organization settings',
    'organization:delete': 'Delete organization',
    
    // User management
    'users:read': 'View users',
    'users:create': 'Create users',
    'users:update': 'Update users',
    'users:delete': 'Delete users',
    'users:invite': 'Invite users',
    
    // Role management
    'roles:read': 'View roles',
    'roles:create': 'Create roles',
    'roles:update': 'Update roles',
    'roles:delete': 'Delete roles',
    'roles:assign': 'Assign roles to users',
    
    // Agent management
    'agents:read': 'View agents',
    'agents:create': 'Create agents',
    'agents:update': 'Update agents',
    'agents:delete': 'Delete agents',
    'agents:execute': 'Execute agents',
    
    // Tool management
    'tools:read': 'View tools',
    'tools:create': 'Create tools',
    'tools:update': 'Update tools',
    'tools:delete': 'Delete tools',
    'tools:execute': 'Execute tools',
    
    // Workflow management
    'workflows:read': 'View workflows',
    'workflows:create': 'Create workflows',
    'workflows:update': 'Update workflows',
    'workflows:delete': 'Delete workflows',
    'workflows:execute': 'Execute workflows',
    
    // Analytics and audit
    'analytics:read': 'View analytics',
    'audit:read': 'View audit logs',
    
    // API keys
    'apikeys:read': 'View API keys',
    'apikeys:create': 'Create API keys',
    'apikeys:update': 'Update API keys',
    'apikeys:delete': 'Delete API keys',
    
    // System administration
    'system:admin': 'System administration'
  } as const

  // Default role permissions
  static readonly DEFAULT_ROLE_PERMISSIONS = {
    [RoleLevel.SUPER_ADMIN]: Object.keys(this.PERMISSIONS),
    [RoleLevel.ORG_ADMIN]: [
      'organization:read',
      'organization:update',
      'users:read',
      'users:create',
      'users:update',
      'users:delete',
      'users:invite',
      'roles:read',
      'roles:create',
      'roles:update',
      'roles:assign',
      'agents:read',
      'agents:create',
      'agents:update',
      'agents:delete',
      'agents:execute',
      'tools:read',
      'tools:create',
      'tools:update',
      'tools:delete',
      'tools:execute',
      'workflows:read',
      'workflows:create',
      'workflows:update',
      'workflows:delete',
      'workflows:execute',
      'analytics:read',
      'audit:read',
      'apikeys:read',
      'apikeys:create',
      'apikeys:update',
      'apikeys:delete'
    ],
    [RoleLevel.DEVELOPER]: [
      'organization:read',
      'users:read',
      'roles:read',
      'agents:read',
      'agents:create',
      'agents:update',
      'agents:execute',
      'tools:read',
      'tools:create',
      'tools:update',
      'tools:execute',
      'workflows:read',
      'workflows:create',
      'workflows:update',
      'workflows:execute',
      'analytics:read'
    ],
    [RoleLevel.VIEWER]: [
      'organization:read',
      'users:read',
      'roles:read',
      'agents:read',
      'tools:read',
      'workflows:read',
      'analytics:read'
    ]
  }

  static async initializeSystemRoles() {
    const roles = [
      {
        name: 'Super Admin',
        level: RoleLevel.SUPER_ADMIN,
        permissions: this.DEFAULT_ROLE_PERMISSIONS[RoleLevel.SUPER_ADMIN],
        description: 'Full system access across all organizations',
        isSystem: true
      },
      {
        name: 'Organization Admin',
        level: RoleLevel.ORG_ADMIN,
        permissions: this.DEFAULT_ROLE_PERMISSIONS[RoleLevel.ORG_ADMIN],
        description: 'Full access within organization',
        isSystem: true
      },
      {
        name: 'Developer',
        level: RoleLevel.DEVELOPER,
        permissions: this.DEFAULT_ROLE_PERMISSIONS[RoleLevel.DEVELOPER],
        description: 'Can create and manage agents, tools, and workflows',
        isSystem: true
      },
      {
        name: 'Viewer',
        level: RoleLevel.VIEWER,
        permissions: this.DEFAULT_ROLE_PERMISSIONS[RoleLevel.VIEWER],
        description: 'Read-only access to resources',
        isSystem: true
      }
    ]

    for (const role of roles) {
      await prisma.role.upsert({
        where: { name_level: { name: role.name, level: role.level } },
        update: { permissions: role.permissions },
        create: role
      })
    }
  }

  static async checkPermission(
    userId: string,
    resource: string,
    action: string,
    scope?: Record<string, any>
  ): Promise<boolean> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: {
          where: { isActive: true },
          include: {
            role: {
              where: { isActive: true }
            }
          }
        }
      }
    })

    if (!user || !user.isActive) {
      return false
    }

    const permission = `${resource}:${action}`
    
    // Check if user has the required permission through any of their roles
    for (const userRole of user.userRoles) {
      if (userRole.role.permissions.includes(permission)) {
        // If scope is provided, check scope constraints
        if (scope && userRole.scope) {
          return this.checkScopeConstraints(scope, userRole.scope)
        }
        return true
      }
      
      // Super admin has all permissions
      if (userRole.role.level === RoleLevel.SUPER_ADMIN) {
        return true
      }
    }

    return false
  }

  static async getUserPermissions(userId: string): Promise<string[]> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: {
          where: { isActive: true },
          include: {
            role: {
              where: { isActive: true }
            }
          }
        }
      }
    })

    if (!user) {
      return []
    }

    const permissions = new Set<string>()
    
    for (const userRole of user.userRoles) {
      // Super admin gets all permissions
      if (userRole.role.level === RoleLevel.SUPER_ADMIN) {
        return Object.keys(this.PERMISSIONS)
      }
      
      userRole.role.permissions.forEach(permission => permissions.add(permission))
    }

    return Array.from(permissions)
  }

  static async assignRoleToUser(userId: string, roleId: string, scope?: Record<string, any>) {
    return prisma.userRole.create({
      data: {
        userId,
        roleId,
        scope: scope || {}
      }
    })
  }

  static async removeRoleFromUser(userId: string, roleId: string) {
    return prisma.userRole.updateMany({
      where: { userId, roleId },
      data: { isActive: false }
    })
  }

  static async updateUserRoles(userId: string, roleIds: string[], scope?: Record<string, any>) {
    // Deactivate all current roles
    await prisma.userRole.updateMany({
      where: { userId },
      data: { isActive: false }
    })

    // Assign new roles
    const userRoles = roleIds.map(roleId => ({
      userId,
      roleId,
      scope: scope || {},
      isActive: true
    }))

    await prisma.userRole.createMany({
      data: userRoles,
      skipDuplicates: true
    })
  }

  static async createCustomRole(
    name: string,
    level: RoleLevel,
    permissions: string[],
    description?: string
  ) {
    return prisma.role.create({
      data: {
        name,
        level,
        permissions,
        description,
        isSystem: false
      }
    })
  }

  static async updateRole(roleId: string, updates: {
    name?: string
    permissions?: string[]
    description?: string
    isActive?: boolean
  }) {
    return prisma.role.update({
      where: { id: roleId },
      data: updates
    })
  }

  static async getRolesByLevel(level: RoleLevel) {
    return prisma.role.findMany({
      where: { level, isActive: true },
      orderBy: { name: 'asc' }
    })
  }

  static async getOrganizationRoles(organizationId: string) {
    // For now, return system roles. In future, can add org-specific roles
    return prisma.role.findMany({
      where: { isActive: true },
      orderBy: [{ level: 'asc' }, { name: 'asc' }]
    })
  }

  private static checkScopeConstraints(
    requestScope: Record<string, any>,
    roleScope: Record<string, any>
  ): boolean {
    // Simple scope checking - can be extended for complex scenarios
    for (const [key, value] of Object.entries(requestScope)) {
      if (roleScope[key] && roleScope[key] !== value) {
        return false
      }
    }
    return true
  }
}