import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

export interface AnalyticsData {
  totalAgents: number;
  totalExecutions: number;
  totalCost: number;
  successRate: number;
  avgExecutionTime: number;
  dailyStats: Array<{
    date: string;
    executions: number;
    cost: number;
  }>;
  topAgents: Array<{
    id: string;
    name: string;
    executions: number;
    cost: number;
  }>;
}

@Injectable()
export class AnalyticsService {
  constructor(private prisma: PrismaService) {}

  async getOrganizationAnalytics(
    organizationId: string,
    timeRange: { start: Date; end: Date },
  ): Promise<AnalyticsData> {
    const [totalStats, dailyStats, topAgents] = await Promise.all([
      this.getTotalStats(organizationId, timeRange),
      this.getDailyStats(organizationId, timeRange),
      this.getTopAgents(organizationId, timeRange),
    ]);

    return {
      ...totalStats,
      dailyStats,
      topAgents,
    };
  }

  private async getTotalStats(organizationId: string, timeRange: { start: Date; end: Date }) {
    const [agentStats, executionStats] = await Promise.all([
      this.prisma.agent.count({
        where: { organizationId, isActive: true },
      }),
      this.prisma.$queryRaw`
        SELECT 
          COUNT(*) as total_executions,
          AVG(CASE WHEN status = 'success' THEN 1.0 ELSE 0.0 END) as success_rate,
          AVG(execution_time) as avg_execution_time,
          SUM(cost) as total_cost
        FROM agent_executions 
        WHERE organization_id = ${organizationId}
          AND created_at >= ${timeRange.start}
          AND created_at <= ${timeRange.end}
      `,
    ]);

    const stats = executionStats[0] || {
      total_executions: 0,
      success_rate: 0,
      avg_execution_time: 0,
      total_cost: 0,
    };

    return {
      totalAgents: agentStats,
      totalExecutions: Number(stats.total_executions),
      successRate: Number(stats.success_rate),
      avgExecutionTime: Number(stats.avg_execution_time),
      totalCost: Number(stats.total_cost),
    };
  }

  private async getDailyStats(organizationId: string, timeRange: { start: Date; end: Date }) {
    const result = await this.prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as executions,
        SUM(cost) as cost
      FROM agent_executions 
      WHERE organization_id = ${organizationId}
        AND created_at >= ${timeRange.start}
        AND created_at <= ${timeRange.end}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `;

    return result.map((row: any) => ({
      date: row.date,
      executions: Number(row.executions),
      cost: Number(row.cost),
    }));
  }

  private async getTopAgents(organizationId: string, timeRange: { start: Date; end: Date }) {
    const result = await this.prisma.$queryRaw`
      SELECT 
        a.id,
        a.name,
        COUNT(ae.id) as executions,
        SUM(ae.cost) as cost
      FROM agents a
      LEFT JOIN agent_executions ae ON a.id = ae.agent_id
        AND ae.created_at >= ${timeRange.start}
        AND ae.created_at <= ${timeRange.end}
      WHERE a.organization_id = ${organizationId}
      GROUP BY a.id, a.name
      ORDER BY executions DESC
      LIMIT 10
    `;

    return result.map((row: any) => ({
      id: row.id,
      name: row.name,
      executions: Number(row.executions),
      cost: Number(row.cost),
    }));
  }

  async getUsageQuotas(organizationId: string) {
    const quotas = await this.prisma.$queryRaw`
      SELECT 
        resource_type,
        limit_value,
        current_usage,
        (current_usage::float / limit_value::float * 100) as usage_percentage
      FROM quotas 
      WHERE organization_id = ${organizationId}
    `;

    return quotas.map((quota: any) => ({
      resourceType: quota.resource_type,
      limit: Number(quota.limit_value),
      used: Number(quota.current_usage),
      percentage: Number(quota.usage_percentage),
    }));
  }
}