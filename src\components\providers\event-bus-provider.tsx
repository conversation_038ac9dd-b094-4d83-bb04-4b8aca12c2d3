"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { eventBus, EventBusMessage, EventChannel, EventType } from '@/lib/event-bus';

interface EventBusContextType {
  isConnected: boolean;
  latency: number;
  subscribe: (channel: EventChannel, handler: (message: EventBusMessage) => void) => () => void;
  publish: (channel: EventChannel, type: EventType, data: any, options?: any) => void;
  joinRoom: (roomId: string) => void;
  leaveRoom: (roomId: string) => void;
  getEventHistory: (channel?: EventChannel, limit?: number) => EventBusMessage[];
}

const EventBusContext = createContext<EventBusContextType | null>(null);

export function EventBusProvider({ children }: { children: React.ReactNode }) {
  const [isConnected, setIsConnected] = useState(false);
  const [latency, setLatency] = useState(0);

  useEffect(() => {
    const initializeEventBus = async () => {
      try {
        await eventBus.connect();
        setIsConnected(true);
        
        // Update latency periodically
        const latencyInterval = setInterval(() => {
          setLatency(eventBus.getLatencyScore());
        }, 5000);

        return () => {
          clearInterval(latencyInterval);
          eventBus.disconnect();
        };
      } catch (error) {
        console.error('Failed to connect to event bus:', error);
        setIsConnected(false);
      }
    };

    initializeEventBus();
  }, []);

  const contextValue: EventBusContextType = {
    isConnected,
    latency,
    subscribe: eventBus.subscribe.bind(eventBus),
    publish: eventBus.publish.bind(eventBus),
    joinRoom: eventBus.joinRoom.bind(eventBus),
    leaveRoom: eventBus.leaveRoom.bind(eventBus),
    getEventHistory: eventBus.getEventHistory.bind(eventBus)
  };

  return (
    <EventBusContext.Provider value={contextValue}>
      {children}
    </EventBusContext.Provider>
  );
}

export function useEventBus() {
  const context = useContext(EventBusContext);
  if (!context) {
    throw new Error('useEventBus must be used within an EventBusProvider');
  }
  return context;
}