import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateAgentDto, UpdateAgentDto } from './dto/agent.dto';

@Injectable()
export class AgentService {
  constructor(private prisma: PrismaService) {}

  async create(organizationId: string, dto: CreateAgentDto) {
    return this.prisma.agent.create({
      data: {
        ...dto,
        organizationId,
        memory: dto.memory || {},
        settings: dto.settings || {},
      },
    });
  }

  async findAll(organizationId: string) {
    return this.prisma.agent.findMany({
      where: { organizationId },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: string, organizationId: string) {
    return this.prisma.agent.findFirst({
      where: { id, organizationId },
    });
  }

  async update(id: string, organizationId: string, dto: UpdateAgentDto) {
    return this.prisma.agent.update({
      where: { id },
      data: {
        ...dto,
        updatedAt: new Date(),
      },
    });
  }

  async remove(id: string, organizationId: string) {
    return this.prisma.agent.delete({
      where: { id },
    });
  }

  async chat(agentId: string, organizationId: string, message: string, sessionId?: string, onStream?: (chunk: string) => void) {
    const agent = await this.findOne(agentId, organizationId);
    if (!agent) {
      throw new Error('Agent not found');
    }

    if (!agent.isActive) {
      throw new Error('Agent is not active');
    }

    const startTime = Date.now();
    
    try {
      const aiResponse = await this.processMessage(agent, message, onStream);
      const executionTime = Date.now() - startTime;

      // Store execution record
      await this.prisma.$executeRaw`
        INSERT INTO agent_executions (
          id, agent_id, organization_id, input, output, 
          token_usage, cost, execution_time, status, created_at
        ) VALUES (
          gen_random_uuid(), ${agentId}, ${organizationId}, ${message}, ${aiResponse.content},
          ${JSON.stringify(aiResponse.usage)}::jsonb, ${aiResponse.cost}, ${executionTime}, 'success', NOW()
        )
      `;

      return {
        agentId,
        message,
        response: aiResponse.content,
        usage: aiResponse.usage,
        cost: aiResponse.cost,
        executionTime,
        timestamp: new Date(),
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      await this.prisma.$executeRaw`
        INSERT INTO agent_executions (
          id, agent_id, organization_id, input, error, 
          execution_time, status, created_at
        ) VALUES (
          gen_random_uuid(), ${agentId}, ${organizationId}, ${message}, ${error.message},
          ${executionTime}, 'error', NOW()
        )
      `;

      throw error;
    }
  }

  private async processMessage(agent: any, message: string, onStream?: (chunk: string) => void) {
    const { aiProvider } = await import('../../lib/providers/ai-provider');
    
    const messages = [
      { role: 'system', content: agent.systemPrompt },
      { role: 'user', content: message },
    ];

    const providerConfig = {
      primary: agent.settings?.provider || 'openai',
      fallbacks: ['anthropic'],
      maxRetries: 2,
      timeout: 30000,
    };

    if (onStream) {
      return await aiProvider.streamResponse(messages, agent.model, onStream);
    } else {
      return await aiProvider.generateResponse(messages, agent.model, providerConfig);
    }
  }

  async getAnalytics(agentId: string, organizationId: string, timeRange: { start: Date; end: Date }) {
    const result = await this.prisma.$queryRaw`
      SELECT 
        COUNT(*) as total_executions,
        AVG(CASE WHEN status = 'success' THEN 1.0 ELSE 0.0 END) as success_rate,
        AVG(execution_time) as avg_execution_time,
        SUM(cost) as total_cost
      FROM agent_executions 
      WHERE organization_id = ${organizationId}
        AND agent_id = ${agentId}
        AND created_at >= ${timeRange.start}
        AND created_at <= ${timeRange.end}
    `;

    const summary = result[0] || {
      total_executions: 0,
      success_rate: 0,
      avg_execution_time: 0,
      total_cost: 0,
    };

    return {
      totalExecutions: Number(summary.total_executions),
      successRate: Number(summary.success_rate),
      avgExecutionTime: Number(summary.avg_execution_time),
      totalCost: Number(summary.total_cost),
    };
  }

  async getExecutionHistory(agentId: string, organizationId: string, limit: number = 50) {
    return this.prisma.$queryRaw`
      SELECT id, input, output, error, status, cost, execution_time, created_at
      FROM agent_executions
      WHERE organization_id = ${organizationId} AND agent_id = ${agentId}
      ORDER BY created_at DESC
      LIMIT ${limit}
    `;
  }
}