import { apiClient, APIResponse } from './base';
import { Tool, ToolV<PERSON><PERSON>, ToolExecutionResult, ToolAuditLog, ToolCategory, ToolExecutionType } from '../types/tool';

export interface CreateToolRequest {
  name: string;
  slug?: string;
  description: string;
  category: ToolCategory;
  executionType: ToolExecutionType;
  isActive: boolean;
}

export interface UpdateToolRequest extends Partial<CreateToolRequest> {}

export interface CreateToolVersionRequest {
  version: string;
  inputSchema: Record<string, any>;
  outputSchema: Record<string, any>;
  handlerUrl?: string;
  internalHandlerRef?: string;
  config?: Record<string, any>;
  isActive: boolean;
}

export interface ExecuteToolRequest {
  input: Record<string, any>;
  versionId?: string;
  sessionId?: string;
  workflowId?: string;
  timeout?: number;
}

export interface ToolMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  totalCost: number;
  lastExecution: string;
  popularInputs: Array<{
    input: Record<string, any>;
    count: number;
  }>;
}

export interface ToolUsageStats {
  daily: Array<{
    date: string;
    executions: number;
    errors: number;
    avgTime: number;
  }>;
  topUsers: Array<{
    userId: string;
    executions: number;
  }>;
  errorBreakdown: Array<{
    error: string;
    count: number;
  }>;
}

class ToolAPI {
  // Tool CRUD operations
  async createTool(data: CreateToolRequest): Promise<APIResponse<Tool>> {
    return apiClient.post('/tools', data);
  }

  async getTool(id: string): Promise<APIResponse<Tool>> {
    return apiClient.get(`/tools/${id}`);
  }

  async updateTool(id: string, data: UpdateToolRequest): Promise<APIResponse<Tool>> {
    return apiClient.patch(`/tools/${id}`, data);
  }

  async deleteTool(id: string): Promise<APIResponse<void>> {
    return apiClient.delete(`/tools/${id}`);
  }

  async listTools(params?: {
    page?: number;
    limit?: number;
    search?: string;
    category?: ToolCategory;
    executionType?: ToolExecutionType;
    isActive?: boolean;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<APIResponse<Tool[]>> {
    return apiClient.get('/tools', { params });
  }

  async duplicateTool(id: string, name?: string): Promise<APIResponse<Tool>> {
    return apiClient.post(`/tools/${id}/duplicate`, { name });
  }

  // Tool version management
  async createToolVersion(toolId: string, data: CreateToolVersionRequest): Promise<APIResponse<ToolVersion>> {
    return apiClient.post(`/tools/${toolId}/versions`, data);
  }

  async getToolVersion(toolId: string, versionId: string): Promise<APIResponse<ToolVersion>> {
    return apiClient.get(`/tools/${toolId}/versions/${versionId}`);
  }

  async updateToolVersion(
    toolId: string, 
    versionId: string, 
    data: Partial<CreateToolVersionRequest>
  ): Promise<APIResponse<ToolVersion>> {
    return apiClient.patch(`/tools/${toolId}/versions/${versionId}`, data);
  }

  async deleteToolVersion(toolId: string, versionId: string): Promise<APIResponse<void>> {
    return apiClient.delete(`/tools/${toolId}/versions/${versionId}`);
  }

  async listToolVersions(toolId: string): Promise<APIResponse<ToolVersion[]>> {
    return apiClient.get(`/tools/${toolId}/versions`);
  }

  async activateToolVersion(toolId: string, versionId: string): Promise<APIResponse<void>> {
    return apiClient.patch(`/tools/${toolId}/versions/${versionId}/activate`);
  }

  async getActiveToolVersion(toolId: string): Promise<APIResponse<ToolVersion>> {
    return apiClient.get(`/tools/${toolId}/versions/active`);
  }

  // Tool execution
  async executeTool(toolId: string, data: ExecuteToolRequest): Promise<APIResponse<ToolExecutionResult>> {
    return apiClient.post(`/tools/${toolId}/execute`, data);
  }

  async executeToolAsync(toolId: string, data: ExecuteToolRequest): Promise<APIResponse<{
    executionId: string;
    status: 'queued' | 'running';
  }>> {
    return apiClient.post(`/tools/${toolId}/execute/async`, data);
  }

  async getExecutionResult(toolId: string, executionId: string): Promise<APIResponse<ToolExecutionResult>> {
    return apiClient.get(`/tools/${toolId}/executions/${executionId}`);
  }

  async cancelExecution(toolId: string, executionId: string): Promise<APIResponse<void>> {
    return apiClient.delete(`/tools/${toolId}/executions/${executionId}`);
  }

  async listExecutions(toolId: string, params?: {
    status?: 'success' | 'error' | 'timeout';
    userId?: string;
    sessionId?: string;
    workflowId?: string;
    page?: number;
    limit?: number;
    since?: string;
  }): Promise<APIResponse<ToolExecutionResult[]>> {
    return apiClient.get(`/tools/${toolId}/executions`, { params });
  }

  // Tool testing and validation
  async testTool(toolId: string, data: {
    input: Record<string, any>;
    versionId?: string;
  }): Promise<APIResponse<ToolExecutionResult>> {
    return apiClient.post(`/tools/${toolId}/test`, data);
  }

  async validateToolSchema(toolId: string, versionId: string, data: {
    inputSchema?: Record<string, any>;
    outputSchema?: Record<string, any>;
  }): Promise<APIResponse<{
    valid: boolean;
    errors?: string[];
    warnings?: string[];
  }>> {
    return apiClient.post(`/tools/${toolId}/versions/${versionId}/validate`, data);
  }

  async validateToolInput(toolId: string, input: Record<string, any>): Promise<APIResponse<{
    valid: boolean;
    errors?: string[];
  }>> {
    return apiClient.post(`/tools/${toolId}/validate-input`, { input });
  }

  // Tool metrics and analytics
  async getToolMetrics(id: string, timeRange?: {
    start: string;
    end: string;
  }): Promise<APIResponse<ToolMetrics>> {
    return apiClient.get(`/tools/${id}/metrics`, { params: timeRange });
  }

  async getToolUsageStats(id: string, timeRange?: {
    start: string;
    end: string;
  }): Promise<APIResponse<ToolUsageStats>> {
    return apiClient.get(`/tools/${id}/usage`, { params: timeRange });
  }

  async getToolAuditLogs(id: string, params?: {
    action?: string;
    userId?: string;
    page?: number;
    limit?: number;
    since?: string;
  }): Promise<APIResponse<ToolAuditLog[]>> {
    return apiClient.get(`/tools/${id}/audit`, { params });
  }

  // Tool deployment and management
  async deployTool(id: string, environment: 'development' | 'staging' | 'production'): Promise<APIResponse<{
    deploymentId: string;
    status: string;
    endpoint?: string;
  }>> {
    return apiClient.post(`/tools/${id}/deploy`, { environment });
  }

  async getDeploymentStatus(toolId: string, deploymentId: string): Promise<APIResponse<{
    status: 'pending' | 'deploying' | 'deployed' | 'failed';
    progress: number;
    logs: string[];
    endpoint?: string;
    error?: string;
  }>> {
    return apiClient.get(`/tools/${toolId}/deployments/${deploymentId}`);
  }

  async rollbackDeployment(toolId: string, versionId: string): Promise<APIResponse<void>> {
    return apiClient.post(`/tools/${toolId}/rollback`, { versionId });
  }

  // Tool sharing and collaboration
  async shareTool(id: string, data: {
    userIds?: string[];
    permissions: ('view' | 'edit' | 'execute')[];
    expiresAt?: string;
  }): Promise<APIResponse<{
    shareId: string;
    shareUrl: string;
  }>> {
    return apiClient.post(`/tools/${id}/share`, data);
  }

  async getSharedTools(): Promise<APIResponse<Tool[]>> {
    return apiClient.get('/tools/shared');
  }

  // Tool marketplace
  async publishToMarketplace(id: string, data: {
    name: string;
    description: string;
    category: string;
    tags: string[];
    pricing?: {
      type: 'free' | 'paid';
      price?: number;
    };
  }): Promise<APIResponse<{
    listingId: string;
  }>> {
    return apiClient.post(`/tools/${id}/publish`, data);
  }

  async searchMarketplace(params: {
    query?: string;
    category?: string;
    tags?: string[];
    pricing?: 'free' | 'paid';
    page?: number;
    limit?: number;
  }): Promise<APIResponse<any[]>> {
    return apiClient.get('/marketplace/tools', { params });
  }

  async installFromMarketplace(listingId: string): Promise<APIResponse<Tool>> {
    return apiClient.post(`/marketplace/tools/${listingId}/install`);
  }

  // Tool templates
  async createTemplate(id: string, data: {
    name: string;
    description: string;
    category: string;
    isPublic: boolean;
  }): Promise<APIResponse<{
    templateId: string;
  }>> {
    return apiClient.post(`/tools/${id}/template`, data);
  }

  async listTemplates(params?: {
    category?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<APIResponse<any[]>> {
    return apiClient.get('/tools/templates', { params });
  }

  async createFromTemplate(templateId: string, data: {
    name: string;
    customizations?: Record<string, any>;
  }): Promise<APIResponse<Tool>> {
    return apiClient.post(`/tools/templates/${templateId}/create`, data);
  }

  // Bulk operations
  async bulkUpdate(toolIds: string[], updates: UpdateToolRequest): Promise<APIResponse<{
    updated: string[];
    failed: { id: string; error: string }[];
  }>> {
    return apiClient.patch('/tools/bulk', { toolIds, updates });
  }

  async bulkDelete(toolIds: string[]): Promise<APIResponse<{
    deleted: string[];
    failed: { id: string; error: string }[];
  }>> {
    return apiClient.delete('/tools/bulk', { data: { toolIds } });
  }

  async bulkExecute(executions: Array<{
    toolId: string;
    input: Record<string, any>;
    versionId?: string;
  }>): Promise<APIResponse<{
    results: ToolExecutionResult[];
    failed: { toolId: string; error: string }[];
  }>> {
    return apiClient.post('/tools/bulk/execute', { executions });
  }

  // Export/Import
  async exportTool(id: string, format: 'json' | 'yaml'): Promise<APIResponse<{
    downloadUrl: string;
    expiresAt: string;
  }>> {
    return apiClient.post(`/tools/${id}/export`, { format });
  }

  async importTool(file: File): Promise<APIResponse<Tool>> {
    return apiClient.uploadFile('/tools/import', file);
  }

  // Tool monitoring
  async getToolHealth(id: string): Promise<APIResponse<{
    status: 'healthy' | 'degraded' | 'down';
    lastCheck: string;
    responseTime: number;
    errorRate: number;
    uptime: number;
  }>> {
    return apiClient.get(`/tools/${id}/health`);
  }

  async setToolAlerts(id: string, alerts: {
    errorRate?: { threshold: number; enabled: boolean };
    responseTime?: { threshold: number; enabled: boolean };
    uptime?: { threshold: number; enabled: boolean };
  }): Promise<APIResponse<void>> {
    return apiClient.post(`/tools/${id}/alerts`, alerts);
  }
}

export const toolAPI = new ToolAPI();
export default ToolAPI;