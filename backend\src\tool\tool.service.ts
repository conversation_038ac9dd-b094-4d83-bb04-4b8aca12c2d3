import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateToolDto, UpdateToolDto } from './dto/tool.dto';
import { realToolService } from '../../lib/tools/real-tools';

@Injectable()
export class ToolService {
  constructor(private prisma: PrismaService) {}

  async create(organizationId: string, dto: CreateToolDto) {
    return this.prisma.tool.create({
      data: {
        ...dto,
        organizationId,
        config: dto.config || {},
        schema: dto.schema || {},
      },
    });
  }

  async findAll(organizationId: string) {
    return this.prisma.tool.findMany({
      where: { organizationId },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: string, organizationId: string) {
    return this.prisma.tool.findFirst({
      where: { id, organizationId },
    });
  }

  async update(id: string, organizationId: string, dto: UpdateToolDto) {
    return this.prisma.tool.update({
      where: { id },
      data: {
        ...dto,
        updatedAt: new Date(),
      },
    });
  }

  async remove(id: string, organizationId: string) {
    return this.prisma.tool.delete({
      where: { id },
    });
  }

  async execute(toolId: string, organizationId: string, input: any) {
    const tool = await this.findOne(toolId, organizationId);
    if (!tool) {
      throw new BadRequestException('Tool not found');
    }

    if (!tool.isActive) {
      throw new BadRequestException('Tool is not active');
    }

    const startTime = Date.now();
    
    try {
      const result = await this.executeTool(tool, input);
      const executionTime = Date.now() - startTime;

      await this.prisma.$executeRaw`
        INSERT INTO tool_executions (
          id, tool_id, organization_id, input, output, 
          execution_time, status, cost, created_at
        ) VALUES (
          gen_random_uuid(), ${toolId}, ${organizationId}, ${JSON.stringify(input)}::jsonb, 
          ${JSON.stringify(result.data)}::jsonb, ${result.executionTime}, 
          ${result.success ? 'success' : 'error'}, ${result.cost || 0}, NOW()
        )
      `;

      return {
        toolId,
        input,
        result: result.data,
        success: result.success,
        executionTime: result.executionTime,
        cost: result.cost,
        timestamp: new Date(),
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      await this.prisma.$executeRaw`
        INSERT INTO tool_executions (
          id, tool_id, organization_id, input, error, 
          execution_time, status, created_at
        ) VALUES (
          gen_random_uuid(), ${toolId}, ${organizationId}, ${JSON.stringify(input)}::jsonb, 
          ${error.message}, ${executionTime}, 'error', NOW()
        )
      `;

      throw error;
    }
  }

  private async executeTool(tool: any, input: any) {
    const config = tool.config || {};
    
    switch (tool.name) {
      case 'web-search':
        return await realToolService.executeWebSearch(input.query, input.limit);
      
      case 'calculator':
        return await realToolService.executeCalculator(input.expression);
      
      case 'api-caller':
        return await realToolService.executeApiCall({
          url: input.url,
          method: input.method || 'GET',
          headers: input.headers,
          body: input.body,
          timeout: config.timeout,
        });
      
      case 'email':
        return await realToolService.executeEmailSend({
          to: input.to,
          subject: input.subject,
          content: input.content,
          from: config.fromEmail,
        });
      
      default:
        throw new Error(`Unknown tool: ${tool.name}`);
    }
  }

  private async executeApiTool(tool: any, input: any) {
    const config = tool.config;
    const response = await fetch(config.url, {
      method: config.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
      body: config.method !== 'GET' ? JSON.stringify(input) : undefined,
    });

    return {
      status: response.status,
      data: await response.json(),
      timestamp: new Date(),
    };
  }

  private async executeWebhookTool(tool: any, input: any) {
    const config = tool.config;
    const response = await fetch(config.webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Tool-Signature': this.generateSignature(input, config.secret),
      },
      body: JSON.stringify(input),
    });

    return {
      status: response.status,
      data: await response.json(),
      timestamp: new Date(),
    };
  }

  private async executeDatabaseTool(tool: any, input: any) {
    const config = tool.config;
    // Database query execution logic
    return {
      query: config.query,
      parameters: input,
      result: 'Database query executed',
      timestamp: new Date(),
    };
  }

  private async executeFileSystemTool(tool: any, input: any) {
    const config = tool.config;
    // File system operation logic
    return {
      operation: config.operation,
      path: config.path,
      result: 'File system operation completed',
      timestamp: new Date(),
    };
  }

  private async executeCustomTool(tool: any, input: any) {
    const config = tool.config;
    // Custom tool execution logic
    return {
      code: config.code,
      input,
      result: 'Custom tool executed',
      timestamp: new Date(),
    };
  }

  private generateSignature(payload: any, secret: string): string {
    const crypto = require('crypto');
    return crypto
      .createHmac('sha256', secret)
      .update(JSON.stringify(payload))
      .digest('hex');
  }
}