"use client";

import React from "react";
import { useAuth } from "@/components/auth/AuthProvider";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Shield, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredRole?: string;
  fallback?: React.ReactNode;
}

export default function ProtectedRoute({
  children,
  requiredPermission,
  requiredRole,
  fallback,
}: ProtectedRouteProps) {
  const { user, loading, hasPermission, hasRole } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push("/auth/login");
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  // Check permission if required
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-background p-4">
          <div className="max-w-md w-full">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="mt-2">
                <div className="space-y-2">
                  <p className="font-semibold">Access Denied</p>
                  <p className="text-sm">
                    You don't have the required permission:{" "}
                    <code className="bg-muted px-1 py-0.5 rounded text-xs">
                      {requiredPermission}
                    </code>
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Contact your organization administrator to request access.
                  </p>
                </div>
              </AlertDescription>
            </Alert>
          </div>
        </div>
      )
    );
  }

  // Check role if required
  if (requiredRole && !hasRole(requiredRole)) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-background p-4">
          <div className="max-w-md w-full">
            <Alert variant="destructive">
              <Shield className="h-4 w-4" />
              <AlertDescription className="mt-2">
                <div className="space-y-2">
                  <p className="font-semibold">Insufficient Role</p>
                  <p className="text-sm">
                    You need the{" "}
                    <code className="bg-muted px-1 py-0.5 rounded text-xs">
                      {requiredRole}
                    </code>{" "}
                    role to access this resource.
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Contact your organization administrator to request role
                    elevation.
                  </p>
                </div>
              </AlertDescription>
            </Alert>
          </div>
        </div>
      )
    );
  }

  return <>{children}</>;
}
