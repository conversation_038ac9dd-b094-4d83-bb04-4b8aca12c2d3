import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { ToolService } from './tool.service';
import { CreateToolDto, UpdateToolDto, ExecuteToolDto } from './dto/tool.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Tools')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('tools')
export class ToolController {
  constructor(private toolService: ToolService) {}

  @Post()
  @ApiOperation({ summary: 'Create new tool' })
  create(@Request() req, @Body() dto: CreateToolDto) {
    return this.toolService.create(req.user.organizationId, dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all tools' })
  findAll(@Request() req) {
    return this.toolService.findAll(req.user.organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get tool by ID' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.toolService.findOne(id, req.user.organizationId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update tool' })
  update(@Param('id') id: string, @Request() req, @Body() dto: UpdateToolDto) {
    return this.toolService.update(id, req.user.organizationId, dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete tool' })
  remove(@Param('id') id: string, @Request() req) {
    return this.toolService.remove(id, req.user.organizationId);
  }

  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute tool' })
  execute(@Param('id') id: string, @Request() req, @Body() dto: ExecuteToolDto) {
    return this.toolService.execute(id, req.user.organizationId, dto.input);
  }
}