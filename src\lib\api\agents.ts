import { apiClient, APIResponse } from './base';
import { Agent, Session, AgentEvent } from '../types/agent';

export interface CreateAgentRequest {
  name: string;
  description: string;
  type: string;
  model: string;
  systemPrompt: string;
  tools: string[];
  memory: {
    enabled: boolean;
    type: string;
    maxTokens: number;
  };
  providerConfig: {
    primary: string;
    fallbacks: string[];
  };
  isActive: boolean;
}

export interface UpdateAgentRequest extends Partial<CreateAgentRequest> {}

export interface CreateSessionRequest {
  agentId: string;
  context?: Record<string, any>;
}

export interface SendMessageRequest {
  sessionId: string;
  message: string;
  context?: Record<string, any>;
}

export interface MessageResponse {
  id: string;
  content: string;
  role: 'assistant';
  timestamp: string;
  tokenUsage: {
    input: number;
    output: number;
    total: number;
  };
  cost: number;
  toolCalls?: any[];
  metadata?: Record<string, any>;
}

export interface AgentMetrics {
  totalSessions: number;
  totalMessages: number;
  totalTokens: number;
  totalCost: number;
  averageResponseTime: number;
  successRate: number;
  lastActivity: string;
}

class AgentAPI {
  // Agent CRUD operations
  async createAgent(data: CreateAgentRequest): Promise<APIResponse<Agent>> {
    return apiClient.post('/agents', data);
  }

  async getAgent(id: string): Promise<APIResponse<Agent>> {
    return apiClient.get(`/agents/${id}`);
  }

  async updateAgent(id: string, data: UpdateAgentRequest): Promise<APIResponse<Agent>> {
    return apiClient.patch(`/agents/${id}`, data);
  }

  async deleteAgent(id: string): Promise<APIResponse<void>> {
    return apiClient.delete(`/agents/${id}`);
  }

  async listAgents(params?: {
    page?: number;
    limit?: number;
    search?: string;
    type?: string;
    isActive?: boolean;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<APIResponse<Agent[]>> {
    return apiClient.get('/agents', { params });
  }

  async duplicateAgent(id: string, name?: string): Promise<APIResponse<Agent>> {
    return apiClient.post(`/agents/${id}/duplicate`, { name });
  }

  // Session management
  async createSession(data: CreateSessionRequest): Promise<APIResponse<Session>> {
    return apiClient.post('/sessions', data);
  }

  async getSession(id: string): Promise<APIResponse<Session>> {
    return apiClient.get(`/sessions/${id}`);
  }

  async endSession(id: string): Promise<APIResponse<void>> {
    return apiClient.patch(`/sessions/${id}/end`);
  }

  async listSessions(params?: {
    agentId?: string;
    userId?: string;
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<APIResponse<Session[]>> {
    return apiClient.get('/sessions', { params });
  }

  // Message handling
  async sendMessage(data: SendMessageRequest): Promise<APIResponse<MessageResponse>> {
    return apiClient.post('/messages', data);
  }

  async sendMessageStream(
    data: SendMessageRequest,
    onChunk: (chunk: any) => void
  ): Promise<void> {
    return apiClient.stream('/messages/stream', data, onChunk);
  }

  async getSessionMessages(sessionId: string, params?: {
    page?: number;
    limit?: number;
    before?: string;
    after?: string;
  }): Promise<APIResponse<MessageResponse[]>> {
    return apiClient.get(`/sessions/${sessionId}/messages`, { params });
  }

  // Agent metrics and analytics
  async getAgentMetrics(id: string, timeRange?: {
    start: string;
    end: string;
  }): Promise<APIResponse<AgentMetrics>> {
    return apiClient.get(`/agents/${id}/metrics`, { params: timeRange });
  }

  async getAgentEvents(id: string, params?: {
    type?: string;
    page?: number;
    limit?: number;
    since?: string;
  }): Promise<APIResponse<AgentEvent[]>> {
    return apiClient.get(`/agents/${id}/events`, { params });
  }

  // Agent testing
  async testAgent(id: string, testMessage: string): Promise<APIResponse<MessageResponse>> {
    return apiClient.post(`/agents/${id}/test`, { message: testMessage });
  }

  async validateAgentConfig(config: CreateAgentRequest): Promise<APIResponse<{
    valid: boolean;
    errors?: string[];
    warnings?: string[];
  }>> {
    return apiClient.post('/agents/validate', config);
  }

  // Agent deployment
  async deployAgent(id: string, environment: 'development' | 'staging' | 'production'): Promise<APIResponse<{
    deploymentId: string;
    status: string;
    url?: string;
  }>> {
    return apiClient.post(`/agents/${id}/deploy`, { environment });
  }

  async getDeploymentStatus(agentId: string, deploymentId: string): Promise<APIResponse<{
    status: 'pending' | 'deploying' | 'deployed' | 'failed';
    progress: number;
    logs: string[];
    url?: string;
    error?: string;
  }>> {
    return apiClient.get(`/agents/${agentId}/deployments/${deploymentId}`);
  }

  // Agent collaboration
  async shareAgent(id: string, data: {
    userIds?: string[];
    permissions: ('view' | 'edit' | 'execute')[];
    expiresAt?: string;
  }): Promise<APIResponse<{
    shareId: string;
    shareUrl: string;
  }>> {
    return apiClient.post(`/agents/${id}/share`, data);
  }

  async getSharedAgents(): Promise<APIResponse<Agent[]>> {
    return apiClient.get('/agents/shared');
  }

  // Agent templates
  async createTemplate(id: string, data: {
    name: string;
    description: string;
    category: string;
    isPublic: boolean;
  }): Promise<APIResponse<{
    templateId: string;
  }>> {
    return apiClient.post(`/agents/${id}/template`, data);
  }

  async listTemplates(params?: {
    category?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<APIResponse<any[]>> {
    return apiClient.get('/agents/templates', { params });
  }

  async createFromTemplate(templateId: string, data: {
    name: string;
    customizations?: Record<string, any>;
  }): Promise<APIResponse<Agent>> {
    return apiClient.post(`/agents/templates/${templateId}/create`, data);
  }

  // Bulk operations
  async bulkUpdate(agentIds: string[], updates: UpdateAgentRequest): Promise<APIResponse<{
    updated: string[];
    failed: { id: string; error: string }[];
  }>> {
    return apiClient.patch('/agents/bulk', { agentIds, updates });
  }

  async bulkDelete(agentIds: string[]): Promise<APIResponse<{
    deleted: string[];
    failed: { id: string; error: string }[];
  }>> {
    return apiClient.delete('/agents/bulk', { data: { agentIds } });
  }

  // Export/Import
  async exportAgent(id: string, format: 'json' | 'yaml'): Promise<APIResponse<{
    downloadUrl: string;
    expiresAt: string;
  }>> {
    return apiClient.post(`/agents/${id}/export`, { format });
  }

  async importAgent(file: File): Promise<APIResponse<Agent>> {
    return apiClient.uploadFile('/agents/import', file);
  }
}

export const agentAPI = new AgentAPI();
export default AgentAPI;