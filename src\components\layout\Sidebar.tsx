"use client";

import React, { useState } from 'react';
import { useAuth } from '@/components/auth/AuthProvider';
import { usePathname, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ThemeSwitcher } from '@/components/theme-switcher';
import OrganizationSwitcher from '@/components/organization/OrganizationSwitcher';
import { 
  Home,
  Bot,
  Workflow,
  Wrench,
  Users,
  Settings,
  BarChart3,
  Building2,
  LogOut,
  User,
  Shield,
  ChevronLeft,
  ChevronRight,
  Menu,
  Crown
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface SidebarProps {
  className?: string;
}

const navigation = [
  {
    name: 'Dashboard',
    href: '/',
    icon: Home,
    permission: null,
  },
  {
    name: 'Agents',
    href: '/agents',
    icon: Bot,
    permission: 'agents:read',
  },
  {
    name: 'Workflows',
    href: '/workflows',
    icon: Workflow,
    permission: 'workflows:read',
  },
  {
    name: 'Tools',
    href: '/tools',
    icon: Wrench,
    permission: 'tools:read',
  },
  {
    name: 'Providers',
    href: '/providers',
    icon: Building2,
    permission: 'providers:read',
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: BarChart3,
    permission: 'analytics:read',
  },
];

const adminNavigation = [
  {
    name: 'User Management',
    href: '/admin/users',
    icon: Users,
    permission: 'users:read',
  },
  {
    name: 'Organization Settings',
    href: '/admin/organization',
    icon: Settings,
    permission: 'organization:update',
  },
];

export default function Sidebar({ className }: SidebarProps) {
  const { user, organization, logout, hasPermission } = useAuth();
  const pathname = usePathname();
  const router = useRouter();
  const [collapsed, setCollapsed] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);

  const handleLogout = async () => {
    await logout();
  };

  const getUserRole = () => {
    if (!user?.roles) return 'User';
    const highestRole = user.roles.reduce((highest, current) => {
      const levels = { VIEWER: 1, DEVELOPER: 2, ORG_ADMIN: 3, SUPER_ADMIN: 4 };
      return levels[current.level as keyof typeof levels] > levels[highest.level as keyof typeof levels] 
        ? current : highest;
    });
    return highestRole.name;
  };

  const getRoleIcon = () => {
    if (!user?.roles) return <User className="w-4 h-4" />;
    const hasAdmin = user.roles.some(r => r.level === 'SUPER_ADMIN' || r.level === 'ORG_ADMIN');
    return hasAdmin ? <Crown className="w-4 h-4 text-yellow-500" /> : <User className="w-4 h-4" />;
  };

  const filteredNavigation = navigation.filter(item => 
    !item.permission || hasPermission(item.permission)
  );

  const filteredAdminNavigation = adminNavigation.filter(item => 
    hasPermission(item.permission)
  );

  return (
    <>
      {/* Mobile backdrop */}
      {mobileOpen && (
        <div 
          className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm lg:hidden"
          onClick={() => setMobileOpen(false)}
        />
      )}

      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="sm"
        className="fixed top-4 left-4 z-50 lg:hidden"
        onClick={() => setMobileOpen(!mobileOpen)}
      >
        <Menu className="w-5 h-5" />
      </Button>

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 flex flex-col bg-background border-r border-border transition-all duration-300",
        collapsed ? "w-16" : "w-64",
        mobileOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
        className
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          {!collapsed && (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Building2 className="w-5 h-5 text-primary-foreground" />
              </div>
              <h1 className="text-xl font-bold text-foreground">SynapseAI</h1>
            </div>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCollapsed(!collapsed)}
            className="hidden lg:flex"
          >
            {collapsed ? (
              <ChevronRight className="w-4 h-4" />
            ) : (
              <ChevronLeft className="w-4 h-4" />
            )}
          </Button>
        </div>

        {/* Organization Switcher */}
        <div className="p-4 border-b border-border">
          {collapsed ? (
            <div className="flex justify-center">
              <Avatar className="w-8 h-8">
                <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${organization?.name}`} />
                <AvatarFallback>
                  <Building2 className="w-4 h-4" />
                </AvatarFallback>
              </Avatar>
            </div>
          ) : (
            <OrganizationSwitcher />
          )}
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto p-4 space-y-2">
          {/* Main Navigation */}
          <div className="space-y-1">
            {!collapsed && (
              <p className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2">
                Main
              </p>
            )}
            {filteredNavigation.map((item) => {
              const isActive = pathname === item.href || 
                (item.href !== '/' && pathname.startsWith(item.href));
              
              return (
                <Link key={item.name} href={item.href}>
                  <Button
                    variant={isActive ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start",
                      collapsed ? "px-2" : "px-3",
                      isActive && "bg-accent text-accent-foreground"
                    )}
                    onClick={() => setMobileOpen(false)}
                  >
                    <item.icon className={cn("w-5 h-5", collapsed ? "" : "mr-3")} />
                    {!collapsed && item.name}
                  </Button>
                </Link>
              );
            })}
          </div>

          {/* Admin Navigation */}
          {filteredAdminNavigation.length > 0 && (
            <>
              <Separator className="my-4" />
              <div className="space-y-1">
                {!collapsed && (
                  <p className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2">
                    Administration
                  </p>
                )}
                {filteredAdminNavigation.map((item) => {
                  const isActive = pathname === item.href || 
                    (item.href !== '/' && pathname.startsWith(item.href));
                  
                  return (
                    <Link key={item.name} href={item.href}>
                      <Button
                        variant={isActive ? "secondary" : "ghost"}
                        className={cn(
                          "w-full justify-start",
                          collapsed ? "px-2" : "px-3",
                          isActive && "bg-accent text-accent-foreground"
                        )}
                        onClick={() => setMobileOpen(false)}
                      >
                        <item.icon className={cn("w-5 h-5", collapsed ? "" : "mr-3")} />
                        {!collapsed && item.name}
                      </Button>
                    </Link>
                  );
                })}
              </div>
            </>
          )}
        </nav>

        {/* User Profile */}
        <div className="p-4 border-t border-border">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className={cn(
                  "w-full justify-start h-auto p-3",
                  collapsed && "px-2"
                )}
              >
                <div className="flex items-center space-x-3">
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={user?.profile?.avatar} />
                    <AvatarFallback>
                      {user?.profile?.firstName?.[0]}{user?.profile?.lastName?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  {!collapsed && (
                    <div className="text-left flex-1">
                      <p className="font-medium text-sm">
                        {user?.profile?.firstName} {user?.profile?.lastName}
                      </p>
                      <div className="flex items-center space-x-1">
                        {getRoleIcon()}
                        <p className="text-xs text-muted-foreground">
                          {getUserRole()}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </Button>
            </DropdownMenuTrigger>
            
            <DropdownMenuContent className="w-56" align="end">
              <div className="p-2">
                <p className="font-medium">{user?.profile?.firstName} {user?.profile?.lastName}</p>
                <p className="text-sm text-muted-foreground">{user?.email}</p>
                <Badge variant="outline" className="mt-1">
                  {getUserRole()}
                </Badge>
              </div>
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem onClick={() => router.push('/profile')}>
                <User className="w-4 h-4 mr-2" />
                Profile Settings
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={() => router.push('/security')}>
                <Shield className="w-4 h-4 mr-2" />
                Security
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              
              <div className="p-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Theme</span>
                  <ThemeSwitcher />
                </div>
              </div>
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem onClick={handleLogout} className="text-destructive">
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </>
  );
}