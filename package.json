{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@auth/prisma-adapter": "^2.10.0", "@monaco-editor/react": "^4.7.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/platform-socket.io": "^11.1.5", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.1.5", "@prisma/client": "^6.13.0", "@sendgrid/mail": "^8.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/qrcode": "^1.5.5", "@types/ws": "^8.18.1", "autoprefixer": "10.4.20", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.23.12", "ioredis": "^5.7.0", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.468.0", "nestjs-zod": "^4.3.1", "next": "14.2.23", "next-auth": "^4.24.11", "next-themes": "^0.2.1", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prettier": "^3.3.3", "prisma": "^6.13.0", "qrcode": "^1.5.4", "radix-ui": "^1.1.3", "react": "^18", "react-day-picker": "^9.5.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "reactflow": "^11.11.4", "redis": "^5.7.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "speakeasy": "^2.0.0", "stripe": "^17.6.0", "tempo-devtools": "^2.0.109", "vaul": "^1.1.2", "ws": "^8.18.3", "zod": "^4.0.14", "openai": "^4.52.7", "pg": "^8.11.3", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "cheerio": "^1.0.0-rc.12", "compression": "^1.7.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "typescript": "^5"}}