"use client";

import React, { useState, useRef, useEffect } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AlertCircle, Bot, Code, Copy, Database, FileText, Info, Loader2, MessageSquare, Paperclip, RefreshCw, Send, Settings, Sparkles, ThumbsDown, Thum<PERSON>Up, X, Zap } from "lucide-react";
import AppLayout from "@/components/layout/AppLayout";
import { useAgent } from "@/components/providers/agent-provider";
import { cn } from "@/lib/utils";

interface Message {
  id: string;
  role: "user" | "assistant" | "system" | "tool";
  content: string;
  timestamp: number;
  status?: "sending" | "error" | "complete";
  toolCalls?: ToolCall[];
  toolResults?: ToolResult[];
}

interface ToolCall {
  id: string;
  name: string;
  arguments: string;
}

interface ToolResult {
  callId: string;
  result: string;
  error?: string;
}

export default function AgentChatPage() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      role: "system",
      content: "Welcome to the AI Agent Chat. How can I help you today?",
      timestamp: Date.now()
    }
  ]);
  const [input, setInput] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [activeTab, setActiveTab] = useState("chat");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { agents } = useAgent();

  // Sample agent data
  const currentAgent = {
    id: "agent-1",
    name: "Research Assistant",
    description: "AI research assistant that can search the web, analyze documents, and provide insights",
    model: "gpt-4",
    provider: "OpenAI",
    avatar: "https://api.dicebear.com/7.x/bottts/svg?seed=research",
    tools: ["web-search", "document-qa", "code-interpreter"]
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = () => {
    if (!input.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: "user",
      content: input,
      timestamp: Date.now()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInput("");
    setIsTyping(true);

    // Focus back on input
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);

    // Simulate AI response with typing indicator
    setTimeout(() => {
      // Add assistant message
      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        role: "assistant",
        content: simulateResponse(input),
        timestamp: Date.now(),
        status: "complete"
      };
      
      setMessages(prev => [...prev, assistantMessage]);
      setIsTyping(false);
    }, 2000);
  };

  const simulateResponse = (userInput: string) => {
    // Simple response simulation
    if (userInput.toLowerCase().includes("hello") || userInput.toLowerCase().includes("hi")) {
      return "Hello! How can I assist you with your research today?";
    }
    
    if (userInput.toLowerCase().includes("search") || userInput.toLowerCase().includes("find")) {
      return "I can search for information on that topic. Let me gather some relevant resources for you.";
    }
    
    if (userInput.toLowerCase().includes("document") || userInput.toLowerCase().includes("analyze")) {
      return "I'd be happy to analyze that document for you. Please upload it or provide a link, and I'll extract the key insights.";
    }
    
    if (userInput.toLowerCase().includes("code") || userInput.toLowerCase().includes("programming")) {
      return "I can help with coding questions or generate code snippets. What programming language are you working with?";
    }
    
    return "I understand you're asking about " + userInput.split(" ").slice(0, 3).join(" ") + "... Let me help you with that. Based on my knowledge, this is a complex topic with several important aspects to consider.";
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const simulateToolCall = () => {
    // Add a message with tool calls
    const toolCallMessage: Message = {
      id: `assistant-tool-${Date.now()}`,
      role: "assistant",
      content: "Let me search for that information for you.",
      timestamp: Date.now(),
      toolCalls: [
        {
          id: "tool-call-1",
          name: "web-search",
          arguments: JSON.stringify({
            query: "latest research on AI agents",
            limit: 3
          })
        }
      ]
    };
    
    setMessages(prev => [...prev, toolCallMessage]);
    
    // Simulate tool execution
    setTimeout(() => {
      const toolResultMessage: Message = {
        id: `tool-${Date.now()}`,
        role: "tool",
        content: "Tool execution results",
        timestamp: Date.now(),
        toolResults: [
          {
            callId: "tool-call-1",
            result: JSON.stringify({
              results: [
                {
                  title: "Recent Advances in AI Agent Architectures",
                  url: "https://example.com/research/ai-agents",
                  snippet: "This paper discusses the latest developments in AI agent architectures, focusing on multi-agent systems and emergent behaviors."
                },
                {
                  title: "Collaborative AI Agents in Enterprise Settings",
                  url: "https://example.com/enterprise-ai",
                  snippet: "A study on how collaborative AI agents are being deployed in enterprise environments to automate complex workflows."
                },
                {
                  title: "The Future of AI Agents: Trends and Predictions",
                  url: "https://example.com/ai-future",
                  snippet: "Industry experts share their insights on where AI agent technology is headed in the next five years."
                }
              ]
            })
          }
        ]
      };
      
      setMessages(prev => [...prev, toolResultMessage]);
      
      // Assistant summarizes the results
      setTimeout(() => {
        const summaryMessage: Message = {
          id: `assistant-summary-${Date.now()}`,
          role: "assistant",
          content: "Based on my search, here are the key findings about AI agents:\n\n1. Recent research is focusing on multi-agent systems and emergent behaviors\n2. Enterprises are increasingly deploying collaborative AI agents to automate complex workflows\n3. Industry experts predict significant advancements in AI agent technology over the next five years\n\nWould you like me to elaborate on any of these points?",
          timestamp: Date.now()
        };
        
        setMessages(prev => [...prev, summaryMessage]);
      }, 1500);
    }, 2000);
  };

  return (
    <AppLayout>
      <div className="flex h-[calc(100vh-8rem)] overflow-hidden">
        {/* Left sidebar - Agent info */}
        <div className="w-64 border-r border-border hidden md:block">
          <div className="p-4">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={currentAgent.avatar} alt={currentAgent.name} />
                <AvatarFallback>AI</AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-medium">{currentAgent.name}</h3>
                <p className="text-xs text-muted-foreground">
                  {currentAgent.model}
                </p>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-2">Description</h4>
                <p className="text-xs text-muted-foreground">
                  {currentAgent.description}
                </p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium mb-2">Tools</h4>
                <div className="flex flex-wrap gap-1">
                  {currentAgent.tools.map(tool => (
                    <Badge key={tool} variant="outline" className="text-xs">
                      {tool}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium mb-2">Provider</h4>
                <div className="flex items-center">
                  <Zap className="h-3 w-3 mr-1 text-blue-500" />
                  <span className="text-xs">{currentAgent.provider}</span>
                </div>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Settings className="mr-2 h-3.5 w-3.5" />
                <span className="text-xs">Agent Settings</span>
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <RefreshCw className="mr-2 h-3.5 w-3.5" />
                <span className="text-xs">New Conversation</span>
              </Button>
            </div>
          </div>
        </div>
        
        {/* Main chat area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <Tabs defaultValue="chat" value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <div className="border-b border-border px-4">
              <div className="flex items-center justify-between py-2">
                <TabsList>
                  <TabsTrigger value="chat" className="text-xs">
                    <MessageSquare className="h-3.5 w-3.5 mr-1" />
                    Chat
                  </TabsTrigger>
                  <TabsTrigger value="tools" className="text-xs">
                    <Zap className="h-3.5 w-3.5 mr-1" />
                    Tools
                  </TabsTrigger>
                  <TabsTrigger value="files" className="text-xs">
                    <FileText className="h-3.5 w-3.5 mr-1" />
                    Files
                  </TabsTrigger>
                </TabsList>
                
                <div className="flex items-center">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Info className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>View session information</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            </div>
            
            <TabsContent value="chat" className="flex-1 flex flex-col p-0 m-0 data-[state=active]:flex-1">
              {/* Messages area */}
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  {messages.map((message) => (
                    <div key={message.id} className={cn(
                      "flex",
                      message.role === "user" ? "justify-end" : "justify-start"
                    )}>
                      <div className={cn(
                        "flex max-w-[80%]",
                        message.role === "user" ? "flex-row-reverse" : "flex-row",
                      )}>
                        {message.role !== "user" && (
                          <Avatar className="h-8 w-8 mr-2">
                            <AvatarImage src={currentAgent.avatar} alt={currentAgent.name} />
                            <AvatarFallback>AI</AvatarFallback>
                          </Avatar>
                        )}
                        
                        <div className={cn(
                          "rounded-lg p-3",
                          message.role === "user" 
                            ? "bg-primary text-primary-foreground ml-2" 
                            : message.role === "system"
                              ? "bg-muted text-muted-foreground"
                              : message.role === "tool"
                                ? "bg-yellow-100 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-800"
                                : "bg-accent"
                        )}>
                          {message.content}
                          
                          {/* Tool calls */}
                          {message.toolCalls && message.toolCalls.length > 0 && (
                            <div className="mt-2 pt-2 border-t border-border">
                              <p className="text-xs font-medium mb-1">Tool Calls:</p>
                              {message.toolCalls.map(tool => (
                                <div key={tool.id} className="bg-background rounded p-2 text-xs font-mono mt-1">
                                  <div className="flex items-center text-muted-foreground mb-1">
                                    <Zap className="h-3 w-3 mr-1" />
                                    <span>{tool.name}</span>
                                  </div>
                                  <pre className="whitespace-pre-wrap overflow-auto max-h-32">
                                    {JSON.stringify(JSON.parse(tool.arguments), null, 2)}
                                  </pre>
                                </div>
                              ))}
                            </div>
                          )}
                          
                          {/* Tool results */}
                          {message.toolResults && message.toolResults.length > 0 && (
                            <div className="mt-2">
                              {message.toolResults.map(result => (
                                <div key={result.callId} className="bg-background rounded p-2 text-xs font-mono">
                                  <div className="flex items-center text-muted-foreground mb-1">
                                    <Database className="h-3 w-3 mr-1" />
                                    <span>Tool Result</span>
                                  </div>
                                  {result.error ? (
                                    <div className="text-red-500">{result.error}</div>
                                  ) : (
                                    <pre className="whitespace-pre-wrap overflow-auto max-h-32">
                                      {JSON.stringify(JSON.parse(result.result), null, 2)}
                                    </pre>
                                  )}
                                </div>
                              ))}
                            </div>
                          )}
                          
                          <div className={cn(
                            "flex items-center justify-end gap-1 mt-1",
                            message.role === "user" ? "text-primary-foreground/70" : "text-muted-foreground"
                          )}>
                            <span className="text-[10px]">
                              {new Date(message.timestamp).toLocaleTimeString()}
                            </span>
                            
                            {message.role === "assistant" && (
                              <>
                                <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full opacity-70 hover:opacity-100">
                                  <Copy className="h-3 w-3" />
                                </Button>
                                <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full opacity-70 hover:opacity-100">
                                  <ThumbsUp className="h-3 w-3" />
                                </Button>
                                <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full opacity-70 hover:opacity-100">
                                  <ThumbsDown className="h-3 w-3" />
                                </Button>
                              </>
                            )}
                          </div>
                        </div>
                        
                        {message.role === "user" && (
                          <Avatar className="h-8 w-8 ml-2">
                            <AvatarImage src="https://api.dicebear.com/7.x/avataaars/svg?seed=user" alt="User" />
                            <AvatarFallback>U</AvatarFallback>
                          </Avatar>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {/* Typing indicator */}
                  {isTyping && (
                    <div className="flex justify-start">
                      <div className="flex flex-row">
                        <Avatar className="h-8 w-8 mr-2">
                          <AvatarImage src={currentAgent.avatar} alt={currentAgent.name} />
                          <AvatarFallback>AI</AvatarFallback>
                        </Avatar>
                        
                        <div className="rounded-lg p-3 bg-accent">
                          <div className="flex items-center space-x-1">
                            <div className="w-2 h-2 rounded-full bg-current animate-bounce" />
                            <div className="w-2 h-2 rounded-full bg-current animate-bounce [animation-delay:-.3s]" />
                            <div className="w-2 h-2 rounded-full bg-current animate-bounce [animation-delay:-.5s]" />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>
              
              {/* Input area */}
              <div className="border-t border-border p-4">
                <div className="flex items-end gap-2">
                  <div className="relative flex-1">
                    <Input
                      ref={inputRef}
                      placeholder="Type your message..."
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      className="pr-10 py-6 resize-none"
                    />
                    <div className="absolute right-2 bottom-2">
                      <Button 
                        type="button" 
                        size="icon" 
                        variant="ghost" 
                        className="h-6 w-6"
                        onClick={() => simulateToolCall()}
                      >
                        <Paperclip className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <Button 
                    type="button" 
                    size="icon" 
                    onClick={handleSendMessage}
                    disabled={!input.trim() || isTyping}
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center text-xs text-muted-foreground">
                    <Sparkles className="h-3 w-3 mr-1" />
                    <span>Powered by {currentAgent.model}</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <span>0 tokens used</span>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="tools" className="flex-1 p-4 m-0">
              <div className="h-full flex flex-col">
                <h3 className="text-lg font-medium mb-4">Available Tools</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentAgent.tools.map(tool => (
                    <Card key={tool} className="overflow-hidden">
                      <CardHeader className="pb-2">
                        <div className="flex items-center">
                          {tool === "web-search" && <Globe className="h-4 w-4 mr-2 text-blue-500" />}
                          {tool === "document-qa" && <FileText className="h-4 w-4 mr-2 text-green-500" />}
                          {tool === "code-interpreter" && <Code className="h-4 w-4 mr-2 text-purple-500" />}
                          <CardTitle className="text-base">{tool}</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <CardDescription>
                          {tool === "web-search" && "Search the web for current information"}
                          {tool === "document-qa" && "Answer questions from documents"}
                          {tool === "code-interpreter" && "Execute code and return results"}
                        </CardDescription>
                      </CardContent>
                      <CardFooter>
                        <Button variant="outline" size="sm" className="w-full">
                          Use Tool
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="files" className="flex-1 p-4 m-0">
              <div className="h-full flex flex-col items-center justify-center">
                <div className="p-4 rounded-full bg-muted">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mt-4">No Files Uploaded</h3>
                <p className="text-muted-foreground text-center max-w-md mt-2">
                  Upload files to provide context to the agent or for analysis
                </p>
                <Button className="mt-4">
                  <Paperclip className="mr-2 h-4 w-4" />
                  Upload Files
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </AppLayout>
  );
}