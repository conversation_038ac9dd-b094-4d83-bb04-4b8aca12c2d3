"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAgent } from "@/components/providers/agent-provider";
import { useEventBus } from "@/components/providers/event-bus-provider";
import { AgentType, MemoryConfig } from "@/lib/types/agent";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../ui/card";
import { Button } from "../ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Textarea } from "../ui/textarea";
import { Switch } from "../ui/switch";
import { Checkbox } from "../ui/checkbox";
import { Badge } from "../ui/badge";
import { Separator } from "../ui/separator";
import { Progress } from "../ui/progress";
import { ScrollArea } from "../ui/scroll-area";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import {
  AlertCircle,
  ArrowRight,
  Check,
  ChevronRight,
  Code,
  Database,
  Globe,
  MessageSquare,
  Plus,
  Rocket,
  Save,
  Settings,
  Wrench,
  Play,
  History,
  RotateCcw,
  FileJson,
  RefreshCw,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Brain,
  Zap,
  Shield,
  TrendingUp,
  Users,
  Clock,
  DollarSign,
  Activity,
  BarChart3
} from "lucide-react";

interface AgentBuilderProps {
  initialAgentId?: string;
  onSave?: (agent: any) => void;
}

export default function AgentBuilder({ initialAgentId, onSave }: AgentBuilderProps) {
  const router = useRouter();
  const { 
    agents, 
    loading, 
    error, 
    selectedAgent, 
    createAgent, 
    updateAgent, 
    deleteAgent, 
    selectAgent,
    executeAgent,
    getExecutionHistory,
    getAgentAnalytics
  } = useAgent();
  
  const { subscribe, unsubscribe } = useEventBus();

  const [currentStep, setCurrentStep] = useState(0);
  const [isCreating, setIsCreating] = useState(!initialAgentId);
  const [isEditing, setIsEditing] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionInput, setExecutionInput] = useState("");
  const [executionResult, setExecutionResult] = useState<any>(null);
  const [executionError, setExecutionError] = useState<string | null>(null);
  const [executionHistory, setExecutionHistory] = useState<any[]>([]);
  const [analytics, setAnalytics] = useState<any>(null);
  const [realTimeEvents, setRealTimeEvents] = useState<any[]>([]);

  // Form state
  const [agentData, setAgentData] = useState({
    name: "",
    description: "",
    type: "standalone" as AgentType,
    model: "gpt-4",
    systemPrompt: "",
    tools: [] as string[],
    memory: {
      enabled: true,
      type: "conversation" as const,
      maxTokens: 4000,
    } as MemoryConfig,
    providerConfig: {
      primary: "openai",
      fallbacks: ["anthropic"],
      routing: { strategy: "latency" as const },
      costLimits: { daily: 10, monthly: 100 }
    },
    isActive: true,
    organizationId: "org_default",
    metadata: {},
    permissions: {}
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [saveProgress, setSaveProgress] = useState(0);
  const [deploymentStatus, setDeploymentStatus] = useState<'idle' | 'deploying' | 'success' | 'error'>('idle');

  const steps = [
    { id: "type", title: "Agent Type", description: "Choose the type of agent", icon: <Brain className="h-4 w-4" /> },
    { id: "basic", title: "Basic Info", description: "Name and description", icon: <FileJson className="h-4 w-4" /> },
    { id: "model", title: "AI Model", description: "Select AI provider and model", icon: <Zap className="h-4 w-4" /> },
    { id: "tools", title: "Tools", description: "Configure available tools", icon: <Wrench className="h-4 w-4" /> },
    { id: "memory", title: "Memory", description: "Memory configuration", icon: <Database className="h-4 w-4" /> },
    { id: "security", title: "Security", description: "Permissions and access", icon: <Shield className="h-4 w-4" /> },
    { id: "review", title: "Review", description: "Review and deploy", icon: <CheckCircle2 className="h-4 w-4" /> },
  ];

  const agentTypes = [
    {
      id: "standalone",
      name: "Standalone Agent",
      description: "Independent conversational agent",
      icon: <MessageSquare className="h-6 w-6" />,
      features: ["Chat interface", "Context awareness", "Memory retention"],
      recommended: true
    },
    {
      id: "tool-driven",
      name: "Tool-driven Agent",
      description: "Agent focused on tool execution",
      icon: <Wrench className="h-6 w-6" />,
      features: ["Tool integration", "API calls", "Data processing"],
      recommended: false
    },
    {
      id: "hybrid",
      name: "Hybrid Agent",
      description: "Combines chat and tool capabilities",
      icon: <Settings className="h-6 w-6" />,
      features: ["Chat + Tools", "Workflow automation", "Multi-modal"],
      recommended: true
    },
    {
      id: "multi-task",
      name: "Multi-task Agent",
      description: "Handles multiple concurrent tasks",
      icon: <Activity className="h-6 w-6" />,
      features: ["Parallel processing", "Task queuing", "Resource management"],
      recommended: false
    },
    {
      id: "collaborative",
      name: "Collaborative Agent",
      description: "Works with other agents",
      icon: <Users className="h-6 w-6" />,
      features: ["Agent communication", "Shared context", "Coordination"],
      recommended: false
    }
  ];

  const aiModels = [
    {
      id: "gpt-4",
      name: "GPT-4",
      provider: "OpenAI",
      capabilities: ["chat", "function-call", "vision"],
      contextWindow: 128000,
      costPer1k: { input: 0.03, output: 0.06 },
      recommended: true
    },
    {
      id: "gpt-4-turbo",
      name: "GPT-4 Turbo",
      provider: "OpenAI",
      capabilities: ["chat", "function-call", "vision"],
      contextWindow: 128000,
      costPer1k: { input: 0.01, output: 0.03 },
      recommended: true
    },
    {
      id: "claude-3-opus",
      name: "Claude 3 Opus",
      provider: "Anthropic",
      capabilities: ["chat", "vision", "analysis"],
      contextWindow: 200000,
      costPer1k: { input: 0.015, output: 0.075 },
      recommended: true
    },
    {
      id: "claude-3-sonnet",
      name: "Claude 3 Sonnet",
      provider: "Anthropic",
      capabilities: ["chat", "analysis"],
      contextWindow: 200000,
      costPer1k: { input: 0.003, output: 0.015 },
      recommended: false
    },
    {
      id: "gemini-pro",
      name: "Gemini Pro",
      provider: "Google",
      capabilities: ["chat", "function-call"],
      contextWindow: 32000,
      costPer1k: { input: 0.0005, output: 0.0015 },
      recommended: false
    }
  ];

  const availableTools = [
    {
      id: "web-search",
      name: "Web Search",
      description: "Search the web for real-time information",
      category: "retrieval",
      verified: true,
      usage: 1250
    },
    {
      id: "calculator",
      name: "Calculator",
      description: "Perform mathematical calculations",
      category: "function",
      verified: true,
      usage: 890
    },
    {
      id: "code-interpreter",
      name: "Code Interpreter",
      description: "Execute and analyze code",
      category: "function",
      verified: true,
      usage: 650
    },
    {
      id: "api-caller",
      name: "API Caller",
      description: "Make HTTP requests to external APIs",
      category: "function",
      verified: true,
      usage: 420
    },
    {
      id: "database-query",
      name: "Database Query",
      description: "Query databases for information",
      category: "database",
      verified: true,
      usage: 380
    },
    {
      id: "document-qa",
      name: "Document Q&A",
      description: "Answer questions from documents",
      category: "retrieval",
      verified: true,
      usage: 720
    }
  ];

  // Load agent data if editing
  useEffect(() => {
    if (initialAgentId) {
      selectAgent(initialAgentId);
      setIsCreating(false);
      setIsTesting(true);
    }
  }, [initialAgentId]);

  // Update form when selected agent changes
  useEffect(() => {
    if (selectedAgent) {
      setAgentData({
        name: selectedAgent.name,
        description: selectedAgent.description,
        type: selectedAgent.type,
        model: selectedAgent.model,
        systemPrompt: selectedAgent.systemPrompt,
        tools: selectedAgent.tools,
        memory: selectedAgent.memory,
        providerConfig: selectedAgent.providerConfig,
        isActive: selectedAgent.isActive,
        organizationId: selectedAgent.organizationId,
        metadata: selectedAgent.metadata || {},
        permissions: selectedAgent.permissions || {}
      });
      
      // Load execution history and analytics
      loadAgentData(selectedAgent.id);
    }
  }, [selectedAgent]);

  // Subscribe to real-time events
  useEffect(() => {
    const eventTypes = [
      'agent:execution:started',
      'agent:execution:completed',
      'thinking_status',
      'text_chunk',
      'error_occurred'
    ];

    eventTypes.forEach(eventType => {
      subscribe(eventType, handleRealTimeEvent);
    });

    return () => {
      eventTypes.forEach(eventType => {
        unsubscribe(eventType, handleRealTimeEvent);
      });
    };
  }, []);

  const handleRealTimeEvent = (event: any) => {
    setRealTimeEvents(prev => [event, ...prev.slice(0, 49)]); // Keep last 50 events
    
    if (event.type === 'agent:execution:completed') {
      setIsExecuting(false);
      setExecutionResult(event.data);
    } else if (event.type === 'error_occurred') {
      setIsExecuting(false);
      setExecutionError(event.data.error);
    }
  };

  const loadAgentData = async (agentId: string) => {
    try {
      const [history, analyticsData] = await Promise.all([
        getExecutionHistory(agentId, 10),
        getAgentAnalytics(agentId, {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          end: new Date()
        })
      ]);
      
      setExecutionHistory(history.executions);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Failed to load agent data:', error);
    }
  };

  const validateStep = (step: number): boolean => {
    const errors: Record<string, string> = {};

    switch (step) {
      case 1: // Basic info
        if (!agentData.name.trim()) errors.name = "Agent name is required";
        if (!agentData.description.trim()) errors.description = "Description is required";
        break;
      case 2: // Model
        if (!agentData.model) errors.model = "AI model selection is required";
        if (!agentData.systemPrompt.trim()) errors.systemPrompt = "System prompt is required";
        break;
      case 3: // Tools
        if (agentData.type !== 'standalone' && agentData.tools.length === 0) {
          errors.tools = "At least one tool is required for this agent type";
        }
        break;
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      if (currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateAgentData = (key: string, value: any) => {
    setAgentData({ ...agentData, [key]: value });
    // Clear validation error for this field
    if (validationErrors[key]) {
      setValidationErrors({ ...validationErrors, [key]: "" });
    }
  };

  const toggleTool = (toolId: string) => {
    const currentTools = [...agentData.tools];
    if (currentTools.includes(toolId)) {
      updateAgentData(
        "tools",
        currentTools.filter((id) => id !== toolId),
      );
    } else {
      updateAgentData("tools", [...currentTools, toolId]);
    }
  };

  const handleSave = async () => {
    if (!validateStep(currentStep)) return;

    setSaveProgress(0);
    setDeploymentStatus('deploying');

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setSaveProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      let savedAgent;
      if (isCreating) {
        savedAgent = await createAgent(agentData, "user_default");
      } else if (selectedAgent) {
        savedAgent = await updateAgent(selectedAgent.id, agentData, "user_default");
      }

      clearInterval(progressInterval);
      setSaveProgress(100);
      setDeploymentStatus('success');

      if (onSave) {
        onSave(savedAgent);
      }

      // Reset after success
      setTimeout(() => {
        setDeploymentStatus('idle');
        setSaveProgress(0);
        if (isCreating) {
          router.push(`/agents/${savedAgent.id}`);
        }
      }, 2000);

    } catch (error) {
      setDeploymentStatus('error');
      console.error('Failed to save agent:', error);
    }
  };

  const handleExecuteAgent = async () => {
    if (!selectedAgent || !executionInput.trim()) return;

    setIsExecuting(true);
    setExecutionError(null);
    setExecutionResult(null);

    try {
      await executeAgent(selectedAgent.id, executionInput, "user_default");
    } catch (error) {
      setIsExecuting(false);
      setExecutionError(error.message);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  return (
    <div className="w-full max-w-7xl mx-auto bg-background p-6 rounded-xl">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              {isCreating ? "Create Agent" : selectedAgent?.name || "Agent Builder"}
            </h1>
            <p className="text-muted-foreground">
              {isCreating 
                ? "Build a powerful AI agent with advanced capabilities" 
                : "Configure and manage your AI agent"}
            </p>
          </div>
          {selectedAgent && (
            <div className="flex items-center space-x-2">
              <Badge variant={selectedAgent.isActive ? "default" : "secondary"}>
                {selectedAgent.isActive ? "Active" : "Inactive"}
              </Badge>
              <Badge variant="outline">v{selectedAgent.version}</Badge>
            </div>
          )}
        </div>
      </div>

      {/* Agent Selection or Creation */}
      {!isCreating && !selectedAgent && (
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Your Agents</h2>
            <Button onClick={() => setIsCreating(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create New Agent
            </Button>
          </div>

          {loading ? (
            <div className="flex justify-center p-8">
              <RefreshCw className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : agents.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {agents.map((agent) => (
                <Card
                  key={agent.id}
                  className="cursor-pointer hover:border-primary transition-colors"
                  onClick={() => selectAgent(agent.id)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <Badge variant={agent.isActive ? "default" : "outline"}>
                        {agent.type}
                      </Badge>
                      <Badge variant="secondary">
                        {agent.model}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg">{agent.name}</CardTitle>
                    <CardDescription className="text-sm">
                      {agent.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Tools: {agent.tools.length}</span>
                      <span>Updated: {new Date(agent.updatedAt).toLocaleDateString()}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="border-dashed border-2 p-8">
              <div className="flex flex-col items-center justify-center text-center">
                <Brain className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Agents Found</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first AI agent to get started with automation.
                </p>
                <Button onClick={() => setIsCreating(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Agent
                </Button>
              </div>
            </Card>
          )}
        </div>
      )}

      {/* Agent Creation/Editing Wizard */}
      {(isCreating || (selectedAgent && !isTesting)) && (
        <>
          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <React.Fragment key={step.id}>
                  <div className="flex flex-col items-center">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div
                            className={`w-12 h-12 rounded-full flex items-center justify-center border-2 ${
                              index === currentStep
                                ? "bg-primary text-primary-foreground border-primary"
                                : index < currentStep
                                ? "bg-primary/20 text-primary border-primary"
                                : "bg-muted text-muted-foreground border-muted"
                            }`}
                          >
                            {index < currentStep ? (
                              <Check className="h-5 w-5" />
                            ) : (
                              step.icon
                            )}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{step.description}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <span
                      className={`mt-2 text-sm text-center ${
                        index === currentStep
                          ? "font-medium text-foreground"
                          : "text-muted-foreground"
                      }`}
                    >
                      {step.title}
                    </span>
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`h-1 w-full max-w-24 ${
                        index < currentStep ? "bg-primary" : "bg-muted"
                      }`}
                    />
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>

          {/* Step Content */}
          <Card className="border shadow-sm">
            <CardContent className="pt-6">
              {/* Step 0: Agent Type */}
              {currentStep === 0 && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Choose Agent Type</h2>
                  <p className="text-muted-foreground mb-6">
                    Select the type of agent that best fits your use case.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {agentTypes.map((type) => (
                      <Card
                        key={type.id}
                        className={`cursor-pointer border-2 transition-all relative ${
                          agentData.type === type.id
                            ? "border-primary bg-primary/5"
                            : "border-border hover:border-primary/50"
                        }`}
                        onClick={() => updateAgentData("type", type.id)}
                      >
                        {type.recommended && (
                          <Badge className="absolute -top-2 -right-2 bg-green-500">
                            Recommended
                          </Badge>
                        )}
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <div className="p-2 rounded-md bg-primary/10">
                              {type.icon}
                            </div>
                            {agentData.type === type.id && (
                              <Check className="h-5 w-5 text-primary" />
                            )}
                          </div>
                          <CardTitle className="text-lg mt-2">
                            {type.name}
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground text-sm mb-3">
                            {type.description}
                          </p>
                          <div className="space-y-1">
                            {type.features.map((feature, idx) => (
                              <div key={idx} className="flex items-center text-xs">
                                <Check className="h-3 w-3 text-green-500 mr-2" />
                                {feature}
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* Step 1: Basic Info */}
              {currentStep === 1 && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Basic Information</h2>
                  <div className="space-y-6">
                    <div>
                      <Label htmlFor="agent-name">Agent Name *</Label>
                      <Input
                        id="agent-name"
                        placeholder="e.g., Customer Support Agent"
                        value={agentData.name}
                        onChange={(e) => updateAgentData("name", e.target.value)}
                        className={validationErrors.name ? "border-red-500" : ""}
                      />
                      {validationErrors.name && (
                        <p className="text-red-500 text-sm mt-1">{validationErrors.name}</p>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="agent-description">Description *</Label>
                      <Textarea
                        id="agent-description"
                        placeholder="Describe what this agent does and how it helps users..."
                        className={`min-h-[100px] ${validationErrors.description ? "border-red-500" : ""}`}
                        value={agentData.description}
                        onChange={(e) => updateAgentData("description", e.target.value)}
                      />
                      {validationErrors.description && (
                        <p className="text-red-500 text-sm mt-1">{validationErrors.description}</p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="agent-active"
                        checked={agentData.isActive}
                        onCheckedChange={(checked) => updateAgentData("isActive", checked)}
                      />
                      <Label htmlFor="agent-active">Active (agent can receive requests)</Label>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: AI Model */}
              {currentStep === 2 && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">AI Model Selection</h2>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 gap-4">
                      {aiModels.map((model) => (
                        <Card
                          key={model.id}
                          className={`cursor-pointer border-2 transition-all ${
                            agentData.model === model.id
                              ? "border-primary bg-primary/5"
                              : "border-border hover:border-primary/50"
                          }`}
                          onClick={() => updateAgentData("model", model.id)}
                        >
                          <CardContent className="p-4">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-2">
                                  <h3 className="font-medium">{model.name}</h3>
                                  {model.recommended && (
                                    <Badge variant="secondary" className="text-xs">
                                      Recommended
                                    </Badge>
                                  )}
                                  {agentData.model === model.id && (
                                    <Check className="h-4 w-4 text-primary" />
                                  )}
                                </div>
                                <p className="text-sm text-muted-foreground mb-2">
                                  Provider: {model.provider}
                                </p>
                                <div className="flex flex-wrap gap-1 mb-2">
                                  {model.capabilities.map((cap) => (
                                    <Badge key={cap} variant="outline" className="text-xs">
                                      {cap}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                              <div className="text-right text-sm">
                                <div className="text-muted-foreground">Context: {formatNumber(model.contextWindow)}</div>
                                <div className="text-muted-foreground">
                                  Cost: {formatCurrency(model.costPer1k.input)}/{formatCurrency(model.costPer1k.output)} per 1K tokens
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>

                    <div className="mt-6">
                      <Label htmlFor="system-prompt">System Prompt *</Label>
                      <Textarea
                        id="system-prompt"
                        placeholder="You are a helpful AI assistant. Your role is to..."
                        className={`min-h-[150px] font-mono text-sm ${validationErrors.systemPrompt ? "border-red-500" : ""}`}
                        value={agentData.systemPrompt}
                        onChange={(e) => updateAgentData("systemPrompt", e.target.value)}
                      />
                      {validationErrors.systemPrompt && (
                        <p className="text-red-500 text-sm mt-1">{validationErrors.systemPrompt}</p>
                      )}
                      <p className="text-sm text-muted-foreground mt-2">
                        Define your agent's personality, capabilities, and behavior guidelines.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Tools */}
              {currentStep === 3 && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Tool Selection</h2>
                  <p className="text-muted-foreground mb-6">
                    Choose tools that your agent can use to accomplish tasks.
                  </p>

                  {validationErrors.tools && (
                    <Alert variant="destructive" className="mb-4">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{validationErrors.tools}</AlertDescription>
                    </Alert>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {availableTools.map((tool) => (
                      <div
                        key={tool.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${
                          agentData.tools.includes(tool.id)
                            ? "border-primary bg-primary/5"
                            : "border-border hover:border-primary/50"
                        }`}
                        onClick={() => toggleTool(tool.id)}
                      >
                        <div className="flex items-start space-x-3">
                          <Checkbox
                            checked={agentData.tools.includes(tool.id)}
                            className="mt-1"
                            onCheckedChange={() => toggleTool(tool.id)}
                          />
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h3 className="font-medium">{tool.name}</h3>
                              {tool.verified && (
                                <Badge variant="secondary" className="text-xs">
                                  <CheckCircle2 className="h-3 w-3 mr-1" />
                                  Verified
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">
                              {tool.description}
                            </p>
                            <div className="flex items-center justify-between">
                              <Badge variant="outline" className="text-xs">
                                {tool.category}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {formatNumber(tool.usage)} uses
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6">
                    <Button variant="outline" className="flex items-center gap-2">
                      <Plus className="h-4 w-4" />
                      Create Custom Tool
                    </Button>
                  </div>
                </div>
              )}

              {/* Step 4: Memory Configuration */}
              {currentStep === 4 && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Memory Configuration</h2>
                  <div className="space-y-6">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="memory-enabled"
                        checked={agentData.memory.enabled}
                        onCheckedChange={(checked) =>
                          updateAgentData("memory", {
                            ...agentData.memory,
                            enabled: checked,
                          })
                        }
                      />
                      <Label htmlFor="memory-enabled">Enable Memory</Label>
                    </div>

                    {agentData.memory.enabled && (
                      <>
                        <div>
                          <Label htmlFor="memory-type">Memory Type</Label>
                          <Select
                            value={agentData.memory.type}
                            onValueChange={(value) =>
                              updateAgentData("memory", {
                                ...agentData.memory,
                                type: value,
                              })
                            }
                          >
                            <SelectTrigger id="memory-type">
                              <SelectValue placeholder="Select memory type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="conversation">
                                Conversation Memory
                              </SelectItem>
                              <SelectItem value="summarization">
                                Summarization Memory
                              </SelectItem>
                              <SelectItem value="vector">
                                Vector Memory
                              </SelectItem>
                              <SelectItem value="none">
                                No Memory
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="memory-tokens">Max Memory Tokens</Label>
                          <div className="flex items-center space-x-4">
                            <Input
                              id="memory-tokens"
                              type="number"
                              value={agentData.memory.maxTokens}
                              onChange={(e) =>
                                updateAgentData("memory", {
                                  ...agentData.memory,
                                  maxTokens: parseInt(e.target.value),
                                })
                              }
                              className="max-w-[200px]"
                            />
                            <span className="text-sm text-muted-foreground">
                              tokens
                            </span>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}

              {/* Step 5: Security & Permissions */}
              {currentStep === 5 && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Security & Permissions</h2>
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Cost Controls</CardTitle>
                        <CardDescription>
                          Set spending limits to control costs
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <Label htmlFor="daily-limit">Daily Spending Limit ($)</Label>
                          <Input
                            id="daily-limit"
                            type="number"
                            step="0.01"
                            value={agentData.providerConfig.costLimits?.daily || 0}
                            onChange={(e) =>
                              updateAgentData("providerConfig", {
                                ...agentData.providerConfig,
                                costLimits: {
                                  ...agentData.providerConfig.costLimits,
                                  daily: parseFloat(e.target.value),
                                },
                              })
                            }
                            className="max-w-[200px]"
                          />
                        </div>
                        <div>
                          <Label htmlFor="monthly-limit">Monthly Spending Limit ($)</Label>
                          <Input
                            id="monthly-limit"
                            type="number"
                            step="0.01"
                            value={agentData.providerConfig.costLimits?.monthly || 0}
                            onChange={(e) =>
                              updateAgentData("providerConfig", {
                                ...agentData.providerConfig,
                                costLimits: {
                                  ...agentData.providerConfig.costLimits,
                                  monthly: parseFloat(e.target.value),
                                },
                              })
                            }
                            className="max-w-[200px]"
                          />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Provider Fallbacks</CardTitle>
                        <CardDescription>
                          Configure backup providers for reliability
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {["anthropic", "google", "mistral"].map((provider) => (
                            <div key={provider} className="flex items-center space-x-2">
                              <Checkbox
                                id={`fallback-${provider}`}
                                checked={agentData.providerConfig.fallbacks.includes(provider)}
                                onCheckedChange={(checked) => {
                                  const fallbacks = checked
                                    ? [...agentData.providerConfig.fallbacks, provider]
                                    : agentData.providerConfig.fallbacks.filter(p => p !== provider);
                                  updateAgentData("providerConfig", {
                                    ...agentData.providerConfig,
                                    fallbacks,
                                  });
                                }}
                              />
                              <Label htmlFor={`fallback-${provider}`} className="capitalize">
                                {provider}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}

              {/* Step 6: Review */}
              {currentStep === 6 && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Review & Deploy</h2>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Agent Overview</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div>
                            <span className="text-sm font-medium">Name:</span>
                            <p>{agentData.name || "Unnamed Agent"}</p>
                          </div>
                          <div>
                            <span className="text-sm font-medium">Type:</span>
                            <p>{agentTypes.find((t) => t.id === agentData.type)?.name}</p>
                          </div>
                          <div>
                            <span className="text-sm font-medium">Model:</span>
                            <p>{aiModels.find((m) => m.id === agentData.model)?.name}</p>
                          </div>
                          <div>
                            <span className="text-sm font-medium">Tools:</span>
                            <p>{agentData.tools.length} selected</p>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Cost Estimate</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div>
                            <span className="text-sm font-medium">Daily Limit:</span>
                            <p>{formatCurrency(agentData.providerConfig.costLimits?.daily || 0)}</p>
                          </div>
                          <div>
                            <span className="text-sm font-medium">Monthly Limit:</span>
                            <p>{formatCurrency(agentData.providerConfig.costLimits?.monthly || 0)}</p>
                          </div>
                          <div>
                            <span className="text-sm font-medium">Estimated per 1K tokens:</span>
                            <p>{formatCurrency(aiModels.find((m) => m.id === agentData.model)?.costPer1k.input || 0)}</p>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {deploymentStatus === 'deploying' && (
                      <Card>
                        <CardContent className="pt-6">
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Deploying agent...</span>
                              <span>{saveProgress}%</span>
                            </div>
                            <Progress value={saveProgress} className="w-full" />
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {deploymentStatus === 'success' && (
                      <Alert>
                        <CheckCircle2 className="h-4 w-4" />
                        <AlertTitle>Success!</AlertTitle>
                        <AlertDescription>
                          Your agent has been successfully deployed and is ready to use.
                        </AlertDescription>
                      </Alert>
                    )}

                    {deploymentStatus === 'error' && (
                      <Alert variant="destructive">
                        <XCircle className="h-4 w-4" />
                        <AlertTitle>Deployment Failed</AlertTitle>
                        <AlertDescription>
                          There was an error deploying your agent. Please try again.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
              )}
            </CardContent>

            <CardFooter className="flex justify-between border-t p-6">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={currentStep === 0 || deploymentStatus === 'deploying'}
              >
                Back
              </Button>

              <div className="flex space-x-2">
                {currentStep < steps.length - 1 ? (
                  <Button 
                    onClick={handleNext} 
                    className="flex items-center gap-1"
                    disabled={deploymentStatus === 'deploying'}
                  >
                    Next <ChevronRight className="h-4 w-4" />
                  </Button>
                ) : (
                  <Button 
                    onClick={handleSave} 
                    className="flex items-center gap-1"
                    disabled={deploymentStatus === 'deploying'}
                  >
                    {deploymentStatus === 'deploying' ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Deploying...
                      </>
                    ) : (
                      <>
                        <Rocket className="h-4 w-4" />
                        {isCreating ? "Create Agent" : "Update Agent"}
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardFooter>
          </Card>
        </>
      )}

      {/* Agent Testing & Management Interface */}
      {selectedAgent && isTesting && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold">{selectedAgent.name}</h2>
              <p className="text-muted-foreground">{selectedAgent.description}</p>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditing(true);
                  setIsTesting(false);
                  setCurrentStep(0);
                }}
              >
                <Settings className="h-4 w-4 mr-2" />
                Edit Agent
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  selectAgent(null);
                  setIsTesting(false);
                }}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Back to List
              </Button>
            </div>
          </div>

          <Tabs defaultValue="test" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="test">Test Agent</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
              <TabsTrigger value="events">Real-time Events</TabsTrigger>
            </TabsList>

            <TabsContent value="test" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Test Your Agent</CardTitle>
                  <CardDescription>
                    Send a message to test your agent's responses
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="test-input">Your Message</Label>
                    <Textarea
                      id="test-input"
                      placeholder="Type your message here..."
                      value={executionInput}
                      onChange={(e) => setExecutionInput(e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>

                  {executionError && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{executionError}</AlertDescription>
                    </Alert>
                  )}

                  {executionResult && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Agent Response</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <p className="whitespace-pre-wrap">{executionResult.result}</p>
                          <div className="flex justify-between text-sm text-muted-foreground">
                            <span>Tokens: {executionResult.tokenUsage?.total || 0}</span>
                            <span>Cost: {formatCurrency(executionResult.cost || 0)}</span>
                            <span>Time: {executionResult.executionTime}ms</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </CardContent>
                <CardFooter>
                  <Button
                    onClick={handleExecuteAgent}
                    disabled={isExecuting || !executionInput.trim()}
                    className="ml-auto"
                  >
                    {isExecuting ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Send Message
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              {analytics && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
                      <Activity className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{formatNumber(analytics.totalExecutions)}</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{(analytics.successRate * 100).toFixed(1)}%</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
                      <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{analytics.avgExecutionTime.toFixed(0)}ms</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{formatCurrency(analytics.totalCost)}</div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Execution History</CardTitle>
                  <CardDescription>Recent agent executions</CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-4">
                      {executionHistory.map((execution, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start mb-2">
                            <Badge variant={execution.status === 'success' ? 'default' : 'destructive'}>
                              {execution.status}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {new Date(execution.started_at).toLocaleString()}
                            </span>
                          </div>
                          <p className="text-sm mb-2">{execution.input}</p>
                          {execution.output && (
                            <p className="text-sm text-muted-foreground">{execution.output}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="events" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Real-time Events</CardTitle>
                  <CardDescription>Live stream of agent events</CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-2">
                      {realTimeEvents.map((event, index) => (
                        <div key={index} className="text-sm p-2 bg-muted rounded">
                          <div className="flex justify-between">
                            <Badge variant="outline" className="text-xs">
                              {event.type}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {new Date(event.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                          <p className="mt-1">{JSON.stringify(event.data, null, 2)}</p>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
}