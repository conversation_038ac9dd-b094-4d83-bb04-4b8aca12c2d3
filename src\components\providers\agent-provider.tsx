"use client";

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { agentAPI, CreateAgentRequest, UpdateAgentRequest, SendMessageRequest, MessageResponse } from '@/lib/api/agents';
import { Agent, Session } from '@/lib/types/agent';
import { useEventBus } from './event-bus-provider';
import { APIError } from '@/lib/api/base';

interface AgentContextType {
  // State
  agents: Agent[];
  sessions: Map<string, Session>;
  loading: boolean;
  error: string | null;
  
  // Agent operations
  createAgent: (agentData: CreateAgentRequest) => Promise<Agent>;
  updateAgent: (id: string, updates: UpdateAgentRequest) => Promise<Agent>;
  deleteAgent: (id: string) => Promise<boolean>;
  duplicateAgent: (id: string, name?: string) => Promise<Agent>;
  
  // Session operations
  startSession: (agentId: string, context?: Record<string, any>) => Promise<Session>;
  endSession: (sessionId: string) => Promise<void>;
  
  // Message operations
  sendMessage: (sessionId: string, message: string, context?: Record<string, any>) => Promise<MessageResponse>;
  sendMessageStream: (sessionId: string, message: string, onChunk: (chunk: any) => void) => Promise<void>;
  getSessionMessages: (sessionId: string) => Promise<MessageResponse[]>;
  
  // Testing and validation
  testAgent: (id: string, message: string) => Promise<MessageResponse>;
  validateAgentConfig: (config: CreateAgentRequest) => Promise<{ valid: boolean; errors?: string[]; warnings?: string[] }>;
  
  // Deployment
  deployAgent: (id: string, environment: 'development' | 'staging' | 'production') => Promise<{ deploymentId: string; status: string; url?: string }>;
  
  // Utility functions
  refreshAgents: () => Promise<void>;
  clearError: () => void;
}

const AgentContext = createContext<AgentContextType | null>(null);

export function AgentProvider({ children }: { children: React.ReactNode }) {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [sessions, setSessions] = useState<Map<string, Session>>(new Map());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { subscribe } = useEventBus();

  // Load agents on mount
  useEffect(() => {
    refreshAgents();
  }, []);

  // Subscribe to real-time events
  useEffect(() => {
    const unsubscribeAgent = subscribe('agent-events', (message) => {
      switch (message.type) {
        case 'agent_created':
        case 'agent_updated':
        case 'agent_deleted':
          refreshAgents();
          break;
        case 'session_start':
          if (message.data.session) {
            setSessions(prev => new Map(prev.set(message.data.session.id, message.data.session)));
          }
          break;
        case 'session_end':
          setSessions(prev => {
            const newSessions = new Map(prev);
            const session = newSessions.get(message.data.sessionId);
            if (session) {
              session.status = 'completed';
              session.endedAt = new Date();
            }
            return newSessions;
          });
          break;
      }
    });

    return () => {
      unsubscribeAgent();
    };
  }, [subscribe]);

  const handleError = useCallback((err: any) => {
    const errorMessage = err instanceof Error ? err.message : 
                        (err as APIError)?.message || 'An unexpected error occurred';
    setError(errorMessage);
    console.error('Agent Provider Error:', err);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refreshAgents = useCallback(async () => {
    try {
      setLoading(true);
      clearError();
      
      const response = await agentAPI.listAgents({
        limit: 100,
        sortBy: 'updatedAt',
        sortOrder: 'desc'
      });
      
      if (response.success && response.data) {
        setAgents(response.data);
      } else {
        throw new Error(response.error || 'Failed to load agents');
      }
    } catch (err) {
      handleError(err);
    } finally {
      setLoading(false);
    }
  }, [handleError, clearError]);

  const createAgent = useCallback(async (agentData: CreateAgentRequest): Promise<Agent> => {
    try {
      setLoading(true);
      clearError();
      
      const response = await agentAPI.createAgent(agentData);
      
      if (response.success && response.data) {
        setAgents(prev => [response.data!, ...prev]);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create agent');
      }
    } catch (err) {
      handleError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [handleError, clearError]);

  const updateAgent = useCallback(async (id: string, updates: UpdateAgentRequest): Promise<Agent> => {
    try {
      setLoading(true);
      clearError();
      
      const response = await agentAPI.updateAgent(id, updates);
      
      if (response.success && response.data) {
        setAgents(prev => prev.map(agent => 
          agent.id === id ? response.data! : agent
        ));
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to update agent');
      }
    } catch (err) {
      handleError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [handleError, clearError]);

  const deleteAgent = useCallback(async (id: string): Promise<boolean> => {
    try {
      setLoading(true);
      clearError();
      
      const response = await agentAPI.deleteAgent(id);
      
      if (response.success) {
        setAgents(prev => prev.filter(agent => agent.id !== id));
        return true;
      } else {
        throw new Error(response.error || 'Failed to delete agent');
      }
    } catch (err) {
      handleError(err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [handleError, clearError]);

  const duplicateAgent = useCallback(async (id: string, name?: string): Promise<Agent> => {
    try {
      setLoading(true);
      clearError();
      
      const response = await agentAPI.duplicateAgent(id, name);
      
      if (response.success && response.data) {
        setAgents(prev => [response.data!, ...prev]);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to duplicate agent');
      }
    } catch (err) {
      handleError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [handleError, clearError]);

  const startSession = useCallback(async (agentId: string, context?: Record<string, any>): Promise<Session> => {
    try {
      clearError();
      
      const response = await agentAPI.createSession({ agentId, context });
      
      if (response.success && response.data) {
        setSessions(prev => new Map(prev.set(response.data!.id, response.data!)));
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to start session');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [handleError, clearError]);

  const endSession = useCallback(async (sessionId: string): Promise<void> => {
    try {
      clearError();
      
      const response = await agentAPI.endSession(sessionId);
      
      if (response.success) {
        setSessions(prev => {
          const newSessions = new Map(prev);
          const session = newSessions.get(sessionId);
          if (session) {
            session.status = 'completed';
            session.endedAt = new Date();
          }
          return newSessions;
        });
      } else {
        throw new Error(response.error || 'Failed to end session');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [handleError, clearError]);

  const sendMessage = useCallback(async (
    sessionId: string, 
    message: string, 
    context?: Record<string, any>
  ): Promise<MessageResponse> => {
    try {
      clearError();
      
      const response = await agentAPI.sendMessage({ sessionId, message, context });
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to send message');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [handleError, clearError]);

  const sendMessageStream = useCallback(async (
    sessionId: string, 
    message: string, 
    onChunk: (chunk: any) => void
  ): Promise<void> => {
    try {
      clearError();
      
      await agentAPI.sendMessageStream({ sessionId, message }, onChunk);
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [handleError, clearError]);

  const getSessionMessages = useCallback(async (sessionId: string): Promise<MessageResponse[]> => {
    try {
      clearError();
      
      const response = await agentAPI.getSessionMessages(sessionId, { limit: 100 });
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to get session messages');
      }
    } catch (err) {
      handleError(err);
      return [];
    }
  }, [handleError, clearError]);

  const testAgent = useCallback(async (id: string, message: string): Promise<MessageResponse> => {
    try {
      clearError();
      
      const response = await agentAPI.testAgent(id, message);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to test agent');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [handleError, clearError]);

  const validateAgentConfig = useCallback(async (config: CreateAgentRequest) => {
    try {
      clearError();
      
      const response = await agentAPI.validateAgentConfig(config);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to validate agent config');
      }
    } catch (err) {
      handleError(err);
      return { valid: false, errors: ['Validation failed'] };
    }
  }, [handleError, clearError]);

  const deployAgent = useCallback(async (
    id: string, 
    environment: 'development' | 'staging' | 'production'
  ) => {
    try {
      clearError();
      
      const response = await agentAPI.deployAgent(id, environment);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to deploy agent');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [handleError, clearError]);

  const contextValue: AgentContextType = {
    // State
    agents,
    sessions,
    loading,
    error,
    
    // Agent operations
    createAgent,
    updateAgent,
    deleteAgent,
    duplicateAgent,
    
    // Session operations
    startSession,
    endSession,
    
    // Message operations
    sendMessage,
    sendMessageStream,
    getSessionMessages,
    
    // Testing and validation
    testAgent,
    validateAgentConfig,
    
    // Deployment
    deployAgent,
    
    // Utility functions
    refreshAgents,
    clearError
  };

  return (
    <AgentContext.Provider value={contextValue}>
      {children}
    </AgentContext.Provider>
  );
}

export function useAgent() {
  const context = useContext(AgentContext);
  if (!context) {
    throw new Error('useAgent must be used within an AgentProvider');
  }
  return context;
}