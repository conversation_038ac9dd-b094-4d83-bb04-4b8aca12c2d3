import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { ProviderService } from './provider.service';
import { CreateProviderDto, UpdateProviderDto } from './dto/provider.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Providers')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('providers')
export class ProviderController {
  constructor(private providerService: ProviderService) {}

  @Post()
  @ApiOperation({ summary: 'Create new provider' })
  create(@Request() req, @Body() dto: CreateProviderDto) {
    return this.providerService.create(req.user.organizationId, dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all providers' })
  findAll(@Request() req) {
    return this.providerService.findAll(req.user.organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get provider by ID' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.providerService.findOne(id, req.user.organizationId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update provider' })
  update(@Param('id') id: string, @Request() req, @Body() dto: UpdateProviderDto) {
    return this.providerService.update(id, req.user.organizationId, dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete provider' })
  remove(@Param('id') id: string, @Request() req) {
    return this.providerService.remove(id, req.user.organizationId);
  }

  @Post(':id/test')
  @ApiOperation({ summary: 'Test provider connection' })
  testConnection(@Param('id') id: string, @Request() req) {
    return this.providerService.testConnection(id, req.user.organizationId);
  }

  @Get(':id/models')
  @ApiOperation({ summary: 'Get available models' })
  getModels(@Param('id') id: string, @Request() req) {
    return this.providerService.getModels(id, req.user.organizationId);
  }
}