import * as fs from 'fs';
import * as path from 'path';

export interface ProcessedDocument {
  id: string;
  title: string;
  content: string;
  chunks: DocumentChunk[];
  metadata: {
    fileType: string;
    size: number;
    wordCount: number;
  };
}

export interface DocumentChunk {
  id: string;
  content: string;
  embedding?: number[];
  metadata: {
    chunkIndex: number;
    startChar: number;
    endChar: number;
  };
}

export class DocumentProcessor {
  private readonly maxChunkSize = 1000;
  private readonly chunkOverlap = 200;

  async processDocument(filePath: string, fileType: string): Promise<ProcessedDocument> {
    const content = await this.extractContent(filePath, fileType);
    const title = this.extractTitle(content, path.basename(filePath));
    const chunks = this.chunkDocument(content);
    
    const stats = fs.statSync(filePath);
    
    return {
      id: this.generateId(),
      title,
      content,
      chunks,
      metadata: {
        fileType,
        size: stats.size,
        wordCount: this.countWords(content),
      },
    };
  }

  private async extractContent(filePath: string, fileType: string): Promise<string> {
    switch (fileType.toLowerCase()) {
      case 'txt':
        return fs.readFileSync(filePath, 'utf-8');
      case 'pdf':
        return await this.extractPdfContent(filePath);
      default:
        throw new Error(`Unsupported file type: ${fileType}`);
    }
  }

  private async extractPdfContent(filePath: string): Promise<string> {
    const pdf = require('pdf-parse');
    const dataBuffer = fs.readFileSync(filePath);
    const data = await pdf(dataBuffer);
    return data.text;
  }

  private extractTitle(content: string, filename: string): string {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length > 0) {
      const firstLine = lines[0].trim();
      if (firstLine.length < 100 && firstLine.length > 5) {
        return firstLine;
      }
    }
    return path.parse(filename).name;
  }

  private chunkDocument(content: string): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];
    const sentences = content.split(/[.!?]+/).map(s => s.trim()).filter(s => s.length > 0);
    
    let currentChunk = '';
    let currentChunkStart = 0;
    let chunkIndex = 0;

    for (const sentence of sentences) {
      const potentialChunk = currentChunk + (currentChunk ? ' ' : '') + sentence;

      if (potentialChunk.length > this.maxChunkSize && currentChunk) {
        chunks.push({
          id: this.generateId(),
          content: currentChunk.trim(),
          metadata: {
            chunkIndex,
            startChar: currentChunkStart,
            endChar: currentChunkStart + currentChunk.length,
          },
        });

        const overlapStart = Math.max(0, currentChunk.length - this.chunkOverlap);
        currentChunk = currentChunk.substring(overlapStart) + ' ' + sentence;
        currentChunkStart += overlapStart;
        chunkIndex++;
      } else {
        currentChunk = potentialChunk;
      }
    }

    if (currentChunk.trim()) {
      chunks.push({
        id: this.generateId(),
        content: currentChunk.trim(),
        metadata: {
          chunkIndex,
          startChar: currentChunkStart,
          endChar: currentChunkStart + currentChunk.length,
        },
      });
    }

    return chunks;
  }

  private countWords(text: string): number {
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  private generateId(): string {
    return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async generateEmbeddings(chunks: DocumentChunk[]): Promise<DocumentChunk[]> {
    for (const chunk of chunks) {
      try {
        const response = await fetch('https://api.openai.com/v1/embeddings', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'text-embedding-3-small',
            input: chunk.content,
          }),
        });

        const data = await response.json();
        chunk.embedding = data.data[0].embedding;
      } catch (error) {
        console.error('Failed to generate embedding for chunk:', error);
      }
    }

    return chunks;
  }
}