import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth';

export const GET = withAuth(async (request: NextRequest) => {
  const authContext = (request as any).auth;

  return NextResponse.json({
    success: true,
    data: {
      user: authContext.user,
      organization: authContext.organization,
      role: authContext.role,
      permissions: authContext.permissions
    }
  });
});