generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Organization {
  id           String           @id @default(cuid())
  name         String
  slug         String           @unique
  settings     Json
  customDomain String?
  branding     Json
  features     String[]
  quotas       Json
  subscription SubscriptionTier
  isActive     Boolean          @default(true)
  users        User[]
  agents       Agent[]
  workflows    Workflow[]
  tools        Tool[]
  providers    Provider[]
  auditLogs    AuditLog[]
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  @@map("organizations")
}

model User {
  id             String     @id @default(cuid())
  organizationId String
  email          String     @unique
  passwordHash   String?
  profile        Json
  roles          UserRole[]
  mfaEnabled     Boolean    @default(false)
  mfaSecret      String?
  lastLogin      DateTime?
  organization   Organization @relation(fields: [organizationId], references: [id])
  sessions       Session[]
  auditLogs      AuditLog[]
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt

  @@map("users")
}

model Role {
  id          String     @id @default(cuid())
  name        String
  level       RoleLevel
  permissions String[]
  isSystem    Boolean    @default(false)
  userRoles   UserRole[]

  @@map("roles")
}

model UserRole {
  id         String   @id @default(cuid())
  userId     String
  roleId     String
  scope      Json?
  user       User     @relation(fields: [userId], references: [id])
  role       Role     @relation(fields: [roleId], references: [id])
  assignedAt DateTime @default(now())

  @@unique([userId, roleId])
  @@map("user_roles")
}

model Session {
  id           String   @id @default(cuid())
  userId       String
  token        String   @unique
  refreshToken String   @unique
  deviceInfo   Json
  ipAddress    String
  expiresAt    DateTime
  user         User     @relation(fields: [userId], references: [id])
  createdAt    DateTime @default(now())

  @@map("sessions")
}

model Agent {
  id             String       @id @default(cuid())
  organizationId String
  name           String
  description    String?
  model          String
  systemPrompt   String
  tools          String[]
  memory         Json
  settings       Json
  isActive       Boolean      @default(true)
  organization   Organization @relation(fields: [organizationId], references: [id])
  workflows      WorkflowNode[]
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@map("agents")
}

model Workflow {
  id             String         @id @default(cuid())
  organizationId String
  name           String
  description    String?
  nodes          WorkflowNode[]
  edges          WorkflowEdge[]
  settings       Json
  isActive       Boolean        @default(true)
  organization   Organization   @relation(fields: [organizationId], references: [id])
  executions     WorkflowExecution[]
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  @@map("workflows")
}

model WorkflowNode {
  id         String     @id @default(cuid())
  workflowId String
  agentId    String?
  toolId     String?
  type       NodeType
  position   Json
  config     Json
  workflow   Workflow   @relation(fields: [workflowId], references: [id])
  agent      Agent?     @relation(fields: [agentId], references: [id])
  tool       Tool?      @relation(fields: [toolId], references: [id])
  sourceEdges WorkflowEdge[] @relation("SourceNode")
  targetEdges WorkflowEdge[] @relation("TargetNode")

  @@map("workflow_nodes")
}

model WorkflowEdge {
  id           String       @id @default(cuid())
  workflowId   String
  sourceNodeId String
  targetNodeId String
  condition    Json?
  workflow     Workflow     @relation(fields: [workflowId], references: [id])
  sourceNode   WorkflowNode @relation("SourceNode", fields: [sourceNodeId], references: [id])
  targetNode   WorkflowNode @relation("TargetNode", fields: [targetNodeId], references: [id])

  @@map("workflow_edges")
}

model WorkflowExecution {
  id         String            @id @default(cuid())
  workflowId String
  status     ExecutionStatus
  input      Json
  output     Json?
  logs       Json[]
  startedAt  DateTime          @default(now())
  completedAt DateTime?
  workflow   Workflow          @relation(fields: [workflowId], references: [id])

  @@map("workflow_executions")
}

model Tool {
  id             String         @id @default(cuid())
  organizationId String
  name           String
  description    String?
  type           ToolType
  config         Json
  schema         Json
  isActive       Boolean        @default(true)
  organization   Organization   @relation(fields: [organizationId], references: [id])
  workflows      WorkflowNode[]
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  @@map("tools")
}

model Provider {
  id             String       @id @default(cuid())
  organizationId String
  name           String
  type           ProviderType
  config         Json
  credentials    Json
  isActive       Boolean      @default(true)
  organization   Organization @relation(fields: [organizationId], references: [id])
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@map("providers")
}

model AuditLog {
  id             String       @id @default(cuid())
  organizationId String
  userId         String?
  action         String
  resource       String
  resourceId     String?
  details        Json
  ipAddress      String
  userAgent      String
  timestamp      DateTime     @default(now())
  organization   Organization @relation(fields: [organizationId], references: [id])
  user           User?        @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

enum RoleLevel {
  SUPER_ADMIN
  ORG_ADMIN
  DEVELOPER
  VIEWER
}

enum SubscriptionTier {
  FREE
  STARTER
  PROFESSIONAL
  ENTERPRISE
}

enum NodeType {
  AGENT
  TOOL
  CONDITION
  TRIGGER
  ACTION
}

enum ExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

enum ToolType {
  API
  WEBHOOK
  DATABASE
  FILE_SYSTEM
  CUSTOM
}

enum ProviderType {
  OPENAI
  CLAUDE
  GEMINI
  MISTRAL
  GROQ
  OLLAMA
}