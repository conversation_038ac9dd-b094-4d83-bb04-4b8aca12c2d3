"use client";

import React, { useState, use<PERSON><PERSON>back, useRef, useEffect } from "react";
import ReactFlow, {
  ReactFlowProvider,
  Background,
  Controls,
  MiniMap,
  addEdge,
  useNodesState,
  useEdgesState,
  Node,
  Edge,
  Connection,
  NodeTypes,
  EdgeTypes,
  Panel,
  useReactFlow,
  NodeMouseHandler,
  OnConnectStartParams,
  NodeDragHandler,
} from "reactflow";
import "reactflow/dist/style.css";

import {
  PlusCircle,
  Save,
  Play,
  Settings,
  Trash2,
  ZoomIn,
  ZoomOut,
  Maximize2,
  Grid3X3,
  LayoutGrid,
  List,
  Download,
  Upload,
  Check,
  X,
  Copy,
  Undo,
  Redo,
  Search,
  Filter,
  MoreHorizontal,
} from "lucide-react";

import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "../ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import {
  Tooltip,
  Toolt<PERSON>Content,
  <PERSON>lt<PERSON><PERSON>rovider,
  TooltipTrigger,
} from "../ui/tooltip";
import { Input } from "../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Separator } from "../ui/separator";
import { Badge } from "../ui/badge";
import { Switch } from "../ui/switch";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { ScrollArea } from "../ui/scroll-area";
import { useWorkflow } from "../providers/workflow-provider";
import { workflowManager, WorkflowNode as WFNode, WorkflowConnection } from "@/lib/workflow-manager";

// Custom Node Components
const AgentNode = ({ data, selected }: { data: any; selected: boolean }) => (
  <div className={`p-3 rounded-lg border-2 ${selected ? 'border-primary' : 'border-border'} bg-card shadow-sm w-[200px]`}>
    <div className="flex items-center justify-between mb-2">
      <div className="flex items-center">
        <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-2">
          <data.icon className="h-4 w-4 text-primary" />
        </div>
        <div className="font-medium truncate">{data.name}</div>
      </div>
      <Badge variant={data.status === 'active' ? 'default' : 'outline'} className="text-xs">
        {data.status}
      </Badge>
    </div>
    <div className="text-xs text-muted-foreground truncate mb-2">{data.description}</div>
    <div className="flex justify-between text-xs">
      <div>Model: <span className="font-medium">{data.model}</span></div>
      <div>{data.tools?.length || 0} tools</div>
    </div>
    <div className="mt-2 flex justify-between">
      <div className="flex -space-x-2">
        {data.inputs?.map((input: string, i: number) => (
          <div key={`input-${i}`} className="w-4 h-4 rounded-full bg-blue-500 border border-background" />
        ))}
      </div>
      <div className="flex -space-x-2">
        {data.outputs?.map((output: string, i: number) => (
          <div key={`output-${i}`} className="w-4 h-4 rounded-full bg-green-500 border border-background" />
        ))}
      </div>
    </div>
  </div>
);

const ToolNode = ({ data, selected }: { data: any; selected: boolean }) => (
  <div className={`p-3 rounded-lg border-2 ${selected ? 'border-primary' : 'border-border'} bg-card shadow-sm w-[180px]`}>
    <div className="flex items-center justify-between mb-2">
      <div className="flex items-center">
        <div className="w-8 h-8 rounded-full bg-amber-500/20 flex items-center justify-center mr-2">
          <data.icon className="h-4 w-4 text-amber-500" />
        </div>
        <div className="font-medium truncate">{data.name}</div>
      </div>
    </div>
    <div className="text-xs text-muted-foreground truncate mb-2">{data.description}</div>
    <div className="flex justify-between text-xs">
      <div>Type: <span className="font-medium">{data.toolType}</span></div>
    </div>
    <div className="mt-2 flex justify-between">
      <div className="flex -space-x-2">
        {data.inputs?.map((input: string, i: number) => (
          <div key={`input-${i}`} className="w-4 h-4 rounded-full bg-blue-500 border border-background" />
        ))}
      </div>
      <div className="flex -space-x-2">
        {data.outputs?.map((output: string, i: number) => (
          <div key={`output-${i}`} className="w-4 h-4 rounded-full bg-green-500 border border-background" />
        ))}
      </div>
    </div>
  </div>
);

const ConditionNode = ({ data, selected }: { data: any; selected: boolean }) => (
  <div className={`p-3 rounded-lg border-2 ${selected ? 'border-primary' : 'border-border'} bg-card shadow-sm w-[180px]`}>
    <div className="flex items-center justify-between mb-2">
      <div className="flex items-center">
        <div className="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center mr-2">
          <data.icon className="h-4 w-4 text-purple-500" />
        </div>
        <div className="font-medium truncate">{data.name}</div>
      </div>
    </div>
    <div className="text-xs text-muted-foreground truncate mb-2">{data.description}</div>
    <div className="text-xs bg-muted p-1 rounded font-mono">{data.condition}</div>
    <div className="mt-2 flex justify-between">
      <div className="flex -space-x-2">
        {data.inputs?.map((input: string, i: number) => (
          <div key={`input-${i}`} className="w-4 h-4 rounded-full bg-blue-500 border border-background" />
        ))}
      </div>
      <div className="flex -space-x-2">
        {data.outputs?.map((output: string, i: number) => (
          <div key={`output-${i}`} className="w-4 h-4 rounded-full bg-green-500 border border-background" />
        ))}
      </div>
    </div>
  </div>
);

const TriggerNode = ({ data, selected }: { data: any; selected: boolean }) => (
  <div className={`p-3 rounded-lg border-2 ${selected ? 'border-primary' : 'border-border'} bg-card shadow-sm w-[180px]`}>
    <div className="flex items-center justify-between mb-2">
      <div className="flex items-center">
        <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center mr-2">
          <data.icon className="h-4 w-4 text-green-500" />
        </div>
        <div className="font-medium truncate">{data.name}</div>
      </div>
      <Badge variant="outline" className="text-xs">
        {data.triggerType}
      </Badge>
    </div>
    <div className="text-xs text-muted-foreground truncate mb-2">{data.description}</div>
    <div className="mt-2 flex justify-end">
      <div className="flex -space-x-2">
        {data.outputs?.map((output: string, i: number) => (
          <div key={`output-${i}`} className="w-4 h-4 rounded-full bg-green-500 border border-background" />
        ))}
      </div>
    </div>
  </div>
);

const OutputNode = ({ data, selected }: { data: any; selected: boolean }) => (
  <div className={`p-3 rounded-lg border-2 ${selected ? 'border-primary' : 'border-border'} bg-card shadow-sm w-[180px]`}>
    <div className="flex items-center justify-between mb-2">
      <div className="flex items-center">
        <div className="w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center mr-2">
          <data.icon className="h-4 w-4 text-red-500" />
        </div>
        <div className="font-medium truncate">{data.name}</div>
      </div>
    </div>
    <div className="text-xs text-muted-foreground truncate mb-2">{data.description}</div>
    <div className="mt-2 flex justify-start">
      <div className="flex -space-x-2">
        {data.inputs?.map((input: string, i: number) => (
          <div key={`input-${i}`} className="w-4 h-4 rounded-full bg-blue-500 border border-background" />
        ))}
      </div>
    </div>
  </div>
);

// Custom Edge
const CustomEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
}: any) => {
  const edgePath = `M${sourceX},${sourceY} C${sourceX + 50},${sourceY} ${targetX - 50},${targetY} ${targetX},${targetY}`;
  const labelX = (sourceX + targetX) / 2;
  const labelY = (sourceY + targetY) / 2;

  return (
    <>
      <path
        id={id}
        style={style}
        className="react-flow__edge-path stroke-muted-foreground stroke-2"
        d={edgePath}
        markerEnd={markerEnd}
      />
      {data?.label && (
        <foreignObject
          width={80}
          height={24}
          x={labelX - 40}
          y={labelY - 12}
          className="overflow-visible"
        >
          <div className="flex items-center justify-center bg-background border rounded px-2 py-1 text-xs shadow-sm">
            {data.label}
          </div>
        </foreignObject>
      )}
    </>
  );
};

// Node type mapping
const nodeTypes: NodeTypes = {
  agent: AgentNode,
  tool: ToolNode,
  condition: ConditionNode,
  trigger: TriggerNode,
  output: OutputNode,
};

const edgeTypes: EdgeTypes = {
  custom: CustomEdge,
};

// Icons for node types
const nodeIcons = {
  agent: () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>,
  tool: () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/></svg>,
  condition: () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="m8 3 4 8 5-5 5 15H2L8 3z"/></svg>,
  trigger: () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"/></svg>,
  output: () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="17 8 12 3 7 8"/><line x1="12" y1="3" x2="12" y2="15"/></svg>,
};

// Node palette items
const nodePaletteItems = [
  {
    type: "agent",
    name: "AI Agent",
    description: "An AI agent that can process requests",
    icon: nodeIcons.agent,
    inputs: ["input"],
    outputs: ["output", "error"],
  },
  {
    type: "tool",
    name: "Tool",
    description: "Execute a specific function or API call",
    icon: nodeIcons.tool,
    inputs: ["input"],
    outputs: ["output", "error"],
  },
  {
    type: "condition",
    name: "Condition",
    description: "Branch based on conditions",
    icon: nodeIcons.condition,
    inputs: ["input"],
    outputs: ["true", "false"],
  },
  {
    type: "trigger",
    name: "Trigger",
    description: "Workflow starting point",
    icon: nodeIcons.trigger,
    inputs: [],
    outputs: ["output"],
  },
  {
    type: "output",
    name: "Output",
    description: "Workflow endpoint",
    icon: nodeIcons.output,
    inputs: ["input"],
    outputs: [],
  },
];

// Convert from our workflow model to ReactFlow nodes and edges
const convertToReactFlow = (
  nodes: WFNode[],
  connections: WorkflowConnection[]
): { nodes: Node[]; edges: Edge[] } => {
  const rfNodes = nodes.map((node) => ({
    id: node.id,
    type: node.type,
    position: node.position,
    data: {
      ...node.data,
      name: node.name,
      inputs: node.inputs,
      outputs: node.outputs,
      icon: nodeIcons[node.type as keyof typeof nodeIcons],
    },
  }));

  const rfEdges = connections.map((conn) => ({
    id: conn.id,
    source: conn.sourceNodeId,
    target: conn.targetNodeId,
    sourceHandle: conn.sourceOutput,
    targetHandle: conn.targetInput,
    type: "custom",
    data: {
      label: conn.condition,
    },
  }));

  return { nodes: rfNodes, edges: rfEdges };
};

// Convert from ReactFlow back to our workflow model
const convertFromReactFlow = (
  nodes: Node[],
  edges: Edge[]
): { nodes: WFNode[]; connections: WorkflowConnection[] } => {
  const wfNodes = nodes.map((node) => ({
    id: node.id,
    type: node.type as WFNode["type"],
    name: node.data.name,
    position: node.position,
    data: {
      ...node.data,
      agentId: node.data.agentId,
      condition: node.data.condition,
      action: node.data.action,
      parameters: node.data.parameters,
      config: node.data.config,
    },
    inputs: node.data.inputs || [],
    outputs: node.data.outputs || [],
  }));

  const wfConnections = edges.map((edge) => ({
    id: edge.id,
    sourceNodeId: edge.source,
    sourceOutput: edge.sourceHandle || "output",
    targetNodeId: edge.target,
    targetInput: edge.targetHandle || "input",
    condition: edge.data?.label,
  }));

  return { nodes: wfNodes, connections: wfConnections };
};

interface WorkflowDesignerProps {
  initialNodes?: WFNode[];
  initialConnections?: WorkflowConnection[];
  onSave?: (nodes: WFNode[], connections: WorkflowConnection[]) => void;
  onExecute?: () => void;
}

const WorkflowDesignerInner = ({
  initialNodes = [],
  initialConnections = [],
  onSave = () => {},
  onExecute = () => {},
}: WorkflowDesignerProps) => {
  const { templates } = useWorkflow();
  const reactFlowInstance = useReactFlow();
  const [workflowName, setWorkflowName] = useState<string>("New Workflow");
  const [workflowDescription, setWorkflowDescription] = useState<string>("");
  const [workflowStatus, setWorkflowStatus] = useState<string>("draft");
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [showMiniMap, setShowMiniMap] = useState<boolean>(true);
  const [showGrid, setShowGrid] = useState<boolean>(true);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [isExecuting, setIsExecuting] = useState<boolean>(false);
  const [executionPath, setExecutionPath] = useState<string[]>([]);
  const [nodeConfigOpen, setNodeConfigOpen] = useState<boolean>(false);
  const [edgeConfigOpen, setEdgeConfigOpen] = useState<boolean>(false);
  const [undoStack, setUndoStack] = useState<any[]>([]);
  const [redoStack, setRedoStack] = useState<any[]>([]);

  // Convert initial data to ReactFlow format
  const initialRFState = convertToReactFlow(initialNodes, initialConnections);
  const [nodes, setNodes, onNodesChange] = useNodesState(initialRFState.nodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialRFState.edges);

  // Handle node selection
  const onNodeClick: NodeMouseHandler = useCallback((_, node) => {
    setSelectedNode(node);
    setSelectedEdge(null);
    setNodeConfigOpen(true);
  }, []);

  // Handle edge selection
  const onEdgeClick = useCallback((_, edge) => {
    setSelectedEdge(edge);
    setSelectedNode(null);
    setEdgeConfigOpen(true);
  }, []);

  // Handle connection between nodes
  const onConnect = useCallback(
    (params: Connection) => {
      // Save current state for undo
      setUndoStack((stack) => [...stack, { nodes, edges }]);
      setRedoStack([]);

      // Create a unique ID for the new edge
      const id = `edge_${Date.now()}`;
      setEdges((eds) =>
        addEdge(
          {
            ...params,
            id,
            type: "custom",
            data: { label: "" },
          },
          eds
        )
      );
    },
    [nodes, edges, setEdges]
  );

  // Handle node drag
  const onNodeDragStop: NodeDragHandler = useCallback(
    (_, node) => {
      // Update the selected node if it's being dragged
      if (selectedNode && selectedNode.id === node.id) {
        setSelectedNode(node);
      }
    },
    [selectedNode]
  );

  // Handle adding a new node
  const handleAddNode = useCallback(
    (type: string, nodeData: any) => {
      // Save current state for undo
      setUndoStack((stack) => [...stack, { nodes, edges }]);
      setRedoStack([]);

      const newNode: Node = {
        id: `${type}_${Date.now()}`,
        type,
        position: {
          x: Math.random() * 300 + 50,
          y: Math.random() * 300 + 50,
        },
        data: {
          name: `New ${type}`,
          description: nodeData.description,
          icon: nodeData.icon,
          inputs: nodeData.inputs,
          outputs: nodeData.outputs,
          status: "draft",
          model: type === "agent" ? "gpt-4" : undefined,
          toolType: type === "tool" ? "api" : undefined,
          condition: type === "condition" ? "value > 0" : undefined,
          triggerType: type === "trigger" ? "webhook" : undefined,
        },
      };

      setNodes((nds) => nds.concat(newNode));
      setSelectedNode(newNode);
      setNodeConfigOpen(true);
    },
    [nodes, edges, setNodes]
  );

  // Handle updating node data
  const handleUpdateNodeData = useCallback(
    (nodeId: string, data: any) => {
      setNodes((nds) =>
        nds.map((node) => {
          if (node.id === nodeId) {
            return {
              ...node,
              data: {
                ...node.data,
                ...data,
              },
            };
          }
          return node;
        })
      );

      // Update selected node if it's the one being edited
      if (selectedNode && selectedNode.id === nodeId) {
        setSelectedNode({
          ...selectedNode,
          data: {
            ...selectedNode.data,
            ...data,
          },
        });
      }
    },
    [selectedNode, setNodes]
  );

  // Handle updating edge data
  const handleUpdateEdgeData = useCallback(
    (edgeId: string, data: any) => {
      setEdges((eds) =>
        eds.map((edge) => {
          if (edge.id === edgeId) {
            return {
              ...edge,
              data: {
                ...edge.data,
                ...data,
              },
            };
          }
          return edge;
        })
      );

      // Update selected edge if it's the one being edited
      if (selectedEdge && selectedEdge.id === edgeId) {
        setSelectedEdge({
          ...selectedEdge,
          data: {
            ...selectedEdge.data,
            ...data,
          },
        });
      }
    },
    [selectedEdge, setEdges]
  );

  // Handle deleting a node
  const handleDeleteNode = useCallback(
    (nodeId: string) => {
      // Save current state for undo
      setUndoStack((stack) => [...stack, { nodes, edges }]);
      setRedoStack([]);

      // Remove connected edges
      setEdges((eds) => eds.filter((e) => e.source !== nodeId && e.target !== nodeId));
      
      // Remove the node
      setNodes((nds) => nds.filter((n) => n.id !== nodeId));
      
      // Clear selection if the deleted node was selected
      if (selectedNode && selectedNode.id === nodeId) {
        setSelectedNode(null);
        setNodeConfigOpen(false);
      }
    },
    [nodes, edges, selectedNode, setNodes, setEdges]
  );

  // Handle deleting an edge
  const handleDeleteEdge = useCallback(
    (edgeId: string) => {
      // Save current state for undo
      setUndoStack((stack) => [...stack, { nodes, edges }]);
      setRedoStack([]);

      setEdges((eds) => eds.filter((e) => e.id !== edgeId));
      
      // Clear selection if the deleted edge was selected
      if (selectedEdge && selectedEdge.id === edgeId) {
        setSelectedEdge(null);
        setEdgeConfigOpen(false);
      }
    },
    [edges, selectedEdge, setEdges]
  );

  // Handle saving the workflow
  const handleSave = useCallback(() => {
    const { nodes: wfNodes, connections: wfConnections } = convertFromReactFlow(nodes, edges);
    onSave(wfNodes, wfConnections);
  }, [nodes, edges, onSave]);

  // Handle executing the workflow
  const handleExecute = useCallback(() => {
    setIsExecuting(true);
    
    // Find trigger nodes
    const triggerNodes = nodes.filter((node) => node.type === "trigger");
    if (triggerNodes.length > 0) {
      setExecutionPath([triggerNodes[0].id]);
      
      // Simulate execution path
      setTimeout(() => {
        const simulateExecution = (nodeId: string, path: string[] = []) => {
          const outgoingEdges = edges.filter((edge) => edge.source === nodeId);
          if (outgoingEdges.length > 0) {
            const nextNodeId = outgoingEdges[0].target;
            const newPath = [...path, nextNodeId];
            setExecutionPath(newPath);
            
            setTimeout(() => {
              simulateExecution(nextNodeId, newPath);
            }, 800);
          } else {
            // End of execution
            setTimeout(() => {
              setIsExecuting(false);
              setExecutionPath([]);
              onExecute();
            }, 1000);
          }
        };
        
        simulateExecution(triggerNodes[0].id, [triggerNodes[0].id]);
      }, 1000);
    } else {
      setIsExecuting(false);
      alert("No trigger nodes found to start execution");
    }
  }, [nodes, edges, onExecute]);

  // Handle undo
  const handleUndo = useCallback(() => {
    if (undoStack.length > 0) {
      const prevState = undoStack[undoStack.length - 1];
      setRedoStack((stack) => [...stack, { nodes, edges }]);
      setUndoStack((stack) => stack.slice(0, -1));
      setNodes(prevState.nodes);
      setEdges(prevState.edges);
    }
  }, [undoStack, nodes, edges, setNodes, setEdges]);

  // Handle redo
  const handleRedo = useCallback(() => {
    if (redoStack.length > 0) {
      const nextState = redoStack[redoStack.length - 1];
      setUndoStack((stack) => [...stack, { nodes, edges }]);
      setRedoStack((stack) => stack.slice(0, -1));
      setNodes(nextState.nodes);
      setEdges(nextState.edges);
    }
  }, [redoStack, nodes, edges, setNodes, setEdges]);

  // Handle fit view
  const handleFitView = useCallback(() => {
    reactFlowInstance.fitView({ padding: 0.2 });
  }, [reactFlowInstance]);

  // Handle loading a template
  const handleLoadTemplate = useCallback(
    (templateId: string) => {
      const template = templates.find((t) => t.id === templateId);
      if (template) {
        // Save current state for undo
        setUndoStack((stack) => [...stack, { nodes, edges }]);
        setRedoStack([]);

        const { nodes: templateNodes, connections: templateConnections } = template.workflow;
        const { nodes: rfNodes, edges: rfEdges } = convertToReactFlow(
          templateNodes,
          templateConnections
        );
        
        setWorkflowName(template.name);
        setWorkflowDescription(template.workflow.description);
        setNodes(rfNodes);
        setEdges(rfEdges);
      }
    },
    [templates, nodes, edges, setNodes, setEdges]
  );

  // Filter nodes in palette based on search term
  const filteredPaletteItems = nodePaletteItems.filter(
    (item) =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter templates based on search term
  const filteredTemplates = templates.filter(
    (template) =>
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Apply execution path highlighting
  useEffect(() => {
    if (executionPath.length > 0) {
      setNodes((nds) =>
        nds.map((node) => ({
          ...node,
          className: executionPath.includes(node.id) ? "animate-pulse" : "",
        }))
      );

      setEdges((eds) =>
        eds.map((edge) => {
          const sourceInPath = executionPath.includes(edge.source);
          const targetInPath = executionPath.includes(edge.target);
          const isActive = sourceInPath && targetInPath && 
            executionPath.indexOf(edge.target) === executionPath.indexOf(edge.source) + 1;
          
          return {
            ...edge,
            className: isActive ? "animate-pulse" : "",
            style: isActive ? { stroke: 'var(--primary)', strokeWidth: 3 } : {},
          };
        })
      );
    } else {
      setNodes((nds) => nds.map((node) => ({ ...node, className: "" })));
      setEdges((eds) => eds.map((edge) => ({ ...edge, className: "", style: {} })));
    }
  }, [executionPath, setNodes, setEdges]);

  return (
    <div className="flex flex-col h-full w-full bg-background">
      {/* Workflow Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-4">
          <Input
            className="text-xl font-semibold w-64"
            value={workflowName}
            onChange={(e) => setWorkflowName(e.target.value)}
          />
          <Select value={workflowStatus} onValueChange={setWorkflowStatus}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleUndo}
                  disabled={undoStack.length === 0}
                >
                  <Undo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Undo</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleRedo}
                  disabled={redoStack.length === 0}
                >
                  <Redo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Redo</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={handleSave}>
                  <Save className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Save workflow</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon">
                  <Settings className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Workflow settings</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button onClick={handleExecute} disabled={isExecuting}>
            {isExecuting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Executing...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Execute
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar - Node Palette */}
        <div className="w-64 border-r overflow-y-auto bg-background">
          <Tabs defaultValue="nodes">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="nodes">Nodes</TabsTrigger>
              <TabsTrigger value="templates">Templates</TabsTrigger>
            </TabsList>
            <TabsContent value="nodes" className="p-4 space-y-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search nodes..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <ScrollArea className="h-[calc(100vh-220px)]">
                <div className="space-y-3 pr-3">
                  {filteredPaletteItems.map((nodeType) => (
                    <Card
                      key={nodeType.type}
                      className="cursor-pointer hover:bg-accent/50 transition-colors"
                    >
                      <CardHeader className="p-3">
                        <CardTitle className="text-sm flex justify-between items-center">
                          <div className="flex items-center">
                            <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center mr-2">
                              <nodeType.icon className="h-3 w-3 text-primary" />
                            </div>
                            {nodeType.name}
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => handleAddNode(nodeType.type, nodeType)}
                          >
                            <PlusCircle className="h-4 w-4" />
                          </Button>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <p className="text-xs text-muted-foreground">
                          {nodeType.description}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
            <TabsContent value="templates" className="p-4 space-y-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search templates..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <ScrollArea className="h-[calc(100vh-220px)]">
                <div className="space-y-3 pr-3">
                  {filteredTemplates.map((template) => (
                    <Card
                      key={template.id}
                      className="cursor-pointer hover:bg-accent/50 transition-colors"
                    >
                      <CardHeader className="p-3">
                        <CardTitle className="text-sm flex justify-between items-center">
                          {template.name}
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => handleLoadTemplate(template.id)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <p className="text-xs text-muted-foreground mb-2">
                          {template.description}
                        </p>
                        <div className="flex flex-wrap gap-1">
                          <Badge variant="outline" className="text-xs">
                            {template.category}
                          </Badge>
                          {template.tags.slice(0, 2).map((tag) => (
                            <Badge
                              key={tag}
                              variant="secondary"
                              className="text-xs"
                            >
                              {tag}
                            </Badge>
                          ))}
                          {template.tags.length > 2 && (
                            <Badge variant="secondary" className="text-xs">
                              +{template.tags.length - 2}
                            </Badge>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>

        {/* Main Canvas */}
        <div className="flex-1 relative overflow-hidden bg-accent/10">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            onEdgeClick={onEdgeClick}
            onNodeDragStop={onNodeDragStop}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            fitView
            snapToGrid
            snapGrid={[15, 15]}
            defaultEdgeOptions={{
              type: "custom",
              style: { strokeWidth: 2 },
            }}
          >
            {showGrid && <Background gap={15} size={1} />}
            <Controls showInteractive={false} />
            {showMiniMap && (
              <MiniMap
                nodeStrokeWidth={3}
                zoomable
                pannable
                className="bg-background/80 rounded-lg border shadow-sm"
              />
            )}

            <Panel position="top-right" className="bg-background/80 backdrop-blur-sm p-2 rounded-md shadow-sm border">
              <div className="flex flex-col space-y-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleFitView}
                      >
                        <Maximize2 className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="left">
                      <p>Fit to view</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setShowGrid(!showGrid)}
                      >
                        <Grid3X3 className={`h-4 w-4 ${showGrid ? "text-primary" : "text-muted-foreground"}`} />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="left">
                      <p>Toggle grid</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setShowMiniMap(!showMiniMap)}
                      >
                        <LayoutGrid className={`h-4 w-4 ${showMiniMap ? "text-primary" : "text-muted-foreground"}`} />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="left">
                      <p>Toggle minimap</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </Panel>
          </ReactFlow>
        </div>

        {/* Right Sidebar - Node Properties */}
        <div className="w-80 border-l overflow-y-auto bg-background">
          <div className="flex justify-between items-center p-4 border-b">
            <h3 className="text-lg font-semibold">Properties</h3>
            <div className="flex space-x-1">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setViewMode("grid")}
              >
                <LayoutGrid
                  className={`h-4 w-4 ${viewMode === "grid" ? "text-primary" : "text-muted-foreground"}`}
                />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setViewMode("list")}
              >
                <List
                  className={`h-4 w-4 ${viewMode === "list" ? "text-primary" : "text-muted-foreground"}`}
                />
              </Button>
            </div>
          </div>

          <ScrollArea className="h-[calc(100vh-170px)]">
            <div className="p-4">
              {selectedNode ? (
                <div className="space-y-4">
                  <Card>
                    <CardHeader className="p-4">
                      <CardTitle className="text-sm flex justify-between items-center">
                        <span>Node Configuration</span>
                        <Badge variant="outline">{selectedNode.type}</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-4 pt-0 space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="node-name">Name</Label>
                        <Input
                          id="node-name"
                          value={selectedNode.data.name}
                          onChange={(e) =>
                            handleUpdateNodeData(selectedNode.id, {
                              name: e.target.value,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="node-description">Description</Label>
                        <Textarea
                          id="node-description"
                          value={selectedNode.data.description}
                          onChange={(e) =>
                            handleUpdateNodeData(selectedNode.id, {
                              description: e.target.value,
                            })
                          }
                          rows={2}
                        />
                      </div>

                      {/* Agent-specific fields */}
                      {selectedNode.type === "agent" && (
                        <>
                          <div className="space-y-2">
                            <Label htmlFor="agent-model">Model</Label>
                            <Select
                              value={selectedNode.data.model}
                              onValueChange={(value) =>
                                handleUpdateNodeData(selectedNode.id, {
                                  model: value,
                                })
                              }
                            >
                              <SelectTrigger id="agent-model">
                                <SelectValue placeholder="Select model" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="gpt-4">GPT-4</SelectItem>
                                <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                                <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                                <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                                <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                                <SelectItem value="mistral-large">Mistral Large</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="agent-status">Status</Label>
                            <Select
                              value={selectedNode.data.status}
                              onValueChange={(value) =>
                                handleUpdateNodeData(selectedNode.id, {
                                  status: value,
                                })
                              }
                            >
                              <SelectTrigger id="agent-status">
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="draft">Draft</SelectItem>
                                <SelectItem value="inactive">Inactive</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="agent-tools">Tools</Label>
                            <div className="flex flex-wrap gap-1">
                              <Badge variant="outline">web_search</Badge>
                              <Badge variant="outline">code_executor</Badge>
                              <Badge variant="outline">file_manager</Badge>
                              <Badge variant="outline">+</Badge>
                            </div>
                          </div>
                        </>
                      )}

                      {/* Condition-specific fields */}
                      {selectedNode.type === "condition" && (
                        <div className="space-y-2">
                          <Label htmlFor="condition-expr">Condition Expression</Label>
                          <Textarea
                            id="condition-expr"
                            value={selectedNode.data.condition}
                            onChange={(e) =>
                              handleUpdateNodeData(selectedNode.id, {
                                condition: e.target.value,
                              })
                            }
                            rows={2}
                            className="font-mono text-sm"
                          />
                          <p className="text-xs text-muted-foreground">
                            Use JavaScript expressions like: value > 0.8 || result === 'success'
                          </p>
                        </div>
                      )}

                      {/* Tool-specific fields */}
                      {selectedNode.type === "tool" && (
                        <>
                          <div className="space-y-2">
                            <Label htmlFor="tool-type">Tool Type</Label>
                            <Select
                              value={selectedNode.data.toolType}
                              onValueChange={(value) =>
                                handleUpdateNodeData(selectedNode.id, {
                                  toolType: value,
                                })
                              }
                            >
                              <SelectTrigger id="tool-type">
                                <SelectValue placeholder="Select tool type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="api">API Call</SelectItem>
                                <SelectItem value="function">Function</SelectItem>
                                <SelectItem value="database">Database</SelectItem>
                                <SelectItem value="file">File Operation</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="tool-config">Configuration</Label>
                            <Textarea
                              id="tool-config"
                              placeholder="Tool configuration in JSON format"
                              rows={3}
                              className="font-mono text-sm"
                            />
                          </div>
                        </>
                      )}

                      {/* Trigger-specific fields */}
                      {selectedNode.type === "trigger" && (
                        <>
                          <div className="space-y-2">
                            <Label htmlFor="trigger-type">Trigger Type</Label>
                            <Select
                              value={selectedNode.data.triggerType}
                              onValueChange={(value) =>
                                handleUpdateNodeData(selectedNode.id, {
                                  triggerType: value,
                                })
                              }
                            >
                              <SelectTrigger id="trigger-type">
                                <SelectValue placeholder="Select trigger type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="webhook">Webhook</SelectItem>
                                <SelectItem value="schedule">Schedule</SelectItem>
                                <SelectItem value="manual">Manual</SelectItem>
                                <SelectItem value="event">Event</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          {selectedNode.data.triggerType === "webhook" && (
                            <div className="p-3 bg-muted rounded-md">
                              <p className="text-xs font-mono break-all">
                                https://api.example.com/workflows/{selectedNode.id.split("_")[1]}/trigger
                              </p>
                              <Button variant="ghost" size="sm" className="mt-2 h-7 text-xs">
                                <Copy className="h-3 w-3 mr-1" /> Copy URL
                              </Button>
                            </div>
                          )}
                          {selectedNode.data.triggerType === "schedule" && (
                            <div className="space-y-2">
                              <Label htmlFor="schedule-expr">Cron Expression</Label>
                              <Input
                                id="schedule-expr"
                                placeholder="0 0 * * *"
                                className="font-mono"
                              />
                              <p className="text-xs text-muted-foreground">
                                Daily at midnight: 0 0 * * *
                              </p>
                            </div>
                          )}
                        </>
                      )}

                      <div className="flex justify-end">
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteNode(selectedNode.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" /> Delete
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : selectedEdge ? (
                <div className="space-y-4">
                  <Card>
                    <CardHeader className="p-4">
                      <CardTitle className="text-sm">Edge Configuration</CardTitle>
                    </CardHeader>
                    <CardContent className="p-4 pt-0 space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="edge-label">Label</Label>
                        <Input
                          id="edge-label"
                          value={selectedEdge.data?.label || ""}
                          onChange={(e) =>
                            handleUpdateEdgeData(selectedEdge.id, {
                              label: e.target.value,
                            })
                          }
                          placeholder="Connection label"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edge-condition">Condition (optional)</Label>
                        <Textarea
                          id="edge-condition"
                          placeholder="Condition for this connection"
                          rows={2}
                          className="font-mono text-sm"
                        />
                        <p className="text-xs text-muted-foreground">
                          Leave empty to always follow this path
                        </p>
                      </div>
                      <div className="flex justify-end">
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteEdge(selectedEdge.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" /> Delete
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="space-y-4">
                  <Card>
                    <CardHeader className="p-4">
                      <CardTitle className="text-sm">Workflow Details</CardTitle>
                    </CardHeader>
                    <CardContent className="p-4 pt-0 space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="workflow-name">Name</Label>
                        <Input
                          id="workflow-name"
                          value={workflowName}
                          onChange={(e) => setWorkflowName(e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="workflow-description">Description</Label>
                        <Textarea
                          id="workflow-description"
                          value={workflowDescription}
                          onChange={(e) => setWorkflowDescription(e.target.value)}
                          rows={3}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="workflow-status">Status</Label>
                        <Select value={workflowStatus} onValueChange={setWorkflowStatus}>
                          <SelectTrigger id="workflow-status">
                            <SelectValue placeholder="Status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="draft">Draft</SelectItem>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="archived">Archived</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex justify-between items-center pt-2">
                        <div className="text-sm">
                          <div className="text-muted-foreground">Nodes: {nodes.length}</div>
                          <div className="text-muted-foreground">Connections: {edges.length}</div>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" /> Export
                          </Button>
                          <Button variant="outline" size="sm">
                            <Upload className="h-4 w-4 mr-2" /> Import
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </div>

      {/* Status Bar */}
      <div className="border-t p-2 flex justify-between items-center text-xs text-muted-foreground bg-background">
        <div>
          Nodes: {nodes.length} | Connections: {edges.length}
        </div>
        <div>
          {isExecuting ? (
            <span className="text-primary">Executing workflow...</span>
          ) : (
            <span>Ready</span>
          )}
        </div>
        <div>Last saved: {new Date().toLocaleTimeString()}</div>
      </div>

      {/* Node Configuration Dialog */}
      <Dialog open={nodeConfigOpen} onOpenChange={setNodeConfigOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Node Configuration</DialogTitle>
            <DialogDescription>
              Configure the properties for this node.
            </DialogDescription>
          </DialogHeader>
          {selectedNode && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="dialog-node-name">Name</Label>
                <Input
                  id="dialog-node-name"
                  value={selectedNode.data.name}
                  onChange={(e) =>
                    handleUpdateNodeData(selectedNode.id, {
                      name: e.target.value,
                    })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="dialog-node-description">Description</Label>
                <Textarea
                  id="dialog-node-description"
                  value={selectedNode.data.description}
                  onChange={(e) =>
                    handleUpdateNodeData(selectedNode.id, {
                      description: e.target.value,
                    })
                  }
                  rows={2}
                />
              </div>

              {/* Type-specific configuration fields would go here */}
              {/* Similar to the sidebar configuration but in dialog form */}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setNodeConfigOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setNodeConfigOpen(false)}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edge Configuration Dialog */}
      <Dialog open={edgeConfigOpen} onOpenChange={setEdgeConfigOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Connection Configuration</DialogTitle>
            <DialogDescription>
              Configure the properties for this connection.
            </DialogDescription>
          </DialogHeader>
          {selectedEdge && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="dialog-edge-label">Label</Label>
                <Input
                  id="dialog-edge-label"
                  value={selectedEdge.data?.label || ""}
                  onChange={(e) =>
                    handleUpdateEdgeData(selectedEdge.id, {
                      label: e.target.value,
                    })
                  }
                  placeholder="Connection label"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="dialog-edge-condition">Condition (optional)</Label>
                <Textarea
                  id="dialog-edge-condition"
                  placeholder="Condition for this connection"
                  rows={2}
                  className="font-mono text-sm"
                />
                <p className="text-xs text-muted-foreground">
                  Leave empty to always follow this path
                </p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setEdgeConfigOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setEdgeConfigOpen(false)}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const WorkflowDesigner = (props: WorkflowDesignerProps) => {
  return (
    <ReactFlowProvider>
      <WorkflowDesignerInner {...props} />
    </ReactFlowProvider>
  );
};

export default WorkflowDesigner;