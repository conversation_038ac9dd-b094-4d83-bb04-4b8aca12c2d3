import { NextRequest, NextResponse } from 'next/server';
import { requireResourceAccess } from '@/lib/auth';
import { AgentManager } from '@/lib/agents';

export const GET = requireResourceAccess('agents', 'read')(async (request: NextRequest) => {
  try {
    const authContext = (request as any).auth;
    const { searchParams } = new URL(request.url);
    
    const agentManager = new AgentManager(authContext.organization.id);
    
    const filters = {
      active: searchParams.get('active') === 'true' ? true : searchParams.get('active') === 'false' ? false : undefined,
      created_by: searchParams.get('created_by') || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0,
    };

    const agents = await agentManager.listAgents(filters);

    return NextResponse.json({
      success: true,
      data: agents,
      pagination: {
        limit: filters.limit,
        offset: filters.offset,
        total: agents.length
      }
    });

  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'Failed to fetch agents' },
      { status: 500 }
    );
  }
});

export const POST = requireResourceAccess('agents', 'create')(async (request: NextRequest) => {
  try {
    const authContext = (request as any).auth;
    const {
      name,
      description,
      prompt_template_id,
      provider_id,
      config,
      memory_config
    } = await request.json();

    if (!name || !prompt_template_id || !provider_id) {
      return NextResponse.json(
        { error: 'Name, prompt_template_id, and provider_id are required' },
        { status: 400 }
      );
    }

    const agentManager = new AgentManager(authContext.organization.id);
    
    const agent = await agentManager.createAgent(
      name,
      description || '',
      prompt_template_id,
      provider_id,
      config || {},
      memory_config || { type: 'conversation', limit: 50, retention_policy: 'auto' },
      authContext.user.id
    );

    return NextResponse.json({
      success: true,
      data: agent
    }, { status: 201 });

  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'Failed to create agent' },
      { status: 500 }
    );
  }
});