import axios, { AxiosInstance } from 'axios';
import { eventBus, EventType } from './event-bus';

export interface AIProvider {
  id: string;
  name: string;
  type: 'openai' | 'claude' | 'gemini' | 'mistral' | 'local';
  endpoint: string;
  apiKey?: string;
  models: string[];
  status: 'healthy' | 'degraded' | 'offline';
  latency: number;
  costPerToken: number;
  maxTokens: number;
  priority: number;
  fallbackChain: string[];
}

export interface ProviderRequest {
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  tools?: any[];
}

export interface ProviderResponse {
  id: string;
  model: string;
  choices: Array<{
    message: {
      role: string;
      content: string;
      tool_calls?: any[];
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  cost: number;
}

class ProviderManager {
  private providers = new Map<string, AIProvider>();
  private httpClients = new Map<string, AxiosInstance>();
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private requestQueue: Array<{
    request: ProviderRequest;
    resolve: (response: ProviderResponse) => void;
    reject: (error: Error) => void;
    attempts: number;
    providerId?: string;
  }> = [];

  constructor() {
    this.initializeProviders();
    this.startHealthChecks();
  }

  private initializeProviders(): void {
    const defaultProviders: AIProvider[] = [
      {
        id: 'openai-gpt4',
        name: 'OpenAI GPT-4',
        type: 'openai',
        endpoint: 'https://api.openai.com/v1/chat/completions',
        models: ['gpt-4', 'gpt-4-turbo', 'gpt-4o'],
        status: 'healthy',
        latency: 0,
        costPerToken: 0.00003,
        maxTokens: 128000,
        priority: 1,
        fallbackChain: ['claude-3-opus', 'gemini-pro']
      },
      {
        id: 'claude-3-opus',
        name: 'Claude 3 Opus',
        type: 'claude',
        endpoint: 'https://api.anthropic.com/v1/messages',
        models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229'],
        status: 'healthy',
        latency: 0,
        costPerToken: 0.000015,
        maxTokens: 200000,
        priority: 2,
        fallbackChain: ['openai-gpt4', 'gemini-pro']
      },
      {
        id: 'gemini-pro',
        name: 'Google Gemini Pro',
        type: 'gemini',
        endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
        models: ['gemini-pro', 'gemini-pro-vision'],
        status: 'healthy',
        latency: 0,
        costPerToken: 0.0000005,
        maxTokens: 32768,
        priority: 3,
        fallbackChain: ['openai-gpt4', 'claude-3-opus']
      },
      {
        id: 'mistral-large',
        name: 'Mistral Large',
        type: 'mistral',
        endpoint: 'https://api.mistral.ai/v1/chat/completions',
        models: ['mistral-large-latest', 'mistral-medium'],
        status: 'healthy',
        latency: 0,
        costPerToken: 0.000008,
        maxTokens: 32768,
        priority: 4,
        fallbackChain: ['openai-gpt4']
      },
      {
        id: 'local-ollama',
        name: 'Local Ollama',
        type: 'local',
        endpoint: 'http://localhost:11434/api/chat',
        models: ['llama2', 'codellama', 'mistral'],
        status: 'offline',
        latency: 0,
        costPerToken: 0,
        maxTokens: 4096,
        priority: 5,
        fallbackChain: ['openai-gpt4']
      }
    ];

    defaultProviders.forEach(provider => {
      this.providers.set(provider.id, provider);
      this.createHttpClient(provider);
    });
  }

  private createHttpClient(provider: AIProvider): void {
    const client = axios.create({
      baseURL: provider.endpoint,
      timeout: 30000,
      headers: this.getAuthHeaders(provider)
    });

    client.interceptors.request.use(config => {
      const startTime = Date.now();
      config.metadata = { startTime };
      return config;
    });

    client.interceptors.response.use(
      response => {
        const endTime = Date.now();
        const latency = endTime - response.config.metadata.startTime;
        this.updateProviderLatency(provider.id, latency);
        return response;
      },
      error => {
        this.handleProviderError(provider.id, error);
        return Promise.reject(error);
      }
    );

    this.httpClients.set(provider.id, client);
  }

  private getAuthHeaders(provider: AIProvider): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    switch (provider.type) {
      case 'openai':
        if (process.env.OPENAI_API_KEY) {
          headers['Authorization'] = `Bearer ${process.env.OPENAI_API_KEY}`;
        }
        break;
      case 'claude':
        if (process.env.ANTHROPIC_API_KEY) {
          headers['x-api-key'] = process.env.ANTHROPIC_API_KEY;
          headers['anthropic-version'] = '2023-06-01';
        }
        break;
      case 'gemini':
        if (process.env.GOOGLE_API_KEY) {
          headers['x-goog-api-key'] = process.env.GOOGLE_API_KEY;
        }
        break;
      case 'mistral':
        if (process.env.MISTRAL_API_KEY) {
          headers['Authorization'] = `Bearer ${process.env.MISTRAL_API_KEY}`;
        }
        break;
    }

    return headers;
  }

  async makeRequest(request: ProviderRequest, preferredProviderId?: string): Promise<ProviderResponse> {
    const provider = this.selectOptimalProvider(request, preferredProviderId);
    
    if (!provider) {
      throw new Error('No available providers for this request');
    }

    eventBus.publish('provider-events', 'tool_call_start', {
      providerId: provider.id,
      model: request.model,
      requestId: this.generateRequestId()
    });

    try {
      const response = await this.executeRequest(provider, request);
      
      eventBus.publish('provider-events', 'tool_call_result', {
        providerId: provider.id,
        model: request.model,
        response,
        cost: response.cost
      });

      return response;
    } catch (error) {
      eventBus.publish('provider-events', 'tool_call_error', {
        providerId: provider.id,
        error: error.message
      });

      // Try fallback chain
      for (const fallbackId of provider.fallbackChain) {
        const fallbackProvider = this.providers.get(fallbackId);
        if (fallbackProvider && fallbackProvider.status === 'healthy') {
          eventBus.publish('provider-events', 'fallback_triggered', {
            originalProvider: provider.id,
            fallbackProvider: fallbackId
          });

          try {
            return await this.executeRequest(fallbackProvider, request);
          } catch (fallbackError) {
            continue;
          }
        }
      }

      throw error;
    }
  }

  private selectOptimalProvider(request: ProviderRequest, preferredId?: string): AIProvider | null {
    let candidates = Array.from(this.providers.values())
      .filter(p => p.status === 'healthy' && p.models.includes(request.model));

    if (preferredId) {
      const preferred = this.providers.get(preferredId);
      if (preferred && preferred.status === 'healthy' && preferred.models.includes(request.model)) {
        return preferred;
      }
    }

    if (candidates.length === 0) {
      candidates = Array.from(this.providers.values())
        .filter(p => p.status === 'degraded');
    }

    // Sort by cost optimization and latency
    candidates.sort((a, b) => {
      const costScore = a.costPerToken - b.costPerToken;
      const latencyScore = a.latency - b.latency;
      const priorityScore = a.priority - b.priority;
      
      return costScore * 0.4 + latencyScore * 0.3 + priorityScore * 0.3;
    });

    return candidates[0] || null;
  }

  private async executeRequest(provider: AIProvider, request: ProviderRequest): Promise<ProviderResponse> {
    const client = this.httpClients.get(provider.id);
    if (!client) {
      throw new Error(`No HTTP client for provider ${provider.id}`);
    }

    const payload = this.formatRequestForProvider(provider, request);
    const response = await client.post('', payload);
    
    return this.formatResponseFromProvider(provider, response.data, request);
  }

  private formatRequestForProvider(provider: AIProvider, request: ProviderRequest): any {
    switch (provider.type) {
      case 'openai':
      case 'mistral':
        return {
          model: request.model,
          messages: request.messages,
          temperature: request.temperature || 0.7,
          max_tokens: request.maxTokens,
          stream: request.stream || false,
          tools: request.tools
        };
      
      case 'claude':
        return {
          model: request.model,
          messages: request.messages.filter(m => m.role !== 'system'),
          system: request.messages.find(m => m.role === 'system')?.content,
          max_tokens: request.maxTokens || 4096,
          temperature: request.temperature || 0.7
        };
      
      case 'gemini':
        return {
          contents: request.messages.map(m => ({
            role: m.role === 'assistant' ? 'model' : 'user',
            parts: [{ text: m.content }]
          })),
          generationConfig: {
            temperature: request.temperature || 0.7,
            maxOutputTokens: request.maxTokens
          }
        };
      
      case 'local':
        return {
          model: request.model,
          messages: request.messages,
          stream: false
        };
      
      default:
        return request;
    }
  }

  private formatResponseFromProvider(provider: AIProvider, response: any, request: ProviderRequest): ProviderResponse {
    const baseResponse: ProviderResponse = {
      id: this.generateRequestId(),
      model: request.model,
      choices: [],
      usage: {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0
      },
      cost: 0
    };

    switch (provider.type) {
      case 'openai':
      case 'mistral':
        baseResponse.choices = response.choices;
        baseResponse.usage = response.usage;
        baseResponse.cost = response.usage.total_tokens * provider.costPerToken;
        break;
      
      case 'claude':
        baseResponse.choices = [{
          message: {
            role: 'assistant',
            content: response.content[0].text
          },
          finish_reason: response.stop_reason
        }];
        baseResponse.usage = {
          prompt_tokens: response.usage.input_tokens,
          completion_tokens: response.usage.output_tokens,
          total_tokens: response.usage.input_tokens + response.usage.output_tokens
        };
        baseResponse.cost = baseResponse.usage.total_tokens * provider.costPerToken;
        break;
      
      case 'gemini':
        baseResponse.choices = [{
          message: {
            role: 'assistant',
            content: response.candidates[0].content.parts[0].text
          },
          finish_reason: response.candidates[0].finishReason
        }];
        baseResponse.usage = {
          prompt_tokens: response.usageMetadata?.promptTokenCount || 0,
          completion_tokens: response.usageMetadata?.candidatesTokenCount || 0,
          total_tokens: response.usageMetadata?.totalTokenCount || 0
        };
        baseResponse.cost = baseResponse.usage.total_tokens * provider.costPerToken;
        break;
      
      case 'local':
        baseResponse.choices = [{
          message: {
            role: 'assistant',
            content: response.message.content
          },
          finish_reason: 'stop'
        }];
        baseResponse.cost = 0;
        break;
    }

    return baseResponse;
  }

  private startHealthChecks(): void {
    this.healthCheckInterval = setInterval(async () => {
      for (const [id, provider] of this.providers) {
        await this.checkProviderHealth(id);
      }
    }, 30000); // Check every 30 seconds
  }

  private async checkProviderHealth(providerId: string): Promise<void> {
    const provider = this.providers.get(providerId);
    if (!provider) return;

    try {
      const client = this.httpClients.get(providerId);
      if (!client) return;

      const startTime = Date.now();
      
      // Simple health check request
      const healthRequest = this.formatRequestForProvider(provider, {
        model: provider.models[0],
        messages: [{ role: 'user', content: 'ping' }],
        maxTokens: 1
      });

      await client.post('', healthRequest);
      
      const latency = Date.now() - startTime;
      this.updateProviderLatency(providerId, latency);
      
      if (provider.status !== 'healthy') {
        provider.status = 'healthy';
        eventBus.publish('provider-events', 'provider_health_check', {
          providerId,
          status: 'healthy',
          latency
        });
      }
    } catch (error) {
      const newStatus = provider.status === 'healthy' ? 'degraded' : 'offline';
      provider.status = newStatus;
      
      eventBus.publish('provider-events', 'provider_health_check', {
        providerId,
        status: newStatus,
        error: error.message
      });
    }
  }

  private updateProviderLatency(providerId: string, latency: number): void {
    const provider = this.providers.get(providerId);
    if (provider) {
      provider.latency = Math.round((provider.latency * 0.8) + (latency * 0.2)); // Moving average
    }
  }

  private handleProviderError(providerId: string, error: any): void {
    const provider = this.providers.get(providerId);
    if (provider && provider.status === 'healthy') {
      provider.status = 'degraded';
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getProviders(): AIProvider[] {
    return Array.from(this.providers.values());
  }

  getProvider(id: string): AIProvider | undefined {
    return this.providers.get(id);
  }

  addProvider(provider: AIProvider): void {
    this.providers.set(provider.id, provider);
    this.createHttpClient(provider);
  }

  updateProvider(id: string, updates: Partial<AIProvider>): void {
    const provider = this.providers.get(id);
    if (provider) {
      Object.assign(provider, updates);
      this.createHttpClient(provider);
    }
  }

  removeProvider(id: string): void {
    this.providers.delete(id);
    this.httpClients.delete(id);
  }

  destroy(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
  }
}

export const providerManager = new ProviderManager();
export default ProviderManager;