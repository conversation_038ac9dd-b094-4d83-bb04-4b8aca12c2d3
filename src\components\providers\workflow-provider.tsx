"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { workflowManager, Workflow, WorkflowExecution, WorkflowTemplate } from '@/lib/workflow-manager';
import { useEventBus } from './event-bus-provider';

interface WorkflowContextType {
  workflows: Workflow[];
  executions: WorkflowExecution[];
  templates: WorkflowTemplate[];
  createWorkflow: (workflowData: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt' | 'version'>) => Promise<Workflow>;
  updateWorkflow: (id: string, updates: Partial<Workflow>) => Promise<Workflow | null>;
  deleteWorkflow: (id: string) => Promise<boolean>;
  executeWorkflow: (workflowId: string, userId: string, initialData?: any) => Promise<WorkflowExecution>;
  cancelExecution: (executionId: string) => Promise<void>;
  createFromTemplate: (templateId: string, name?: string) => Promise<Workflow>;
  isLoading: boolean;
}

const WorkflowContext = createContext<WorkflowContextType | null>(null);

export function WorkflowProvider({ children }: { children: React.ReactNode }) {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { subscribe } = useEventBus();

  useEffect(() => {
    // Load initial data
    const loadData = () => {
      const allWorkflows = workflowManager.getAllWorkflows();
      const allTemplates = workflowManager.getTemplates();
      setWorkflows(allWorkflows);
      setTemplates(allTemplates);
    };

    loadData();

    // Subscribe to workflow events
    const unsubscribeWorkflow = subscribe('workflow-events', (message) => {
      switch (message.type) {
        case 'workflow_started':
          if (message.data.executionId) {
            const execution = workflowManager.getExecution(message.data.executionId);
            if (execution) {
              setExecutions(prev => [...prev.filter(e => e.id !== execution.id), execution]);
            }
          }
          break;
        case 'workflow_completed':
        case 'workflow_failed':
          const execution = workflowManager.getExecution(message.data.executionId);
          if (execution) {
            setExecutions(prev => prev.map(e => e.id === execution.id ? execution : e));
          }
          break;
        case 'state_update':
          if (message.data.executionId) {
            const execution = workflowManager.getExecution(message.data.executionId);
            if (execution) {
              setExecutions(prev => prev.map(e => e.id === execution.id ? execution : e));
            }
          }
          break;
      }
    });

    return () => {
      unsubscribeWorkflow();
    };
  }, [subscribe]);

  const createWorkflow = async (workflowData: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt' | 'version'>) => {
    setIsLoading(true);
    try {
      const workflow = await workflowManager.createWorkflow(workflowData);
      setWorkflows(prev => [...prev, workflow]);
      return workflow;
    } finally {
      setIsLoading(false);
    }
  };

  const updateWorkflow = async (id: string, updates: Partial<Workflow>) => {
    setIsLoading(true);
    try {
      const workflow = await workflowManager.updateWorkflow(id, updates);
      if (workflow) {
        setWorkflows(prev => prev.map(w => w.id === id ? workflow : w));
      }
      return workflow;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteWorkflow = async (id: string) => {
    setIsLoading(true);
    try {
      const deleted = await workflowManager.deleteWorkflow(id);
      if (deleted) {
        setWorkflows(prev => prev.filter(w => w.id !== id));
      }
      return deleted;
    } finally {
      setIsLoading(false);
    }
  };

  const executeWorkflow = async (workflowId: string, userId: string, initialData?: any) => {
    setIsLoading(true);
    try {
      const execution = await workflowManager.executeWorkflow(workflowId, userId, initialData);
      setExecutions(prev => [...prev, execution]);
      return execution;
    } finally {
      setIsLoading(false);
    }
  };

  const cancelExecution = async (executionId: string) => {
    await workflowManager.cancelExecution(executionId);
    const execution = workflowManager.getExecution(executionId);
    if (execution) {
      setExecutions(prev => prev.map(e => e.id === executionId ? execution : e));
    }
  };

  const createFromTemplate = async (templateId: string, name?: string) => {
    setIsLoading(true);
    try {
      const workflow = await workflowManager.createWorkflowFromTemplate(templateId, name);
      setWorkflows(prev => [...prev, workflow]);
      return workflow;
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue: WorkflowContextType = {
    workflows,
    executions,
    templates,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    executeWorkflow,
    cancelExecution,
    createFromTemplate,
    isLoading
  };

  return (
    <WorkflowContext.Provider value={contextValue}>
      {children}
    </WorkflowContext.Provider>
  );
}

export function useWorkflow() {
  const context = useContext(WorkflowContext);
  if (!context) {
    throw new Error('useWorkflow must be used within a WorkflowProvider');
  }
  return context;
}