# Database
DATABASE_URL="postgresql://username:password@localhost:5432/synapseai"
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="your-super-secure-jwt-secret-key-here"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# AI Providers
OPENAI_API_KEY="sk-your-openai-api-key"
ANTHROPIC_API_KEY="sk-ant-your-anthropic-api-key"
GOOGLE_AI_API_KEY="your-google-ai-api-key"
MISTRAL_API_KEY="your-mistral-api-key"
GROQ_API_KEY="gsk_your-groq-api-key"

# External Services
SERP_API_KEY="your-serp-api-key"
SENDGRID_API_KEY="SG.your-sendgrid-api-key"

# Application
NODE_ENV="production"
PORT="8000"
FRONTEND_URL="https://your-domain.com"

# Security
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX_REQUESTS="100"
BCRYPT_ROUNDS="12"

# Monitoring
SENTRY_DSN="https://your-sentry-dsn"
LOG_LEVEL="info"