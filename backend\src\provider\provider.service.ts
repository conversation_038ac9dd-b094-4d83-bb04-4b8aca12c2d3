import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateProviderDto, UpdateProviderDto } from './dto/provider.dto';

@Injectable()
export class ProviderService {
  constructor(private prisma: PrismaService) {}

  async create(organizationId: string, dto: CreateProviderDto) {
    return this.prisma.provider.create({
      data: {
        ...dto,
        organizationId,
        config: dto.config || {},
        credentials: this.encryptCredentials(dto.credentials),
      },
    });
  }

  async findAll(organizationId: string) {
    const providers = await this.prisma.provider.findMany({
      where: { organizationId },
      orderBy: { createdAt: 'desc' },
    });

    return providers.map(provider => ({
      ...provider,
      credentials: this.maskCredentials(provider.credentials),
    }));
  }

  async findOne(id: string, organizationId: string) {
    const provider = await this.prisma.provider.findFirst({
      where: { id, organizationId },
    });

    if (provider) {
      return {
        ...provider,
        credentials: this.maskCredentials(provider.credentials),
      };
    }

    return null;
  }

  async update(id: string, organizationId: string, dto: UpdateProviderDto) {
    const updateData: any = { ...dto };
    
    if (dto.credentials) {
      updateData.credentials = this.encryptCredentials(dto.credentials);
    }

    return this.prisma.provider.update({
      where: { id },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
    });
  }

  async remove(id: string, organizationId: string) {
    return this.prisma.provider.delete({
      where: { id },
    });
  }

  async testConnection(id: string, organizationId: string) {
    const provider = await this.prisma.provider.findFirst({
      where: { id, organizationId },
    });

    if (!provider) {
      throw new Error('Provider not found');
    }

    const credentials = this.decryptCredentials(provider.credentials);
    
    switch (provider.type) {
      case 'OPENAI':
        return this.testOpenAI(credentials);
      case 'CLAUDE':
        return this.testClaude(credentials);
      case 'GEMINI':
        return this.testGemini(credentials);
      case 'MISTRAL':
        return this.testMistral(credentials);
      case 'GROQ':
        return this.testGroq(credentials);
      case 'OLLAMA':
        return this.testOllama(credentials);
      default:
        throw new Error('Unknown provider type');
    }
  }

  async getModels(id: string, organizationId: string) {
    const provider = await this.prisma.provider.findFirst({
      where: { id, organizationId },
    });

    if (!provider) {
      throw new Error('Provider not found');
    }

    const credentials = this.decryptCredentials(provider.credentials);

    switch (provider.type) {
      case 'OPENAI':
        return this.getOpenAIModels(credentials);
      case 'CLAUDE':
        return this.getClaudeModels(credentials);
      case 'GEMINI':
        return this.getGeminiModels(credentials);
      case 'MISTRAL':
        return this.getMistralModels(credentials);
      case 'GROQ':
        return this.getGroqModels(credentials);
      case 'OLLAMA':
        return this.getOllamaModels(credentials);
      default:
        return [];
    }
  }

  private async testOpenAI(credentials: any) {
    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${credentials.apiKey}`,
        },
      });
      return { success: response.ok, status: response.status };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  private async testClaude(credentials: any) {
    try {
      const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': credentials.apiKey,
          'anthropic-version': '2023-06-01',
        },
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          max_tokens: 1,
          messages: [{ role: 'user', content: 'test' }],
        }),
      });
      return { success: response.ok, status: response.status };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  private async testGemini(credentials: any) {
    try {
      const response = await fetch(`https://generativelanguage.googleapis.com/v1/models?key=${credentials.apiKey}`);
      return { success: response.ok, status: response.status };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  private async testMistral(credentials: any) {
    try {
      const response = await fetch('https://api.mistral.ai/v1/models', {
        headers: {
          'Authorization': `Bearer ${credentials.apiKey}`,
        },
      });
      return { success: response.ok, status: response.status };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  private async testGroq(credentials: any) {
    try {
      const response = await fetch('https://api.groq.com/openai/v1/models', {
        headers: {
          'Authorization': `Bearer ${credentials.apiKey}`,
        },
      });
      return { success: response.ok, status: response.status };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  private async testOllama(credentials: any) {
    try {
      const response = await fetch(`${credentials.baseUrl}/api/tags`);
      return { success: response.ok, status: response.status };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  private async getOpenAIModels(credentials: any) {
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${credentials.apiKey}`,
      },
    });
    const data = await response.json();
    return data.data || [];
  }

  private async getClaudeModels(credentials: any) {
    return [
      { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus' },
      { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet' },
      { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku' },
    ];
  }

  private async getGeminiModels(credentials: any) {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1/models?key=${credentials.apiKey}`);
    const data = await response.json();
    return data.models || [];
  }

  private async getMistralModels(credentials: any) {
    const response = await fetch('https://api.mistral.ai/v1/models', {
      headers: {
        'Authorization': `Bearer ${credentials.apiKey}`,
      },
    });
    const data = await response.json();
    return data.data || [];
  }

  private async getGroqModels(credentials: any) {
    const response = await fetch('https://api.groq.com/openai/v1/models', {
      headers: {
        'Authorization': `Bearer ${credentials.apiKey}`,
      },
    });
    const data = await response.json();
    return data.data || [];
  }

  private async getOllamaModels(credentials: any) {
    const response = await fetch(`${credentials.baseUrl}/api/tags`);
    const data = await response.json();
    return data.models || [];
  }

  private encryptCredentials(credentials: any): any {
    // In production, use proper encryption
    const crypto = require('crypto');
    const key = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';
    const cipher = crypto.createCipher('aes-256-cbc', key);
    let encrypted = cipher.update(JSON.stringify(credentials), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return { encrypted };
  }

  private decryptCredentials(encryptedCredentials: any): any {
    // In production, use proper decryption
    const crypto = require('crypto');
    const key = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';
    const decipher = crypto.createDecipher('aes-256-cbc', key);
    let decrypted = decipher.update(encryptedCredentials.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return JSON.parse(decrypted);
  }

  private maskCredentials(credentials: any): any {
    if (!credentials || !credentials.encrypted) return {};
    
    return {
      masked: true,
      hasApiKey: true,
    };
  }
}