"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { AlertCircle, Check, ChevronRight, Cloud, Code, Edit, ExternalLink, Globe, Info, MoreHorizontal, Plus, RefreshCw, Save, Server, Settings, Shield, Trash2, Zap } from "lucide-react";
import { providerManager } from "@/lib/provider-manager";
import AppLayout from "@/components/layout/AppLayout";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Provider {
  id: string;
  name: string;
  type: string;
  status: 'healthy' | 'degraded' | 'offline';
  priority: number;
  models: string[];
  latency: number;
  costPerToken: number;
  maxTokens: number;
  apiKey?: string;
  baseUrl?: string;
  features: string[];
  isDefault?: boolean;
}

export default function ProviderIntegrationPage() {
  const [providers, setProviders] = useState<Provider[]>([
    {
      id: "openai",
      name: "OpenAI",
      type: "cloud",
      status: "healthy",
      priority: 1,
      models: ["gpt-4", "gpt-3.5-turbo"],
      latency: 250,
      costPerToken: 0.00002,
      maxTokens: 128000,
      features: ["chat", "function-call", "vision"],
      isDefault: true
    },
    {
      id: "anthropic",
      name: "Anthropic",
      type: "cloud",
      status: "healthy",
      priority: 2,
      models: ["claude-3-opus", "claude-3-sonnet"],
      latency: 320,
      costPerToken: 0.00003,
      maxTokens: 200000,
      features: ["chat", "vision"]
    },
    {
      id: "google",
      name: "Google AI",
      type: "cloud",
      status: "degraded",
      priority: 3,
      models: ["gemini-pro", "gemini-pro-vision"],
      latency: 400,
      costPerToken: 0.000015,
      maxTokens: 32000,
      features: ["chat", "function-call", "vision"]
    },
    {
      id: "mistral",
      name: "Mistral AI",
      type: "cloud",
      status: "healthy",
      priority: 4,
      models: ["mistral-large", "mistral-medium"],
      latency: 280,
      costPerToken: 0.000018,
      maxTokens: 32000,
      features: ["chat"]
    },
    {
      id: "local",
      name: "Local Models",
      type: "local",
      status: "offline",
      priority: 5,
      models: ["llama-3-70b", "llama-3-8b"],
      latency: 150,
      costPerToken: 0.0,
      maxTokens: 8000,
      baseUrl: "http://localhost:11434",
      features: ["chat"]
    }
  ]);

  const [activeTab, setActiveTab] = useState("all");
  const [isAddProviderOpen, setIsAddProviderOpen] = useState(false);
  const [newProvider, setNewProvider] = useState<Partial<Provider>>({
    name: "",
    type: "cloud",
    priority: providers.length + 1,
    models: [],
    features: []
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-500';
      case 'degraded': return 'bg-yellow-500';
      case 'offline': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'healthy': return 'default';
      case 'degraded': return 'warning';
      case 'offline': return 'destructive';
      default: return 'outline';
    }
  };

  const handleAddProvider = () => {
    // In a real app, this would validate and make an API call
    const provider: Provider = {
      id: `provider_${Date.now()}`,
      name: newProvider.name || "New Provider",
      type: newProvider.type || "cloud",
      status: "offline",
      priority: newProvider.priority || providers.length + 1,
      models: newProvider.models || [],
      latency: 0,
      costPerToken: 0,
      maxTokens: 0,
      apiKey: newProvider.apiKey,
      baseUrl: newProvider.baseUrl,
      features: newProvider.features || []
    };
    
    setProviders([...providers, provider]);
    setIsAddProviderOpen(false);
    setNewProvider({
      name: "",
      type: "cloud",
      priority: providers.length + 2,
      models: [],
      features: []
    });
  };

  const handleDeleteProvider = (id: string) => {
    setProviders(providers.filter(p => p.id !== id));
  };

  const handleSetDefault = (id: string) => {
    setProviders(providers.map(p => ({
      ...p,
      isDefault: p.id === id
    })));
  };

  const filteredProviders = activeTab === "all" 
    ? providers 
    : providers.filter(p => p.type === activeTab);

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Provider Integration</h2>
            <p className="text-muted-foreground">
              Configure and manage AI providers for your agents and workflows
            </p>
          </div>
          <Dialog open={isAddProviderOpen} onOpenChange={setIsAddProviderOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Provider
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Add New Provider</DialogTitle>
                <DialogDescription>
                  Configure a new AI provider to use with your agents and workflows
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Provider Name</Label>
                  <Input 
                    id="name" 
                    placeholder="e.g., OpenAI, Custom Provider" 
                    value={newProvider.name}
                    onChange={(e) => setNewProvider({...newProvider, name: e.target.value})}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="type">Provider Type</Label>
                  <Select 
                    value={newProvider.type} 
                    onValueChange={(value) => setNewProvider({...newProvider, type: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select provider type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cloud">Cloud Provider</SelectItem>
                      <SelectItem value="local">Local Provider</SelectItem>
                      <SelectItem value="custom">Custom Provider</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="baseUrl">Base URL</Label>
                  <Input 
                    id="baseUrl" 
                    placeholder="https://api.provider.com/v1" 
                    value={newProvider.baseUrl || ""}
                    onChange={(e) => setNewProvider({...newProvider, baseUrl: e.target.value})}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="apiKey">API Key</Label>
                  <Input 
                    id="apiKey" 
                    type="password" 
                    placeholder="sk-..." 
                    value={newProvider.apiKey || ""}
                    onChange={(e) => setNewProvider({...newProvider, apiKey: e.target.value})}
                  />
                </div>
                <div className="grid gap-2">
                  <Label>Priority (1 is highest)</Label>
                  <div className="flex items-center gap-4">
                    <Slider 
                      value={[newProvider.priority || providers.length + 1]} 
                      min={1} 
                      max={10} 
                      step={1}
                      onValueChange={(value) => setNewProvider({...newProvider, priority: value[0]})}
                      className="flex-1"
                    />
                    <span className="w-8 text-center">{newProvider.priority || providers.length + 1}</span>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddProviderOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddProvider}>
                  Add Provider
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <div className="flex justify-between items-center">
            <TabsList>
              <TabsTrigger value="all">All Providers</TabsTrigger>
              <TabsTrigger value="cloud">Cloud</TabsTrigger>
              <TabsTrigger value="local">Local</TabsTrigger>
              <TabsTrigger value="custom">Custom</TabsTrigger>
            </TabsList>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Status
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="mr-2 h-4 w-4" />
                Routing Settings
              </Button>
            </div>
          </div>

          <TabsContent value="all" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredProviders.map((provider) => (
                <ProviderCard 
                  key={provider.id} 
                  provider={provider} 
                  onDelete={handleDeleteProvider}
                  onSetDefault={handleSetDefault}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="cloud" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredProviders.map((provider) => (
                <ProviderCard 
                  key={provider.id} 
                  provider={provider} 
                  onDelete={handleDeleteProvider}
                  onSetDefault={handleSetDefault}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="local" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredProviders.map((provider) => (
                <ProviderCard 
                  key={provider.id} 
                  provider={provider} 
                  onDelete={handleDeleteProvider}
                  onSetDefault={handleSetDefault}
                />
              ))}
              {filteredProviders.length === 0 && (
                <Card className="col-span-full">
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <Server className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Local Providers</h3>
                    <p className="text-muted-foreground text-center max-w-md mb-6">
                      Local providers allow you to run models on your own hardware for privacy and cost savings.
                    </p>
                    <Button onClick={() => setIsAddProviderOpen(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add Local Provider
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="custom" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredProviders.map((provider) => (
                <ProviderCard 
                  key={provider.id} 
                  provider={provider} 
                  onDelete={handleDeleteProvider}
                  onSetDefault={handleSetDefault}
                />
              ))}
              {filteredProviders.length === 0 && (
                <Card className="col-span-full">
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <Code className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Custom Providers</h3>
                    <p className="text-muted-foreground text-center max-w-md mb-6">
                      Custom providers allow you to integrate with any API that follows the OpenAI-compatible interface.
                    </p>
                    <Button onClick={() => setIsAddProviderOpen(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add Custom Provider
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-8">
          <h3 className="text-lg font-medium mb-4">Provider Routing Settings</h3>
          <Card>
            <CardHeader>
              <CardTitle>Smart Routing Configuration</CardTitle>
              <CardDescription>
                Configure how SynapseAI routes requests to different providers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="routing-strategy">Default Routing Strategy</Label>
                    <Select defaultValue="priority">
                      <SelectTrigger id="routing-strategy">
                        <SelectValue placeholder="Select strategy" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="priority">Priority-based</SelectItem>
                        <SelectItem value="cost">Cost Optimization</SelectItem>
                        <SelectItem value="latency">Lowest Latency</SelectItem>
                        <SelectItem value="capability">Capability Match</SelectItem>
                        <SelectItem value="round-robin">Round Robin</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-muted-foreground mt-1">
                      How to route requests when no specific provider is specified
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="auto-fallback" defaultChecked />
                    <Label htmlFor="auto-fallback">Automatic Fallback</Label>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Automatically try the next provider if the current one fails
                  </p>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="max-retries">Maximum Retries</Label>
                    <Input id="max-retries" type="number" defaultValue="3" />
                    <p className="text-sm text-muted-foreground mt-1">
                      Maximum number of retry attempts before failing
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="timeout">Request Timeout (ms)</Label>
                    <Input id="timeout" type="number" defaultValue="30000" />
                    <p className="text-sm text-muted-foreground mt-1">
                      How long to wait for a response before timing out
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="text-sm font-medium mb-3">Cost Management</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="daily-budget">Daily Budget ($)</Label>
                    <Input id="daily-budget" type="number" defaultValue="10.00" step="0.01" />
                    <p className="text-sm text-muted-foreground mt-1">
                      Maximum daily spending across all providers
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="alert-threshold">Alert Threshold (%)</Label>
                    <Input id="alert-threshold" type="number" defaultValue="80" />
                    <p className="text-sm text-muted-foreground mt-1">
                      Send alerts when budget usage exceeds this percentage
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end space-x-2">
              <Button variant="outline">Reset to Defaults</Button>
              <Button>Save Changes</Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}

interface ProviderCardProps {
  provider: Provider;
  onDelete: (id: string) => void;
  onSetDefault: (id: string) => void;
}

function ProviderCard({ provider, onDelete, onSetDefault }: ProviderCardProps) {
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [editedProvider, setEditedProvider] = useState<Provider>(provider);

  const handleSaveEdit = () => {
    // In a real app, this would make an API call
    setIsEditOpen(false);
  };

  const getProviderIcon = (type: string) => {
    switch (type) {
      case 'cloud': return <Cloud className="h-5 w-5" />;
      case 'local': return <Server className="h-5 w-5" />;
      case 'custom': return <Code className="h-5 w-5" />;
      default: return <Globe className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-500';
      case 'degraded': return 'bg-yellow-500';
      case 'offline': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'healthy': return 'default';
      case 'degraded': return 'warning';
      case 'offline': return 'destructive';
      default: return 'outline';
    }
  };

  return (
    <Card className={provider.isDefault ? "border-primary/50" : ""}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`p-1.5 rounded-md bg-primary/10`}>
              {getProviderIcon(provider.type)}
            </div>
            <CardTitle className="text-lg">
              {provider.name}
              {provider.isDefault && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="ml-2 bg-primary/10 text-primary border-primary/20">
                        Default
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Default provider for new agents</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </CardTitle>
          </div>
          <div className="flex items-center">
            <Badge variant={getStatusBadgeVariant(provider.status)}>
              {provider.status}
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Provider Actions</DropdownMenuLabel>
                <DropdownMenuItem onClick={() => setIsEditOpen(true)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Provider
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onSetDefault(provider.id)}>
                  <Check className="mr-2 h-4 w-4" />
                  Set as Default
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => onDelete(provider.id)} className="text-destructive">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Provider
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <CardDescription className="flex items-center gap-1 mt-1">
          <span className="capitalize">{provider.type}</span>
          <span className="text-muted-foreground">•</span>
          <span>Priority {provider.priority}</span>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="text-muted-foreground">Models</span>
              <span>{provider.models.length}</span>
            </div>
            <div className="flex flex-wrap gap-1">
              {provider.models.slice(0, 3).map((model) => (
                <Badge key={model} variant="secondary" className="text-xs">
                  {model}
                </Badge>
              ))}
              {provider.models.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{provider.models.length - 3} more
                </Badge>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Latency</p>
              <p>{provider.latency}ms</p>
            </div>
            <div>
              <p className="text-muted-foreground">Cost/Token</p>
              <p>${provider.costPerToken.toFixed(6)}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Max Tokens</p>
              <p>{provider.maxTokens.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Features</p>
              <p>{provider.features.length}</p>
            </div>
          </div>

          <div>
            <div className="flex justify-between text-xs text-muted-foreground mb-1">
              <span>Health</span>
              <span>{provider.status}</span>
            </div>
            <Progress 
              value={provider.status === 'healthy' ? 100 : provider.status === 'degraded' ? 50 : 0} 
              className="h-2" 
            />
          </div>
        </div>
      </CardContent>
      <CardFooter className="pt-1">
        <div className="flex justify-between w-full">
          <Button variant="outline" size="sm" className="text-xs">
            <RefreshCw className="mr-1 h-3 w-3" />
            Test Connection
          </Button>
          <Button variant="outline" size="sm" className="text-xs">
            <Settings className="mr-1 h-3 w-3" />
            Configure
          </Button>
        </div>
      </CardFooter>

      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Provider</DialogTitle>
            <DialogDescription>
              Update provider configuration and settings
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name">Provider Name</Label>
              <Input 
                id="edit-name" 
                value={editedProvider.name}
                onChange={(e) => setEditedProvider({...editedProvider, name: e.target.value})}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-baseUrl">Base URL</Label>
              <Input 
                id="edit-baseUrl" 
                value={editedProvider.baseUrl || ""}
                onChange={(e) => setEditedProvider({...editedProvider, baseUrl: e.target.value})}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-apiKey">API Key</Label>
              <Input 
                id="edit-apiKey" 
                type="password" 
                placeholder="sk-..." 
                value={editedProvider.apiKey || ""}
                onChange={(e) => setEditedProvider({...editedProvider, apiKey: e.target.value})}
              />
            </div>
            <div className="grid gap-2">
              <Label>Priority (1 is highest)</Label>
              <div className="flex items-center gap-4">
                <Slider 
                  value={[editedProvider.priority]} 
                  min={1} 
                  max={10} 
                  step={1}
                  onValueChange={(value) => setEditedProvider({...editedProvider, priority: value[0]})}
                  className="flex-1"
                />
                <span className="w-8 text-center">{editedProvider.priority}</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveEdit}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}