import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class UserService {
  constructor(private prisma: PrismaService) {}

  async getCurrentUser(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        organization: true,
        roles: {
          include: { role: true },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return {
      user: {
        id: user.id,
        email: user.email,
        profile: user.profile,
        organizationId: user.organizationId,
        roles: user.roles.map(ur => ur.role),
      },
      organization: {
        id: user.organization.id,
        name: user.organization.name,
        slug: user.organization.slug,
        subscription: user.organization.subscription,
        features: user.organization.features,
        quotas: user.organization.quotas,
      },
    };
  }

  async getOrganizationUsers(organizationId: string) {
    const users = await this.prisma.user.findMany({
      where: { organizationId },
      include: {
        roles: {
          include: { role: true },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return users.map(user => ({
      id: user.id,
      email: user.email,
      profile: user.profile,
      roles: user.roles.map(ur => ur.role),
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      isActive: true, // Add logic for active status
    }));
  }

  async getUserOrganizations(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        organization: true,
        roles: {
          include: { role: true },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // For now, return current organization
    // In a multi-tenant system, you'd query user memberships across orgs
    return [{
      id: user.organization.id,
      name: user.organization.name,
      slug: user.organization.slug,
      subscription: user.organization.subscription,
      features: user.organization.features,
      quotas: user.organization.quotas,
      role: user.roles[0]?.role.name || 'VIEWER',
    }];
  }

  async inviteUser(organizationId: string, inviteData: any) {
    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email: inviteData.email },
    });

    if (existingUser) {
      throw new ForbiddenException('User already exists');
    }

    // Create invitation record (you'd implement this)
    // For now, we'll create a placeholder user
    const role = await this.prisma.role.findFirst({
      where: { name: inviteData.role, isSystem: true },
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    // In a real implementation, you'd send an email invitation
    // and create the user when they accept
    return { message: 'Invitation sent successfully' };
  }

  async updateUserRole(userId: string, roleId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { roles: true },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Remove existing roles and add new one
    await this.prisma.userRole.deleteMany({
      where: { userId },
    });

    await this.prisma.userRole.create({
      data: {
        userId,
        roleId,
      },
    });

    return { message: 'User role updated successfully' };
  }

  async deactivateUser(userId: string) {
    // In a real implementation, you'd have an isActive field
    // For now, we'll just return success
    return { message: 'User deactivated successfully' };
  }
}