import { Controller, Get, Post, Put, Body, Param, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UserService } from './user.service';

@ApiTags('Users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserController {
  constructor(private userService: UserService) {}

  @Get('me')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'User profile retrieved successfully' })
  async getCurrentUser(@Request() req) {
    return this.userService.getCurrentUser(req.user.sub);
  }

  @Get()
  @ApiOperation({ summary: 'Get organization users' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully' })
  async getOrganizationUsers(@Request() req) {
    return this.userService.getOrganizationUsers(req.user.organizationId);
  }

  @Get('organizations')
  @ApiOperation({ summary: 'Get user organizations' })
  @ApiResponse({ status: 200, description: 'Organizations retrieved successfully' })
  async getUserOrganizations(@Request() req) {
    return this.userService.getUserOrganizations(req.user.sub);
  }

  @Post('invite')
  @ApiOperation({ summary: 'Invite user to organization' })
  @ApiResponse({ status: 201, description: 'User invited successfully' })
  async inviteUser(@Request() req, @Body() inviteData: any) {
    return this.userService.inviteUser(req.user.organizationId, inviteData);
  }

  @Put(':id/role')
  @ApiOperation({ summary: 'Update user role' })
  @ApiResponse({ status: 200, description: 'User role updated successfully' })
  async updateUserRole(@Param('id') userId: string, @Body() roleData: any) {
    return this.userService.updateUserRole(userId, roleData.roleId);
  }

  @Put(':id/deactivate')
  @ApiOperation({ summary: 'Deactivate user' })
  @ApiResponse({ status: 200, description: 'User deactivated successfully' })
  async deactivateUser(@Param('id') userId: string) {
    return this.userService.deactivateUser(userId);
  }
}