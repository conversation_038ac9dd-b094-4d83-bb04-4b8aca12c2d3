import { z } from 'zod';

// Tool Execution Type Schema
export const ToolExecutionTypeSchema = z.enum([
  'sync',
  'async'
]);

// Tool Category Schema
export const ToolCategorySchema = z.enum([
  'retrieval',
  'function',
  'api',
  'database',
  'creative',
  'utility'
]);

// Tool Input Schema Definition
export const ToolInputSchemaSchema = z.record(z.any());

// Tool Output Schema Definition
export const ToolOutputSchemaSchema = z.record(z.any());

// Tool Version Schema
export const ToolVersionSchema = z.object({
  id: z.string().uuid(),
  toolId: z.string().uuid(),
  version: z.string(),
  inputSchema: ToolInputSchemaSchema,
  outputSchema: ToolOutputSchemaSchema,
  handlerUrl: z.string().optional(),
  internalHandlerRef: z.string().optional(),
  config: z.record(z.any()).optional(),
  createdAt: z.date(),
  createdBy: z.string().uuid(),
  isActive: z.boolean()
});

// Tool Schema
export const ToolSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  slug: z.string().min(1).max(100),
  description: z.string().max(500),
  category: ToolCategorySchema,
  executionType: ToolExecutionTypeSchema,
  currentVersion: z.string(),
  organizationId: z.string().uuid(),
  createdBy: z.string().uuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
  isActive: z.boolean(),
  metadata: z.record(z.any()).optional(),
  permissions: z.object({
    canEdit: z.array(z.string()),
    canView: z.array(z.string()),
    canExecute: z.array(z.string())
  }).optional()
});

// Tool Execution Result Schema
export const ToolExecutionResultSchema = z.object({
  id: z.string().uuid(),
  toolId: z.string().uuid(),
  versionId: z.string().uuid(),
  status: z.enum(['success', 'error', 'timeout']),
  input: z.record(z.any()),
  output: z.record(z.any()).optional(),
  error: z.string().optional(),
  executionTime: z.number(),
  startedAt: z.date(),
  completedAt: z.date().optional(),
  userId: z.string().uuid(),
  sessionId: z.string().optional(),
  workflowId: z.string().optional(),
  metadata: z.record(z.any()).optional()
});

// Tool Audit Log Schema
export const ToolAuditLogSchema = z.object({
  id: z.string().uuid(),
  toolId: z.string().uuid(),
  action: z.enum(['created', 'updated', 'deleted', 'version_added', 'version_activated', 'executed']),
  userId: z.string().uuid(),
  timestamp: z.date(),
  details: z.record(z.any())
});

// Type exports
export type ToolExecutionType = z.infer<typeof ToolExecutionTypeSchema>;
export type ToolCategory = z.infer<typeof ToolCategorySchema>;
export type ToolInputSchema = z.infer<typeof ToolInputSchemaSchema>;
export type ToolOutputSchema = z.infer<typeof ToolOutputSchemaSchema>;
export type ToolVersion = z.infer<typeof ToolVersionSchema>;
export type Tool = z.infer<typeof ToolSchema>;
export type ToolExecutionResult = z.infer<typeof ToolExecutionResultSchema>;
export type ToolAuditLog = z.infer<typeof ToolAuditLogSchema>;