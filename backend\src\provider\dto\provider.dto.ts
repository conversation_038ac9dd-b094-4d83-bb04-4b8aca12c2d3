import { IsString, IsOptional, IsObject, IsBoolean, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

enum ProviderType {
  OPENAI = 'OPENAI',
  CLAUDE = 'CLAUDE',
  GEMINI = 'GEMINI',
  MISTRAL = 'MISTRAL',
  GROQ = 'GROQ',
  OLLAMA = 'OLLAMA',
}

export class CreateProviderDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ enum: ProviderType })
  @IsEnum(ProviderType)
  type: ProviderType;

  @ApiProperty()
  @IsObject()
  config: any;

  @ApiProperty()
  @IsObject()
  credentials: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateProviderDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ enum: ProviderType, required: false })
  @IsOptional()
  @IsEnum(ProviderType)
  type?: ProviderType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  config?: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  credentials?: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}