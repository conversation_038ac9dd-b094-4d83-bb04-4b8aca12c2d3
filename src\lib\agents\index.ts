import { Database, SessionManager, QuotaManager } from '../database';
import { ProviderManager } from '../providers';
import { getAPIX } from '../apix';

export interface Agent {
  id: string;
  name: string;
  description: string;
  prompt_template_id: string;
  provider_id: string;
  config: {
    model?: string;
    temperature?: number;
    max_tokens?: number;
    memory_type?: 'conversation' | 'semantic' | 'none';
    memory_limit?: number;
    tools?: string[];
    capabilities?: string[];
  };
  memory_config: {
    type: string;
    limit: number;
    retention_policy: string;
  };
  version: number;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface AgentExecution {
  id: string;
  agent_id: string;
  session_id: string;
  input: string;
  output: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    cost: number;
  };
  latency: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error?: string;
  created_at: string;
}

export class AgentManager {
  private db: Database;
  private sessionManager: SessionManager;
  private quotaManager: QuotaManager;
  private providerManager: ProviderManager;
  private organizationId: string;

  constructor(organizationId: string) {
    this.organizationId = organizationId;
    this.db = new Database(organizationId);
    this.sessionManager = new SessionManager(organizationId);
    this.quotaManager = new QuotaManager(organizationId);
    this.providerManager = new ProviderManager(organizationId);
  }

  // Create new agent
  async createAgent(
    name: string,
    description: string,
    promptTemplateId: string,
    providerId: string,
    config: any,
    memoryConfig: any,
    userId: string
  ): Promise<Agent> {
    // Validate prompt template exists
    const templates = await this.db.orgQuery(
      'prompt_templates',
      'id = $1',
      [promptTemplateId]
    );

    if (templates.length === 0) {
      throw new Error('Prompt template not found');
    }

    // Validate provider exists
    const providers = await this.db.orgQuery(
      'providers',
      'id = $1 AND is_active = true',
      [providerId]
    );

    if (providers.length === 0) {
      throw new Error('Provider not found or inactive');
    }

    // Create agent
    const agent = await this.db.orgInsert('agents', {
      name,
      description,
      prompt_template_id: promptTemplateId,
      provider_id: providerId,
      config,
      memory_config: memoryConfig,
      version: 1,
      is_active: true,
      created_by: userId,
    });

    // Emit agent created event
    const apix = getAPIX();
    await apix.publishEvent({
      type: 'agent:created',
      entityType: 'agent',
      entityId: agent.id,
      organizationId: this.organizationId,
      userId,
      data: { name, description, config }
    });

    return this.formatAgent(agent);
  }

  // Get agent by ID
  async getAgent(agentId: string): Promise<Agent | null> {
    const agents = await this.db.orgQuery(
      'agents',
      'id = $1',
      [agentId]
    );

    return agents.length > 0 ? this.formatAgent(agents[0]) : null;
  }

  // List all agents
  async listAgents(filters: {
    active?: boolean;
    created_by?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<Agent[]> {
    let whereClause = '';
    const params: any[] = [];

    if (filters.active !== undefined) {
      whereClause += ` AND is_active = $${params.length + 1}`;
      params.push(filters.active);
    }

    if (filters.created_by) {
      whereClause += ` AND created_by = $${params.length + 1}`;
      params.push(filters.created_by);
    }

    const limitClause = filters.limit ? `LIMIT ${filters.limit}` : '';
    const offsetClause = filters.offset ? `OFFSET ${filters.offset}` : '';

    const agents = await this.db.orgQuery(
      'agents',
      `${whereClause} ORDER BY created_at DESC ${limitClause} ${offsetClause}`,
      params
    );

    return agents.map(agent => this.formatAgent(agent));
  }

  // Update agent
  async updateAgent(
    agentId: string,
    updates: Partial<{
      name: string;
      description: string;
      prompt_template_id: string;
      provider_id: string;
      config: any;
      memory_config: any;
      is_active: boolean;
    }>,
    userId: string
  ): Promise<Agent> {
    const agent = await this.db.orgUpdate('agents', agentId, {
      ...updates,
      version: this.db.query('SELECT version + 1 FROM agents WHERE id = $1', [agentId])
    });

    // Emit agent updated event
    const apix = getAPIX();
    await apix.publishEvent({
      type: 'agent:updated',
      entityType: 'agent',
      entityId: agentId,
      organizationId: this.organizationId,
      userId,
      data: { updates }
    });

    return this.formatAgent(agent);
  }

  // Delete agent
  async deleteAgent(agentId: string, userId: string): Promise<boolean> {
    const success = await this.db.orgDelete('agents', agentId);

    if (success) {
      // Emit agent deleted event
      const apix = getAPIX();
      await apix.publishEvent({
        type: 'agent:deleted',
        entityType: 'agent',
        entityId: agentId,
        organizationId: this.organizationId,
        userId,
        data: {}
      });
    }

    return success;
  }

  // Execute agent with input
  async executeAgent(
    agentId: string,
    input: string,
    userId: string,
    sessionId?: string,
    options: {
      stream?: boolean;
      context?: Record<string, any>;
    } = {}
  ): Promise<AgentExecution> {
    const startTime = Date.now();

    // Get agent details
    const agent = await this.getAgent(agentId);
    if (!agent) {
      throw new Error('Agent not found');
    }

    if (!agent.is_active) {
      throw new Error('Agent is not active');
    }

    // Create or get session
    if (!sessionId) {
      sessionId = await this.sessionManager.createSession(
        userId,
        'agent',
        { agent_id: agentId, ...options.context }
      );
    }

    // Get session for memory context
    const session = await this.sessionManager.getSession(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    try {
      // Check quota
      await this.quotaManager.checkQuota('agent_executions', 1);

      // Get prompt template
      const templates = await this.db.orgQuery(
        'prompt_templates',
        'id = $1',
        [agent.prompt_template_id]
      );

      if (templates.length === 0) {
        throw new Error('Prompt template not found');
      }

      const template = templates[0];

      // Build prompt with memory context
      const prompt = await this.buildPromptWithMemory(
        template.template,
        input,
        session.memory,
        agent.config
      );

      // Emit execution started event
      const apix = getAPIX();
      await apix.publishEvent({
        type: 'agent:execution:started',
        entityType: 'agent',
        entityId: agentId,
        organizationId: this.organizationId,
        userId,
        data: { input, sessionId, prompt: prompt.substring(0, 200) + '...' }
      });

      // Execute with provider
      const result = await this.providerManager.executeCompletion(prompt, {
        model: agent.config.model,
        maxTokens: agent.config.max_tokens || 1000,
        temperature: agent.config.temperature || 0.7,
        stream: options.stream,
        requirements: {
          capabilities: agent.config.capabilities
        }
      });

      const latency = Date.now() - startTime;

      // Update session memory
      await this.updateAgentMemory(sessionId, input, result.content, agent.memory_config);

      // Create execution record
      const execution: AgentExecution = {
        id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        agent_id: agentId,
        session_id: sessionId,
        input,
        output: result.content,
        usage: result.usage,
        latency,
        status: 'completed',
        created_at: new Date().toISOString()
      };

      // Track usage and quota
      await this.quotaManager.trackUsage(
        'agent_executions',
        agentId,
        1,
        result.usage.total_tokens * 0.001, // Simplified cost calculation
        {
          tokens: result.usage.total_tokens,
          latency,
          model: agent.config.model
        }
      );

      // Emit execution completed event
      await apix.publishEvent({
        type: 'agent:execution:completed',
        entityType: 'agent',
        entityId: agentId,
        organizationId: this.organizationId,
        userId,
        data: {
          executionId: execution.id,
          sessionId,
          usage: execution.usage,
          latency,
          output: result.content.substring(0, 200) + '...'
        }
      });

      return execution;

    } catch (error) {
      const latency = Date.now() - startTime;

      // Create failed execution record
      const execution: AgentExecution = {
        id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        agent_id: agentId,
        session_id: sessionId,
        input,
        output: '',
        usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0, cost: 0 },
        latency,
        status: 'failed',
        error: error.message,
        created_at: new Date().toISOString()
      };

      // Emit execution failed event
      const apix = getAPIX();
      await apix.publishEvent({
        type: 'agent:execution:failed',
        entityType: 'agent',
        entityId: agentId,
        organizationId: this.organizationId,
        userId,
        data: {
          executionId: execution.id,
          sessionId,
          error: error.message,
          latency
        }
      });

      throw error;
    }
  }

  // Build prompt with memory context
  private async buildPromptWithMemory(
    template: string,
    input: string,
    memory: Record<string, any>,
    config: any
  ): Promise<string> {
    let prompt = template;

    // Replace template variables
    prompt = prompt.replace(/\{\{input\}\}/g, input);
    prompt = prompt.replace(/\{\{timestamp\}\}/g, new Date().toISOString());

    // Add memory context based on memory type
    if (config.memory_type === 'conversation' && memory.conversation) {
      const conversationHistory = memory.conversation
        .slice(-10) // Last 10 exchanges
        .map((exchange: any) => `Human: ${exchange.input}\nAssistant: ${exchange.output}`)
        .join('\n\n');
      
      prompt = prompt.replace(/\{\{memory\}\}/g, conversationHistory);
    } else if (config.memory_type === 'semantic' && memory.semantic) {
      // Add relevant semantic memories
      const relevantMemories = memory.semantic
        .slice(0, 5) // Top 5 relevant memories
        .map((mem: any) => mem.content)
        .join('\n');
      
      prompt = prompt.replace(/\{\{memory\}\}/g, relevantMemories);
    } else {
      prompt = prompt.replace(/\{\{memory\}\}/g, '');
    }

    return prompt;
  }

  // Update agent memory after execution
  private async updateAgentMemory(
    sessionId: string,
    input: string,
    output: string,
    memoryConfig: any
  ): Promise<void> {
    const session = await this.sessionManager.getSession(sessionId);
    if (!session) return;

    const memory = session.memory || {};

    if (memoryConfig.type === 'conversation') {
      if (!memory.conversation) {
        memory.conversation = [];
      }

      memory.conversation.push({
        input,
        output,
        timestamp: new Date().toISOString()
      });

      // Limit conversation history
      const limit = memoryConfig.limit || 50;
      if (memory.conversation.length > limit) {
        memory.conversation = memory.conversation.slice(-limit);
      }
    } else if (memoryConfig.type === 'semantic') {
      // For semantic memory, you would typically:
      // 1. Generate embeddings for input/output
      // 2. Store in vector database
      // 3. Implement similarity search
      // This is a simplified version
      if (!memory.semantic) {
        memory.semantic = [];
      }

      memory.semantic.push({
        content: `${input} -> ${output}`,
        timestamp: new Date().toISOString(),
        // embedding: await generateEmbedding(input + output) // Would implement this
      });

      const limit = memoryConfig.limit || 100;
      if (memory.semantic.length > limit) {
        memory.semantic = memory.semantic.slice(-limit);
      }
    }

    await this.sessionManager.updateSessionMemory(sessionId, memory);
  }

  // Get agent execution history
  async getExecutionHistory(
    agentId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<any[]> {
    // This would typically be stored in a separate executions table
    // For now, getting from analytics events
    const events = await this.db.orgQuery(
      'analytics_events',
      'entity_type = $1 AND entity_id = $2 AND event_type LIKE $3 ORDER BY timestamp DESC LIMIT $4 OFFSET $5',
      ['agent', agentId, 'agent:execution:%', limit, offset]
    );

    return events;
  }

  // Get agent analytics
  async getAgentAnalytics(agentId: string, timeRange: string = '7d'): Promise<any> {
    const analytics = await this.db.query(
      `SELECT 
         COUNT(*) as total_executions,
         COUNT(CASE WHEN event_type = 'agent:execution:completed' THEN 1 END) as successful_executions,
         COUNT(CASE WHEN event_type = 'agent:execution:failed' THEN 1 END) as failed_executions,
         AVG(CAST(data->>'latency' AS FLOAT)) as avg_latency,
         SUM(CAST(data->>'usage'->>'total_tokens' AS INTEGER)) as total_tokens,
         SUM(CAST(data->>'usage'->>'cost' AS FLOAT)) as total_cost
       FROM analytics_events 
       WHERE organization_id = $1 
         AND entity_type = 'agent'
         AND entity_id = $2
         AND event_type LIKE 'agent:execution:%'
         AND timestamp > NOW() - INTERVAL '${timeRange}'`,
      [this.organizationId, agentId]
    );

    return analytics[0] || {
      total_executions: 0,
      successful_executions: 0,
      failed_executions: 0,
      avg_latency: 0,
      total_tokens: 0,
      total_cost: 0
    };
  }

  // Test agent with sample input
  async testAgent(
    agentId: string,
    testInput: string,
    userId: string
  ): Promise<AgentExecution> {
    // Create temporary test session
    const testSessionId = await this.sessionManager.createSession(
      userId,
      'agent',
      { agent_id: agentId, test_mode: true },
      300 // 5 minutes expiry for test
    );

    try {
      const result = await this.executeAgent(
        agentId,
        testInput,
        userId,
        testSessionId,
        { context: { test_mode: true } }
      );

      return result;
    } finally {
      // Clean up test session
      await this.sessionManager.deleteSession(testSessionId);
    }
  }

  // Format agent for API response
  private formatAgent(agent: any): Agent {
    return {
      id: agent.id,
      name: agent.name,
      description: agent.description,
      prompt_template_id: agent.prompt_template_id,
      provider_id: agent.provider_id,
      config: agent.config,
      memory_config: agent.memory_config,
      version: agent.version,
      is_active: agent.is_active,
      created_by: agent.created_by,
      created_at: agent.created_at,
      updated_at: agent.updated_at
    };
  }
}