import { IsString, IsOptional, IsObject, IsBoolean, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

enum ToolType {
  API = 'API',
  WEBHOOK = 'WEBHOOK',
  DATABASE = 'DATABASE',
  FILE_SYSTEM = 'FILE_SYSTEM',
  CUSTOM = 'CUSTOM',
}

export class CreateToolDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ enum: ToolType })
  @IsEnum(ToolType)
  type: ToolType;

  @ApiProperty()
  @IsObject()
  config: any;

  @ApiProperty()
  @IsObject()
  schema: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateToolDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ enum: ToolType, required: false })
  @IsOptional()
  @IsEnum(ToolType)
  type?: ToolType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  config?: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  schema?: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class ExecuteToolDto {
  @ApiProperty()
  @IsObject()
  input: any;
}