import axios from 'axios';

export interface ToolExecutionResult {
  success: boolean;
  data?: any;
  error?: string;
  executionTime: number;
  cost?: number;
}

class RealToolService {
  async executeWebSearch(query: string, limit: number = 10): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      const response = await axios.get('https://serpapi.com/search', {
        params: {
          q: query,
          api_key: process.env.SERP_API_KEY,
          engine: 'google',
          num: limit,
        },
        timeout: 10000,
      });

      const results = response.data.organic_results?.map((result: any) => ({
        title: result.title,
        link: result.link,
        snippet: result.snippet,
        position: result.position,
      })) || [];

      return {
        success: true,
        data: {
          query,
          results,
          total: results.length,
        },
        executionTime: Date.now() - startTime,
        cost: 0.01,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        executionTime: Date.now() - startTime,
      };
    }
  }

  async executeCalculator(expression: string): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
      
      if (sanitized !== expression) {
        throw new Error('Invalid characters in expression');
      }

      const result = Function(`"use strict"; return (${sanitized})`)();
      
      if (typeof result !== 'number' || !isFinite(result)) {
        throw new Error('Invalid calculation result');
      }

      return {
        success: true,
        data: {
          expression: sanitized,
          result,
        },
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        executionTime: Date.now() - startTime,
      };
    }
  }

  async executeApiCall(config: {
    url: string;
    method: string;
    headers?: Record<string, string>;
    body?: any;
    timeout?: number;
  }): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      const response = await axios({
        url: config.url,
        method: config.method as any,
        headers: {
          'Content-Type': 'application/json',
          ...config.headers,
        },
        data: config.body,
        timeout: config.timeout || 30000,
        validateStatus: () => true,
      });

      return {
        success: response.status >= 200 && response.status < 300,
        data: {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: response.data,
        },
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        executionTime: Date.now() - startTime,
      };
    }
  }

  async executeEmailSend(config: {
    to: string;
    subject: string;
    content: string;
    from?: string;
  }): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      const sgMail = require('@sendgrid/mail');
      sgMail.setApiKey(process.env.SENDGRID_API_KEY);

      const msg = {
        to: config.to,
        from: config.from || process.env.DEFAULT_FROM_EMAIL,
        subject: config.subject,
        html: config.content,
      };

      const response = await sgMail.send(msg);

      return {
        success: true,
        data: {
          messageId: response[0].headers['x-message-id'],
          statusCode: response[0].statusCode,
        },
        executionTime: Date.now() - startTime,
        cost: 0.001,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        executionTime: Date.now() - startTime,
      };
    }
  }
}

export const realToolService = new RealToolService();