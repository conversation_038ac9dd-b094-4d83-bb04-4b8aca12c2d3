import { Database, SessionManager, QuotaManager } from './database';
import { getAPIX } from './apix';
import { Tool, ToolVersion, ToolExecutionResult, ToolCategory, ToolExecutionType } from './types/tool';
import { v4 as uuidv4 } from 'uuid';

export class ToolManager {
  private db: Database;
  private sessionManager: SessionManager;
  private quotaManager: QuotaManager;
  private apix = getAPIX();
  private handlers = new Map<string, (input: any) => Promise<any>>();

  constructor(organizationId: string) {
    this.db = new Database(organizationId);
    this.sessionManager = new SessionManager(organizationId);
    this.quotaManager = new QuotaManager(organizationId);
    this.registerBuiltinHandlers();
  }

  // Register built-in handlers
  private registerBuiltinHandlers(): void {
    // Calculator handler
    this.handlers.set('calculator', async (input: any) => {
      const { expression } = input;
      try {
        // Simple expression evaluator (in production, use a proper math library)
        const result = Function(`"use strict"; return (${expression})`)();
        return { result, expression };
      } catch (error) {
        throw new Error(`Invalid expression: ${error.message}`);
      }
    });

    // Web search handler (mock)
    this.handlers.set('web-search', async (input: any) => {
      const { query, limit = 5 } = input;
      // Mock search results
      return {
        query,
        results: Array.from({ length: Math.min(limit, 5) }, (_, i) => ({
          title: `Search result ${i + 1} for "${query}"`,
          url: `https://example.com/result-${i + 1}`,
          snippet: `This is a mock search result snippet for ${query}. It contains relevant information about the search query.`
        }))
      };
    });

    // API caller handler
    this.handlers.set('api-caller', async (input: any) => {
      const { url, method = 'GET', headers = {}, body } = input;
      
      try {
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            ...headers
          },
          body: body ? JSON.stringify(body) : undefined
        });

        const data = await response.json();
        
        return {
          status: response.status,
          statusText: response.statusText,
          data,
          headers: Object.fromEntries(response.headers.entries())
        };
      } catch (error) {
        throw new Error(`API call failed: ${error.message}`);
      }
    });

    // Database query handler (mock)
    this.handlers.set('database-query', async (input: any) => {
      const { query, parameters = [] } = input;
      
      // Mock database results
      return {
        query,
        parameters,
        results: [
          { id: 1, name: 'Sample Record 1', value: 'Data 1' },
          { id: 2, name: 'Sample Record 2', value: 'Data 2' }
        ],
        rowCount: 2
      };
    });
  }

  // Register custom handler
  registerHandler(name: string, handler: (input: any) => Promise<any>): void {
    this.handlers.set(name, handler);
  }

  // Create new tool
  async createTool(
    toolData: Omit<Tool, 'id' | 'createdAt' | 'updatedAt'>,
    userId: string
  ): Promise<Tool> {
    await this.quotaManager.checkQuota('tools', 1);

    const tool: Tool = {
      ...toolData,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: userId,
    };

    const savedTool = await this.db.orgInsert('tools', {
      id: tool.id,
      name: tool.name,
      slug: tool.slug,
      description: tool.description,
      category: tool.category,
      execution_type: tool.executionType,
      current_version: tool.currentVersion,
      is_active: tool.isActive,
      created_by: tool.createdBy,
      metadata: tool.metadata || {},
      permissions: tool.permissions || {},
    });

    await this.quotaManager.consumeQuota('tools', 1);

    // Emit creation event
    await this.apix.publishEvent({
      type: 'tool_created',
      entityType: 'tool',
      entityId: tool.id,
      organizationId: toolData.organizationId,
      userId,
      data: { tool: savedTool },
    });

    return this.mapDbToTool(savedTool);
  }

  // Get tool by ID
  async getTool(toolId: string): Promise<Tool | null> {
    const tools = await this.db.orgQuery('tools', 'id = $1', [toolId]);
    return tools.length > 0 ? this.mapDbToTool(tools[0]) : null;
  }

  // List tools with filtering and pagination
  async listTools(
    filters: {
      category?: ToolCategory;
      executionType?: ToolExecutionType;
      isActive?: boolean;
      search?: string;
    } = {},
    pagination: { limit?: number; offset?: number } = {}
  ): Promise<{ tools: Tool[]; total: number }> {
    const { limit = 50, offset = 0 } = pagination;
    let whereClause = '';
    const params: any[] = [];

    if (filters.category) {
      whereClause += ` AND category = $${params.length + 1}`;
      params.push(filters.category);
    }

    if (filters.executionType) {
      whereClause += ` AND execution_type = $${params.length + 1}`;
      params.push(filters.executionType);
    }

    if (filters.isActive !== undefined) {
      whereClause += ` AND is_active = $${params.length + 1}`;
      params.push(filters.isActive);
    }

    if (filters.search) {
      whereClause += ` AND (name ILIKE $${params.length + 1} OR description ILIKE $${params.length + 1})`;
      params.push(`%${filters.search}%`);
    }

    const tools = await this.db.orgQuery(
      'tools',
      `${whereClause} ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`,
      [...params, limit, offset]
    );

    const countResult = await this.db.orgQuery(
      'tools',
      `${whereClause}`,
      params,
      'COUNT(*) as count'
    );

    return {
      tools: tools.map(this.mapDbToTool),
      total: parseInt(countResult[0].count),
    };
  }

  // Update tool
  async updateTool(
    toolId: string,
    updates: Partial<Omit<Tool, 'id' | 'createdAt' | 'organizationId' | 'createdBy'>>,
    userId: string
  ): Promise<Tool> {
    const updateData: any = {
      ...updates,
      updated_at: new Date(),
    };

    // Map field names to database columns
    if (updates.executionType !== undefined) {
      updateData.execution_type = updates.executionType;
      delete updateData.executionType;
    }
    if (updates.currentVersion !== undefined) {
      updateData.current_version = updates.currentVersion;
      delete updateData.currentVersion;
    }
    if (updates.isActive !== undefined) {
      updateData.is_active = updates.isActive;
      delete updateData.isActive;
    }

    const updatedTool = await this.db.orgUpdate('tools', toolId, updateData);

    // Emit update event
    await this.apix.publishEvent({
      type: 'tool_updated',
      entityType: 'tool',
      entityId: toolId,
      organizationId: this.db['organizationId'],
      userId,
      data: { updates, tool: updatedTool },
    });

    return this.mapDbToTool(updatedTool);
  }

  // Delete tool
  async deleteTool(toolId: string, userId: string): Promise<boolean> {
    // Check if tool is being used by any agents
    const agentsUsingTool = await this.db.orgQuery(
      'agents',
      'tools @> $1',
      [JSON.stringify([toolId])]
    );

    if (agentsUsingTool.length > 0) {
      throw new Error('Cannot delete tool that is being used by agents');
    }

    const deleted = await this.db.orgDelete('tools', toolId);

    if (deleted) {
      // Also delete all versions
      await this.db.query(
        'DELETE FROM tool_versions WHERE organization_id = $1 AND tool_id = $2',
        [this.db['organizationId'], toolId]
      );

      // Emit deletion event
      await this.apix.publishEvent({
        type: 'tool_deleted',
        entityType: 'tool',
        entityId: toolId,
        organizationId: this.db['organizationId'],
        userId,
        data: { toolId },
      });
    }

    return deleted;
  }

  // Add tool version
  async addToolVersion(
    toolId: string,
    versionData: Omit<ToolVersion, 'id' | 'toolId' | 'createdAt'>,
    userId: string
  ): Promise<ToolVersion> {
    const version: ToolVersion = {
      ...versionData,
      id: uuidv4(),
      toolId,
      createdAt: new Date(),
      createdBy: userId,
    };

    const savedVersion = await this.db.orgInsert('tool_versions', {
      id: version.id,
      tool_id: version.toolId,
      version: version.version,
      input_schema: version.inputSchema,
      output_schema: version.outputSchema,
      handler_url: version.handlerUrl,
      internal_handler_ref: version.internalHandlerRef,
      config: version.config || {},
      is_active: version.isActive,
      created_by: version.createdBy,
    });

    // If this version is active, deactivate others and update tool's current version
    if (version.isActive) {
      await this.activateToolVersion(toolId, version.id);
    }

    // Emit version added event
    await this.apix.publishEvent({
      type: 'tool:version_added',
      entityType: 'tool',
      entityId: toolId,
      organizationId: this.db['organizationId'],
      userId,
      data: { version: savedVersion },
    });

    return this.mapDbToToolVersion(savedVersion);
  }

  // Activate tool version
  async activateToolVersion(toolId: string, versionId: string): Promise<void> {
    await this.db.transaction(async (db) => {
      // Deactivate all versions for this tool
      await db.query(
        'UPDATE tool_versions SET is_active = false WHERE organization_id = $1 AND tool_id = $2',
        [this.db['organizationId'], toolId]
      );

      // Activate the specified version
      await db.query(
        'UPDATE tool_versions SET is_active = true WHERE organization_id = $1 AND id = $2',
        [this.db['organizationId'], versionId]
      );

      // Update tool's current version
      const version = await db.orgQuery('tool_versions', 'id = $1', [versionId]);
      if (version.length > 0) {
        await db.orgUpdate('tools', toolId, { current_version: version[0].version });
      }
    });

    // Emit activation event
    await this.apix.publishEvent({
      type: 'tool:version_activated',
      entityType: 'tool',
      entityId: toolId,
      organizationId: this.db['organizationId'],
      userId: 'system',
      data: { versionId },
    });
  }

  // Get tool versions
  async getToolVersions(toolId: string): Promise<ToolVersion[]> {
    const versions = await this.db.orgQuery(
      'tool_versions',
      'tool_id = $1 ORDER BY created_at DESC',
      [toolId]
    );

    return versions.map(this.mapDbToToolVersion);
  }

  // Get specific tool version
  async getToolVersion(toolId: string, versionId: string): Promise<ToolVersion | null> {
    const versions = await this.db.orgQuery(
      'tool_versions',
      'tool_id = $1 AND id = $2',
      [toolId, versionId]
    );

    return versions.length > 0 ? this.mapDbToToolVersion(versions[0]) : null;
  }

  // Execute tool
  async executeTool(
    toolId: string,
    input: any,
    userId: string,
    sessionId?: string,
    versionId?: string
  ): Promise<ToolExecutionResult> {
    const tool = await this.getTool(toolId);
    if (!tool) {
      throw new Error('Tool not found');
    }

    if (!tool.isActive) {
      throw new Error('Tool is not active');
    }

    // Get the version to execute
    let version: ToolVersion | null;
    if (versionId) {
      version = await this.getToolVersion(toolId, versionId);
    } else {
      // Get active version
      const versions = await this.db.orgQuery(
        'tool_versions',
        'tool_id = $1 AND is_active = true',
        [toolId]
      );
      version = versions.length > 0 ? this.mapDbToToolVersion(versions[0]) : null;
    }

    if (!version) {
      throw new Error('No active version found for tool');
    }

    // Check quotas
    await this.quotaManager.checkQuota('tool_executions', 1);

    const executionId = uuidv4();
    const startTime = Date.now();

    // Create execution record
    const executionResult: ToolExecutionResult = {
      id: executionId,
      toolId,
      versionId: version.id,
      status: 'success',
      input,
      output: {},
      executionTime: 0,
      startedAt: new Date(startTime),
      userId,
      sessionId,
      metadata: {}
    };

    try {
      // Emit execution started event
      await this.apix.publishEvent({
        type: 'tool:execution:started',
        entityType: 'tool',
        entityId: toolId,
        organizationId: tool.organizationId,
        userId,
        data: { 
          input, 
          sessionId, 
          executionId,
          versionId: version.id 
        },
      });

      let output: any;

      // Execute based on handler type
      if (version.handlerUrl) {
        // HTTP handler
        output = await this.executeHttpHandler(version.handlerUrl, input);
      } else if (version.internalHandlerRef) {
        // Internal handler
        output = await this.executeInternalHandler(version.internalHandlerRef, input);
      } else {
        throw new Error('No handler configured for this tool version');
      }

      executionResult.output = output;
      executionResult.executionTime = Date.now() - startTime;
      executionResult.completedAt = new Date();

      // Store execution result
      await this.db.orgInsert('tool_executions', {
        id: executionResult.id,
        tool_id: toolId,
        version_id: version.id,
        status: executionResult.status,
        input: executionResult.input,
        output: executionResult.output,
        execution_time: executionResult.executionTime,
        started_at: executionResult.startedAt,
        completed_at: executionResult.completedAt,
        user_id: userId,
        session_id: sessionId,
        metadata: executionResult.metadata || {}
      });

      // Track usage
      await this.quotaManager.trackUsage('tool_executions', toolId, 1);

      // Emit completion event
      await this.apix.publishEvent({
        type: 'tool:execution:completed',
        entityType: 'tool',
        entityId: toolId,
        organizationId: tool.organizationId,
        userId,
        data: { 
          sessionId, 
          executionId,
          result: output,
          executionTime: executionResult.executionTime
        },
      });

      return executionResult;

    } catch (error) {
      executionResult.status = 'error';
      executionResult.error = error.message;
      executionResult.executionTime = Date.now() - startTime;
      executionResult.completedAt = new Date();

      // Store error result
      await this.db.orgInsert('tool_executions', {
        id: executionResult.id,
        tool_id: toolId,
        version_id: version.id,
        status: executionResult.status,
        input: executionResult.input,
        error: executionResult.error,
        execution_time: executionResult.executionTime,
        started_at: executionResult.startedAt,
        completed_at: executionResult.completedAt,
        user_id: userId,
        session_id: sessionId,
        metadata: executionResult.metadata || {}
      });

      // Emit error event
      await this.apix.publishEvent({
        type: 'tool:execution:error',
        entityType: 'tool',
        entityId: toolId,
        organizationId: tool.organizationId,
        userId,
        data: { 
          sessionId, 
          executionId,
          error: error.message 
        },
      });

      throw error;
    }
  }

  // Execute HTTP handler
  private async executeHttpHandler(url: string, input: any): Promise<any> {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(input),
    });

    if (!response.ok) {
      throw new Error(`HTTP handler failed: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  // Execute internal handler
  private async executeInternalHandler(handlerRef: string, input: any): Promise<any> {
    const handler = this.handlers.get(handlerRef);
    if (!handler) {
      throw new Error(`Internal handler not found: ${handlerRef}`);
    }

    return await handler(input);
  }

  // Get tool execution history
  async getExecutionHistory(
    toolId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ executions: ToolExecutionResult[]; total: number }> {
    const executions = await this.db.orgQuery(
      'tool_executions',
      'tool_id = $1 ORDER BY started_at DESC LIMIT $2 OFFSET $3',
      [toolId, limit, offset]
    );

    const countResult = await this.db.orgQuery(
      'tool_executions',
      'tool_id = $1',
      [toolId],
      'COUNT(*) as count'
    );

    return {
      executions: executions.map(this.mapDbToToolExecutionResult),
      total: parseInt(countResult[0].count),
    };
  }

  // Get tool analytics
  async getToolAnalytics(
    toolId: string,
    timeRange: { start: Date; end: Date }
  ): Promise<{
    totalExecutions: number;
    successRate: number;
    avgExecutionTime: number;
    executionsByDay: Array<{ date: string; count: number }>;
  }> {
    const analytics = await this.db.query(`
      SELECT 
        COUNT(*) as total_executions,
        AVG(CASE WHEN status = 'success' THEN 1.0 ELSE 0.0 END) as success_rate,
        AVG(execution_time) as avg_execution_time,
        DATE(started_at) as execution_date,
        COUNT(*) as daily_count
      FROM tool_executions 
      WHERE organization_id = $1 
        AND tool_id = $2 
        AND started_at >= $3 
        AND started_at <= $4
      GROUP BY DATE(started_at)
      ORDER BY execution_date DESC
    `, [this.db['organizationId'], toolId, timeRange.start, timeRange.end]);

    const summary = analytics.length > 0 ? analytics[0] : {
      total_executions: 0,
      success_rate: 0,
      avg_execution_time: 0
    };

    return {
      totalExecutions: parseInt(summary.total_executions) || 0,
      successRate: parseFloat(summary.success_rate) || 0,
      avgExecutionTime: parseFloat(summary.avg_execution_time) || 0,
      executionsByDay: analytics.map(row => ({
        date: row.execution_date,
        count: parseInt(row.daily_count)
      }))
    };
  }

  // Helper methods to map database records to types
  private mapDbToTool(dbRecord: any): Tool {
    return {
      id: dbRecord.id,
      name: dbRecord.name,
      slug: dbRecord.slug,
      description: dbRecord.description,
      category: dbRecord.category,
      executionType: dbRecord.execution_type,
      currentVersion: dbRecord.current_version,
      organizationId: dbRecord.organization_id,
      createdBy: dbRecord.created_by,
      createdAt: dbRecord.created_at,
      updatedAt: dbRecord.updated_at,
      isActive: dbRecord.is_active,
      metadata: dbRecord.metadata || {},
      permissions: dbRecord.permissions || {}
    };
  }

  private mapDbToToolVersion(dbRecord: any): ToolVersion {
    return {
      id: dbRecord.id,
      toolId: dbRecord.tool_id,
      version: dbRecord.version,
      inputSchema: dbRecord.input_schema,
      outputSchema: dbRecord.output_schema,
      handlerUrl: dbRecord.handler_url,
      internalHandlerRef: dbRecord.internal_handler_ref,
      config: dbRecord.config || {},
      createdAt: dbRecord.created_at,
      createdBy: dbRecord.created_by,
      isActive: dbRecord.is_active
    };
  }

  private mapDbToToolExecutionResult(dbRecord: any): ToolExecutionResult {
    return {
      id: dbRecord.id,
      toolId: dbRecord.tool_id,
      versionId: dbRecord.version_id,
      status: dbRecord.status,
      input: dbRecord.input,
      output: dbRecord.output,
      error: dbRecord.error,
      executionTime: dbRecord.execution_time,
      startedAt: dbRecord.started_at,
      completedAt: dbRecord.completed_at,
      userId: dbRecord.user_id,
      sessionId: dbRecord.session_id,
      workflowId: dbRecord.workflow_id,
      metadata: dbRecord.metadata || {}
    };
  }
}

// Singleton pattern for tool manager
const toolManagers = new Map<string, ToolManager>();

export function getToolManager(organizationId: string): ToolManager {
  if (!toolManagers.has(organizationId)) {
    toolManagers.set(organizationId, new ToolManager(organizationId));
  }
  return toolManagers.get(organizationId)!;
}