import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { WorkflowService } from './workflow.service';
import { CreateWorkflowDto, UpdateWorkflowDto, ExecuteWorkflowDto } from './dto/workflow.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Workflows')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('workflows')
export class WorkflowController {
  constructor(private workflowService: WorkflowService) {}

  @Post()
  @ApiOperation({ summary: 'Create new workflow' })
  create(@Request() req, @Body() dto: CreateWorkflowDto) {
    return this.workflowService.create(req.user.organizationId, dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all workflows' })
  findAll(@Request() req) {
    return this.workflowService.findAll(req.user.organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get workflow by ID' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.workflowService.findOne(id, req.user.organizationId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update workflow' })
  update(@Param('id') id: string, @Request() req, @Body() dto: UpdateWorkflowDto) {
    return this.workflowService.update(id, req.user.organizationId, dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete workflow' })
  remove(@Param('id') id: string, @Request() req) {
    return this.workflowService.remove(id, req.user.organizationId);
  }

  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute workflow' })
  execute(@Param('id') id: string, @Request() req, @Body() dto: ExecuteWorkflowDto) {
    return this.workflowService.execute(id, req.user.organizationId, dto);
  }
}