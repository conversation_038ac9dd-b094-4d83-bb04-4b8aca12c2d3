import { agent<PERSON>anager } from './agent-manager';
import { eventBus } from './event-bus';

export interface WorkflowNode {
  id: string;
  type: 'agent' | 'condition' | 'trigger' | 'action' | 'merge' | 'split';
  name: string;
  position: { x: number; y: number };
  data: {
    agentId?: string;
    condition?: string;
    action?: string;
    parameters?: Record<string, any>;
    config?: Record<string, any>;
  };
  inputs: string[];
  outputs: string[];
}

export interface WorkflowConnection {
  id: string;
  sourceNodeId: string;
  sourceOutput: string;
  targetNodeId: string;
  targetInput: string;
  condition?: string;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  startedAt: number;
  completedAt?: number;
  currentNodeId?: string;
  executionData: Map<string, any>;
  error?: string;
  userId: string;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  nodes: WorkflowNode[];
  connections: WorkflowConnection[];
  triggers: string[];
  status: 'active' | 'inactive' | 'draft';
  createdAt: number;
  updatedAt: number;
  version: number;
  metadata: Record<string, any>;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  workflow: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>;
  tags: string[];
}

class WorkflowManager {
  private workflows = new Map<string, Workflow>();
  private executions = new Map<string, WorkflowExecution>();
  private templates = new Map<string, WorkflowTemplate>();
  private nodeTypes = new Map<string, any>();

  constructor() {
    this.initializeNodeTypes();
    this.initializeTemplates();
  }

  private initializeNodeTypes(): void {
    const nodeTypes = {
      agent: {
        name: 'AI Agent',
        description: 'Execute an AI agent',
        inputs: ['input'],
        outputs: ['output', 'error'],
        config: {
          agentId: { type: 'select', required: true },
          timeout: { type: 'number', default: 30000 }
        }
      },
      condition: {
        name: 'Condition',
        description: 'Branch based on condition',
        inputs: ['input'],
        outputs: ['true', 'false'],
        config: {
          condition: { type: 'string', required: true },
          operator: { type: 'select', options: ['equals', 'contains', 'greater', 'less'] }
        }
      },
      trigger: {
        name: 'Trigger',
        description: 'Start workflow execution',
        inputs: [],
        outputs: ['output'],
        config: {
          type: { type: 'select', options: ['webhook', 'schedule', 'manual'] },
          schedule: { type: 'string' },
          webhook: { type: 'string' }
        }
      },
      action: {
        name: 'Action',
        description: 'Perform an action',
        inputs: ['input'],
        outputs: ['output', 'error'],
        config: {
          action: { type: 'select', options: ['http_request', 'email', 'database', 'file'] },
          parameters: { type: 'object' }
        }
      },
      merge: {
        name: 'Merge',
        description: 'Merge multiple inputs',
        inputs: ['input1', 'input2', 'input3'],
        outputs: ['output'],
        config: {
          strategy: { type: 'select', options: ['wait_all', 'first_complete', 'majority'] }
        }
      },
      split: {
        name: 'Split',
        description: 'Split input to multiple outputs',
        inputs: ['input'],
        outputs: ['output1', 'output2', 'output3'],
        config: {
          strategy: { type: 'select', options: ['duplicate', 'round_robin', 'conditional'] }
        }
      }
    };

    Object.entries(nodeTypes).forEach(([type, config]) => {
      this.nodeTypes.set(type, config);
    });
  }

  private initializeTemplates(): void {
    const templates: WorkflowTemplate[] = [
      {
        id: 'customer-support',
        name: 'Customer Support Workflow',
        description: 'Automated customer support with escalation',
        category: 'Support',
        tags: ['customer', 'support', 'automation'],
        workflow: {
          name: 'Customer Support',
          description: 'Handle customer inquiries automatically',
          nodes: [
            {
              id: 'trigger-1',
              type: 'trigger',
              name: 'New Ticket',
              position: { x: 100, y: 100 },
              data: { config: { type: 'webhook' } },
              inputs: [],
              outputs: ['output']
            },
            {
              id: 'agent-1',
              type: 'agent',
              name: 'Support Agent',
              position: { x: 300, y: 100 },
              data: { agentId: 'support-agent' },
              inputs: ['input'],
              outputs: ['output', 'error']
            },
            {
              id: 'condition-1',
              type: 'condition',
              name: 'Needs Escalation?',
              position: { x: 500, y: 100 },
              data: { condition: 'confidence < 0.8' },
              inputs: ['input'],
              outputs: ['true', 'false']
            }
          ],
          connections: [
            {
              id: 'conn-1',
              sourceNodeId: 'trigger-1',
              sourceOutput: 'output',
              targetNodeId: 'agent-1',
              targetInput: 'input'
            },
            {
              id: 'conn-2',
              sourceNodeId: 'agent-1',
              sourceOutput: 'output',
              targetNodeId: 'condition-1',
              targetInput: 'input'
            }
          ],
          triggers: ['trigger-1'],
          status: 'draft',
          version: 1,
          metadata: {}
        }
      },
      {
        id: 'data-processing',
        name: 'Data Processing Pipeline',
        description: 'Process and analyze incoming data',
        category: 'Data',
        tags: ['data', 'processing', 'analysis'],
        workflow: {
          name: 'Data Pipeline',
          description: 'Automated data processing workflow',
          nodes: [
            {
              id: 'trigger-1',
              type: 'trigger',
              name: 'Data Received',
              position: { x: 100, y: 100 },
              data: { config: { type: 'webhook' } },
              inputs: [],
              outputs: ['output']
            },
            {
              id: 'agent-1',
              type: 'agent',
              name: 'Data Analyzer',
              position: { x: 300, y: 100 },
              data: { agentId: 'data-agent' },
              inputs: ['input'],
              outputs: ['output', 'error']
            },
            {
              id: 'split-1',
              type: 'split',
              name: 'Split Results',
              position: { x: 500, y: 100 },
              data: { config: { strategy: 'duplicate' } },
              inputs: ['input'],
              outputs: ['output1', 'output2', 'output3']
            }
          ],
          connections: [
            {
              id: 'conn-1',
              sourceNodeId: 'trigger-1',
              sourceOutput: 'output',
              targetNodeId: 'agent-1',
              targetInput: 'input'
            },
            {
              id: 'conn-2',
              sourceNodeId: 'agent-1',
              sourceOutput: 'output',
              targetNodeId: 'split-1',
              targetInput: 'input'
            }
          ],
          triggers: ['trigger-1'],
          status: 'draft',
          version: 1,
          metadata: {}
        }
      }
    ];

    templates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  async createWorkflow(workflowData: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt' | 'version'>): Promise<Workflow> {
    const workflow: Workflow = {
      ...workflowData,
      id: this.generateId('workflow'),
      createdAt: Date.now(),
      updatedAt: Date.now(),
      version: 1
    };

    this.workflows.set(workflow.id, workflow);

    eventBus.publish('workflow-events', 'workflow_started', {
      workflowId: workflow.id,
      name: workflow.name
    });

    return workflow;
  }

  async updateWorkflow(id: string, updates: Partial<Workflow>): Promise<Workflow | null> {
    const workflow = this.workflows.get(id);
    if (!workflow) return null;

    Object.assign(workflow, updates, { 
      updatedAt: Date.now(),
      version: workflow.version + 1
    });

    this.workflows.set(id, workflow);

    eventBus.publish('workflow-events', 'state_update', {
      workflowId: id,
      updates
    });

    return workflow;
  }

  async deleteWorkflow(id: string): Promise<boolean> {
    const deleted = this.workflows.delete(id);
    
    if (deleted) {
      // Cancel any running executions
      for (const [execId, execution] of this.executions) {
        if (execution.workflowId === id && execution.status === 'running') {
          await this.cancelExecution(execId);
        }
      }
    }

    return deleted;
  }

  getWorkflow(id: string): Workflow | undefined {
    return this.workflows.get(id);
  }

  getAllWorkflows(): Workflow[] {
    return Array.from(this.workflows.values());
  }

  async executeWorkflow(workflowId: string, userId: string, initialData?: any): Promise<WorkflowExecution> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    if (workflow.status !== 'active') {
      throw new Error(`Workflow ${workflowId} is not active`);
    }

    const execution: WorkflowExecution = {
      id: this.generateId('execution'),
      workflowId,
      status: 'running',
      startedAt: Date.now(),
      executionData: new Map(),
      userId
    };

    this.executions.set(execution.id, execution);

    eventBus.publish('workflow-events', 'workflow_started', {
      executionId: execution.id,
      workflowId,
      userId
    });

    try {
      // Find trigger nodes
      const triggerNodes = workflow.nodes.filter(node => 
        workflow.triggers.includes(node.id)
      );

      if (triggerNodes.length === 0) {
        throw new Error('No trigger nodes found in workflow');
      }

      // Start execution from trigger nodes
      for (const triggerNode of triggerNodes) {
        await this.executeNode(execution, workflow, triggerNode.id, initialData);
      }

      execution.status = 'completed';
      execution.completedAt = Date.now();

      eventBus.publish('workflow-events', 'workflow_completed', {
        executionId: execution.id,
        workflowId,
        duration: execution.completedAt - execution.startedAt
      });

    } catch (error) {
      execution.status = 'failed';
      execution.error = error.message;
      execution.completedAt = Date.now();

      eventBus.publish('workflow-events', 'workflow_failed', {
        executionId: execution.id,
        workflowId,
        error: error.message
      });
    }

    return execution;
  }

  private async executeNode(
    execution: WorkflowExecution, 
    workflow: Workflow, 
    nodeId: string, 
    inputData?: any
  ): Promise<any> {
    const node = workflow.nodes.find(n => n.id === nodeId);
    if (!node) {
      throw new Error(`Node ${nodeId} not found`);
    }

    execution.currentNodeId = nodeId;

    eventBus.publish('workflow-events', 'state_update', {
      executionId: execution.id,
      currentNode: nodeId,
      status: 'processing'
    });

    let outputData: any;

    try {
      switch (node.type) {
        case 'agent':
          outputData = await this.executeAgentNode(node, inputData);
          break;
        case 'condition':
          outputData = await this.executeConditionNode(node, inputData);
          break;
        case 'action':
          outputData = await this.executeActionNode(node, inputData);
          break;
        case 'merge':
          outputData = await this.executeMergeNode(node, inputData);
          break;
        case 'split':
          outputData = await this.executeSplitNode(node, inputData);
          break;
        case 'trigger':
          outputData = inputData || {};
          break;
        default:
          throw new Error(`Unknown node type: ${node.type}`);
      }

      execution.executionData.set(nodeId, outputData);

      // Execute connected nodes
      const connections = workflow.connections.filter(conn => 
        conn.sourceNodeId === nodeId
      );

      for (const connection of connections) {
        const shouldExecute = this.shouldExecuteConnection(connection, outputData);
        if (shouldExecute) {
          await this.executeNode(execution, workflow, connection.targetNodeId, outputData);
        }
      }

      return outputData;

    } catch (error) {
      eventBus.publish('workflow-events', 'error_occurred', {
        executionId: execution.id,
        nodeId,
        error: error.message
      });
      throw error;
    }
  }

  private async executeAgentNode(node: WorkflowNode, inputData: any): Promise<any> {
    const agentId = node.data.agentId;
    if (!agentId) {
      throw new Error('Agent ID not specified');
    }

    const agent = agentManager.getAgent(agentId);
    if (!agent) {
      throw new Error(`Agent ${agentId} not found`);
    }

    const session = await agentManager.startSession(agentId, 'workflow-system');
    
    try {
      const message = typeof inputData === 'string' ? inputData : JSON.stringify(inputData);
      const response = await agentManager.processMessage(session.id, message);
      
      await agentManager.endSession(session.id);
      
      return {
        response,
        sessionId: session.id,
        agentId
      };
    } catch (error) {
      await agentManager.endSession(session.id);
      throw error;
    }
  }

  private async executeConditionNode(node: WorkflowNode, inputData: any): Promise<any> {
    const condition = node.data.condition;
    if (!condition) {
      throw new Error('Condition not specified');
    }

    // Simple condition evaluation
    const result = this.evaluateCondition(condition, inputData);
    
    return {
      condition,
      result,
      input: inputData
    };
  }

  private async executeActionNode(node: WorkflowNode, inputData: any): Promise<any> {
    const action = node.data.action;
    const parameters = node.data.parameters || {};

    switch (action) {
      case 'http_request':
        return await this.executeHttpRequest(parameters, inputData);
      case 'email':
        return await this.sendEmail(parameters, inputData);
      case 'database':
        return await this.executeDatabaseAction(parameters, inputData);
      case 'file':
        return await this.executeFileAction(parameters, inputData);
      default:
        throw new Error(`Unknown action: ${action}`);
    }
  }

  private async executeMergeNode(node: WorkflowNode, inputData: any): Promise<any> {
    const strategy = node.data.config?.strategy || 'wait_all';
    
    // For now, just return the input data
    // In a full implementation, this would wait for multiple inputs
    return {
      strategy,
      merged: inputData
    };
  }

  private async executeSplitNode(node: WorkflowNode, inputData: any): Promise<any> {
    const strategy = node.data.config?.strategy || 'duplicate';
    
    switch (strategy) {
      case 'duplicate':
        return {
          output1: inputData,
          output2: inputData,
          output3: inputData
        };
      case 'round_robin':
        // Implement round-robin logic
        return { output1: inputData };
      case 'conditional':
        // Implement conditional splitting
        return { output1: inputData };
      default:
        return { output1: inputData };
    }
  }

  private shouldExecuteConnection(connection: WorkflowConnection, outputData: any): boolean {
    if (!connection.condition) return true;
    
    return this.evaluateCondition(connection.condition, outputData);
  }

  private evaluateCondition(condition: string, data: any): boolean {
    try {
      // Simple condition evaluation
      // In production, use a proper expression evaluator
      const func = new Function('data', `return ${condition}`);
      return func(data);
    } catch (error) {
      return false;
    }
  }

  private async executeHttpRequest(parameters: any, inputData: any): Promise<any> {
    // Implement HTTP request
    return { success: true, data: 'HTTP request executed' };
  }

  private async sendEmail(parameters: any, inputData: any): Promise<any> {
    // Implement email sending
    return { success: true, messageId: 'email-sent' };
  }

  private async executeDatabaseAction(parameters: any, inputData: any): Promise<any> {
    // Implement database action
    return { success: true, result: 'Database action executed' };
  }

  private async executeFileAction(parameters: any, inputData: any): Promise<any> {
    // Implement file action
    return { success: true, result: 'File action executed' };
  }

  async cancelExecution(executionId: string): Promise<void> {
    const execution = this.executions.get(executionId);
    if (execution && execution.status === 'running') {
      execution.status = 'paused';
      execution.completedAt = Date.now();
    }
  }

  getExecution(executionId: string): WorkflowExecution | undefined {
    return this.executions.get(executionId);
  }

  getWorkflowExecutions(workflowId: string): WorkflowExecution[] {
    return Array.from(this.executions.values())
      .filter(exec => exec.workflowId === workflowId);
  }

  getTemplates(): WorkflowTemplate[] {
    return Array.from(this.templates.values());
  }

  getTemplate(id: string): WorkflowTemplate | undefined {
    return this.templates.get(id);
  }

  async createWorkflowFromTemplate(templateId: string, name?: string): Promise<Workflow> {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template ${templateId} not found`);
    }

    const workflow = await this.createWorkflow({
      ...template.workflow,
      name: name || template.workflow.name,
      status: 'draft'
    });

    return workflow;
  }

  getNodeTypes(): Map<string, any> {
    return this.nodeTypes;
  }

  private generateId(prefix: string): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const workflowManager = new WorkflowManager();
export default WorkflowManager;