"use client";

import React, { useState } from "react";
import { 
  Pop<PERSON>, 
  <PERSON>overContent, 
  PopoverTrigger 
} from "@/components/ui/popover";
import { 
  Command, 
  CommandEmpty, 
  CommandGroup, 
  CommandInput, 
  CommandItem, 
  CommandList, 
  CommandSeparator 
} from "@/components/ui/command";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Check, ChevronsUpDown, PlusCircle, Settings } from "lucide-react";
import { cn } from "@/lib/utils";

interface Organization {
  id: string;
  name: string;
  image?: string;
  role: "owner" | "admin" | "member";
}

interface OrganizationSwitcherProps {
  collapsed?: boolean;
}

export function OrganizationSwitcher({ collapsed = false }: OrganizationSwitcherProps) {
  const [open, setOpen] = useState(false);
  const [selectedOrganization, setSelectedOrganization] = useState<Organization>({
    id: "org_1",
    name: "Acme Inc",
    image: "https://api.dicebear.com/7.x/avataaars/svg?seed=org1",
    role: "owner"
  });

  // Sample organizations - in a real app, these would come from an API
  const organizations: Organization[] = [
    {
      id: "org_1",
      name: "Acme Inc",
      image: "https://api.dicebear.com/7.x/avataaars/svg?seed=org1",
      role: "owner"
    },
    {
      id: "org_2",
      name: "Globex Corporation",
      image: "https://api.dicebear.com/7.x/avataaars/svg?seed=org2",
      role: "admin"
    },
    {
      id: "org_3",
      name: "Initech",
      image: "https://api.dicebear.com/7.x/avataaars/svg?seed=org3",
      role: "member"
    }
  ];

  const handleOrganizationSelect = (organization: Organization) => {
    setSelectedOrganization(organization);
    setOpen(false);
    // In a real app, you would update the context/state and possibly redirect
  };

  if (collapsed) {
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="ghost" size="icon" className="w-full flex justify-center p-0">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={selectedOrganization.image}
                alt={selectedOrganization.name}
              />
              <AvatarFallback>
                {selectedOrganization.name.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[200px] p-0" align="start" side="right">
          <OrganizationSelector 
            organizations={organizations} 
            selectedOrganization={selectedOrganization}
            onSelect={handleOrganizationSelect}
          />
        </PopoverContent>
      </Popover>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          role="combobox"
          aria-expanded={open}
          aria-label="Select organization"
          className="w-full justify-between"
        >
          <div className="flex items-center gap-2 truncate">
            <Avatar className="h-6 w-6">
              <AvatarImage
                src={selectedOrganization.image}
                alt={selectedOrganization.name}
              />
              <AvatarFallback>
                {selectedOrganization.name.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <span className="truncate">{selectedOrganization.name}</span>
          </div>
          <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <OrganizationSelector 
          organizations={organizations} 
          selectedOrganization={selectedOrganization}
          onSelect={handleOrganizationSelect}
        />
      </PopoverContent>
    </Popover>
  );
}

interface OrganizationSelectorProps {
  organizations: Organization[];
  selectedOrganization: Organization;
  onSelect: (organization: Organization) => void;
}

function OrganizationSelector({ 
  organizations, 
  selectedOrganization, 
  onSelect 
}: OrganizationSelectorProps) {
  return (
    <Command>
      <CommandInput placeholder="Search organization..." />
      <CommandList>
        <CommandEmpty>No organization found.</CommandEmpty>
        <CommandGroup heading="Organizations">
          {organizations.map((org) => (
            <CommandItem
              key={org.id}
              onSelect={() => onSelect(org)}
              className="text-sm"
            >
              <div className="flex items-center gap-2 w-full">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={org.image} alt={org.name} />
                  <AvatarFallback>
                    {org.name.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <span className="flex-1 truncate">{org.name}</span>
                {org.role !== "owner" && (
                  <span className="text-xs text-muted-foreground ml-auto">
                    {org.role}
                  </span>
                )}
                {selectedOrganization.id === org.id && (
                  <Check className="ml-auto h-4 w-4" />
                )}
              </div>
            </CommandItem>
          ))}
        </CommandGroup>
        <CommandSeparator />
        <CommandGroup>
          <CommandItem
            onSelect={() => {
              // In a real app, this would open a modal or navigate to create org page
              console.log("Create new organization");
            }}
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Organization
          </CommandItem>
          <CommandItem
            onSelect={() => {
              // In a real app, this would navigate to org settings
              console.log("Organization settings");
            }}
          >
            <Settings className="mr-2 h-4 w-4" />
            Organization Settings
          </CommandItem>
        </CommandGroup>
      </CommandList>
    </Command>
  );
}