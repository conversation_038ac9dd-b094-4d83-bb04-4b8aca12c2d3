import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { prisma } from '@/lib/prisma'
import { RoleLevel } from '@prisma/client'
import crypto from 'crypto'

export interface JWTPayload {
  userId: string
  organizationId: string
  email: string
  roles: Array<{
    id: string
    name: string
    level: RoleLevel
    permissions: string[]
  }>
  sessionId: string
}

export class AuthService {
  private static readonly JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key'
  private static readonly JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key'
  private static readonly JWT_EXPIRES_IN = '15m'
  private static readonly REFRESH_TOKEN_EXPIRES_IN = '7d'

  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 12)
  }

  static async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword)
  }

  static generateTokens(payload: Omit<JWTPayload, 'sessionId'> & { sessionId: string }) {
    const accessToken = jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: this.JWT_EXPIRES_IN,
      issuer: 'synapseai',
      audience: payload.organizationId
    })

    const refreshToken = jwt.sign(
      { userId: payload.userId, sessionId: payload.sessionId },
      this.JWT_REFRESH_SECRET,
      {
        expiresIn: this.REFRESH_TOKEN_EXPIRES_IN,
        issuer: 'synapseai'
      }
    )

    return { accessToken, refreshToken }
  }

  static verifyAccessToken(token: string): JWTPayload | null {
    try {
      return jwt.verify(token, this.JWT_SECRET) as JWTPayload
    } catch {
      return null
    }
  }

  static verifyRefreshToken(token: string): { userId: string; sessionId: string } | null {
    try {
      return jwt.verify(token, this.JWT_REFRESH_SECRET) as { userId: string; sessionId: string }
    } catch {
      return null
    }
  }

  static generateSecureToken(): string {
    return crypto.randomBytes(32).toString('hex')
  }

  static async createSession(userId: string, deviceInfo: any, ipAddress: string, userAgent?: string) {
    const token = this.generateSecureToken()
    const refreshToken = this.generateSecureToken()
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

    const session = await prisma.session.create({
      data: {
        userId,
        token,
        refreshToken,
        deviceInfo,
        ipAddress,
        userAgent,
        expiresAt
      }
    })

    return session
  }

  static async getUserWithRoles(userId: string) {
    return prisma.user.findUnique({
      where: { id: userId },
      include: {
        organization: true,
        userRoles: {
          where: { isActive: true },
          include: {
            role: {
              where: { isActive: true }
            }
          }
        }
      }
    })
  }

  static async validateSession(token: string) {
    const session = await prisma.session.findUnique({
      where: { token, isActive: true },
      include: {
        user: {
          include: {
            organization: true,
            userRoles: {
              where: { isActive: true },
              include: {
                role: {
                  where: { isActive: true }
                }
              }
            }
          }
        }
      }
    })

    if (!session || session.expiresAt < new Date()) {
      return null
    }

    return session
  }

  static async refreshSession(refreshToken: string) {
    const session = await prisma.session.findUnique({
      where: { refreshToken, isActive: true },
      include: {
        user: {
          include: {
            organization: true,
            userRoles: {
              where: { isActive: true },
              include: {
                role: {
                  where: { isActive: true }
                }
              }
            }
          }
        }
      }
    })

    if (!session || session.expiresAt < new Date()) {
      return null
    }

    // Generate new tokens
    const newToken = this.generateSecureToken()
    const newRefreshToken = this.generateSecureToken()
    const newExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)

    await prisma.session.update({
      where: { id: session.id },
      data: {
        token: newToken,
        refreshToken: newRefreshToken,
        expiresAt: newExpiresAt
      }
    })

    return {
      ...session,
      token: newToken,
      refreshToken: newRefreshToken,
      expiresAt: newExpiresAt
    }
  }

  static async revokeSession(sessionId: string) {
    await prisma.session.update({
      where: { id: sessionId },
      data: { isActive: false }
    })
  }

  static async revokeAllUserSessions(userId: string) {
    await prisma.session.updateMany({
      where: { userId },
      data: { isActive: false }
    })
  }

  static async cleanupExpiredSessions() {
    await prisma.session.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    })
  }
}