"use client";

import React from "react";
import { ToolProvider } from "@/components/providers/tool-provider";
import ToolBuilder from "@/components/tools/ToolBuilder";

export default function ToolBuilderPage({
  params,
}: {
  params: { id?: string };
}) {
  return (
    <div className="flex-1 overflow-auto p-6">
      <ToolProvider>
        <ToolBuilder initialToolId={params.id} />
      </ToolProvider>
    </div>
  );
}