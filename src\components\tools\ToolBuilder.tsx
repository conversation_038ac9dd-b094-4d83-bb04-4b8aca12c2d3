"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useTool } from "@/components/providers/tool-provider";
import { useEventBus } from "@/components/providers/event-bus-provider";
import { Tool<PERSON>ategory, ToolExecutionType } from "@/lib/types/tool";
import Editor from "@monaco-editor/react";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON>ertDescription, AlertTitle } from "@/components/ui/alert";

import {
  AlertCircle,
  ArrowRight,
  Check,
  ChevronRight,
  Code,
  Database,
  Globe,
  MessageSquare,
  Plus,
  Rocket,
  Save,
  Settings,
  Wrench,
  Play,
  History,
  RotateCcw,
  FileJson,
  RefreshCw,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Zap,
  Shield,
  Activity,
  TrendingUp,
  Clock,
  BarChart3,
  Terminal,
  ExternalLink,
  Copy
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface ToolBuilderProps {
  initialToolId?: string;
}

export default function ToolBuilder({ initialToolId }: ToolBuilderProps) {
  const router = useRouter();
  const {
    tools,
    loading,
    error,
    selectedTool,
    selectedVersion,
    executionResults,
    createTool,
    updateTool,
    deleteTool,
    selectTool,
    addToolVersion,
    activateToolVersion,
    selectVersion,
    executeTool,
    getExecutionHistory,
    getToolAnalytics
  } = useTool();

  const { subscribe, unsubscribe } = useEventBus();

  const [currentStep, setCurrentStep] = useState(0);
  const [isCreating, setIsCreating] = useState(!initialToolId);
  const [isEditing, setIsEditing] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionInput, setExecutionInput] = useState("{}");
  const [executionResult, setExecutionResult] = useState<any>(null);
  const [executionError, setExecutionError] = useState<string | null>(null);
  const [executionHistory, setExecutionHistory] = useState<any[]>([]);
  const [analytics, setAnalytics] = useState<any>(null);
  const [realTimeEvents, setRealTimeEvents] = useState<any[]>([]);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [saveProgress, setSaveProgress] = useState(0);
  const [deploymentStatus, setDeploymentStatus] = useState<'idle' | 'deploying' | 'success' | 'error'>('idle');

  // Form state
  const [toolData, setToolData] = useState({
    name: "",
    slug: "",
    description: "",
    category: "function" as ToolCategory,
    executionType: "sync" as ToolExecutionType,
    isActive: true,
    organizationId: "org_default",
    currentVersion: "1.0.0",
    metadata: {},
    permissions: {}
  });

  // Version form state
  const [versionData, setVersionData] = useState({
    version: "1.0.0",
    inputSchema: JSON.stringify({
      type: "object",
      properties: {
        input: {
          type: "string",
          description: "Input parameter"
        }
      },
      required: ["input"]
    }, null, 2),
    outputSchema: JSON.stringify({
      type: "object",
      properties: {
        result: {
          type: "string",
          description: "Tool execution result"
        }
      },
      required: ["result"]
    }, null, 2),
    handlerUrl: "",
    internalHandlerRef: "",
    config: {},
    isActive: true,
    createdBy: "user_default"
  });

  const steps = [
    { id: "basic", title: "Basic Info", description: "Tool name and description", icon: <FileJson className="h-4 w-4" /> },
    { id: "schema", title: "Schema", description: "Input/output definitions", icon: <Code className="h-4 w-4" /> },
    { id: "handler", title: "Handler", description: "Execution configuration", icon: <Settings className="h-4 w-4" /> },
    { id: "security", title: "Security", description: "Permissions and limits", icon: <Shield className="h-4 w-4" /> },
    { id: "test", title: "Test & Deploy", description: "Test and deploy tool", icon: <Rocket className="h-4 w-4" /> },
  ];

  const toolCategories = [
    {
      id: "function",
      name: "Function",
      description: "Execute custom code or functions",
      icon: <Code className="h-6 w-6" />,
      examples: ["Calculator", "Data processor", "Validator"]
    },
    {
      id: "api",
      name: "API",
      description: "Make API calls to external services",
      icon: <Globe className="h-6 w-6" />,
      examples: ["REST API", "GraphQL", "Webhook"]
    },
    {
      id: "database",
      name: "Database",
      description: "Query databases for information",
      icon: <Database className="h-6 w-6" />,
      examples: ["SQL query", "NoSQL lookup", "Data aggregation"]
    },
    {
      id: "retrieval",
      name: "Retrieval",
      description: "Retrieve information from documents",
      icon: <MessageSquare className="h-6 w-6" />,
      examples: ["Document search", "Knowledge base", "Vector search"]
    },
    {
      id: "creative",
      name: "Creative",
      description: "Generate creative content",
      icon: <Zap className="h-6 w-6" />,
      examples: ["Image generation", "Text creation", "Audio synthesis"]
    },
    {
      id: "utility",
      name: "Utility",
      description: "General utility functions",
      icon: <Wrench className="h-6 w-6" />,
      examples: ["File converter", "Text formatter", "Date calculator"]
    }
  ];

  const executionTypes = [
    {
      id: "sync",
      name: "Synchronous",
      description: "Execute and return results immediately",
      icon: <Zap className="h-4 w-4" />,
      maxDuration: "30 seconds"
    },
    {
      id: "async",
      name: "Asynchronous",
      description: "Start execution and return job ID for later retrieval",
      icon: <Clock className="h-4 w-4" />,
      maxDuration: "10 minutes"
    }
  ];

  const builtinHandlers = [
    { id: "calculator", name: "Calculator", description: "Mathematical calculations" },
    { id: "web-search", name: "Web Search", description: "Search the internet" },
    { id: "api-caller", name: "API Caller", description: "Make HTTP requests" },
    { id: "database-query", name: "Database Query", description: "Query databases" }
  ];

  // Load tool data if editing
  useEffect(() => {
    if (initialToolId) {
      selectTool(initialToolId);
      setIsCreating(false);
      setIsTesting(true);
    }
  }, [initialToolId]);

  // Update form when selected tool changes
  useEffect(() => {
    if (selectedTool) {
      setToolData({
        name: selectedTool.name,
        slug: selectedTool.slug,
        description: selectedTool.description,
        category: selectedTool.category,
        executionType: selectedTool.executionType,
        isActive: selectedTool.isActive,
        organizationId: selectedTool.organizationId,
        currentVersion: selectedTool.currentVersion,
        metadata: selectedTool.metadata || {},
        permissions: selectedTool.permissions || {}
      });
      
      loadToolData(selectedTool.id);
    }
  }, [selectedTool]);

  // Update version form when selected version changes
  useEffect(() => {
    if (selectedVersion) {
      setVersionData({
        version: selectedVersion.version,
        inputSchema: JSON.stringify(selectedVersion.inputSchema, null, 2),
        outputSchema: JSON.stringify(selectedVersion.outputSchema, null, 2),
        handlerUrl: selectedVersion.handlerUrl || "",
        internalHandlerRef: selectedVersion.internalHandlerRef || "",
        config: selectedVersion.config || {},
        isActive: selectedVersion.isActive,
        createdBy: selectedVersion.createdBy
      });
    }
  }, [selectedVersion]);

  // Subscribe to real-time events
  useEffect(() => {
    const eventTypes = [
      'tool:execution:started',
      'tool:execution:completed',
      'tool:execution:error'
    ];

    eventTypes.forEach(eventType => {
      subscribe(eventType, handleRealTimeEvent);
    });

    return () => {
      eventTypes.forEach(eventType => {
        unsubscribe(eventType, handleRealTimeEvent);
      });
    };
  }, []);

  const handleRealTimeEvent = (event: any) => {
    setRealTimeEvents(prev => [event, ...prev.slice(0, 49)]);
    
    if (event.type === 'tool:execution:completed') {
      setIsExecuting(false);
      setExecutionResult(event.data);
    } else if (event.type === 'tool:execution:error') {
      setIsExecuting(false);
      setExecutionError(event.data.error);
    }
  };

  const loadToolData = async (toolId: string) => {
    try {
      const [history, analyticsData] = await Promise.all([
        getExecutionHistory(toolId, 10),
        getToolAnalytics(toolId, {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          end: new Date()
        })
      ]);
      
      setExecutionHistory(history.executions);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Failed to load tool data:', error);
    }
  };

  const validateStep = (step: number): boolean => {
    const errors: Record<string, string> = {};

    switch (step) {
      case 0: // Basic info
        if (!toolData.name.trim()) errors.name = "Tool name is required";
        if (!toolData.description.trim()) errors.description = "Description is required";
        break;
      case 1: // Schema
        try {
          JSON.parse(versionData.inputSchema);
        } catch {
          errors.inputSchema = "Invalid JSON schema";
        }
        try {
          JSON.parse(versionData.outputSchema);
        } catch {
          errors.outputSchema = "Invalid JSON schema";
        }
        break;
      case 2: // Handler
        if (!versionData.handlerUrl && !versionData.internalHandlerRef) {
          errors.handler = "Either HTTP handler URL or internal handler reference is required";
        }
        break;
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      if (currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateToolFormData = (key: string, value: any) => {
    setToolData({ ...toolData, [key]: value });
    if (validationErrors[key]) {
      setValidationErrors({ ...validationErrors, [key]: "" });
    }
  };

  const updateVersionFormData = (key: string, value: any) => {
    setVersionData({ ...versionData, [key]: value });
    if (validationErrors[key]) {
      setValidationErrors({ ...validationErrors, [key]: "" });
    }
  };

  const handleCreateTool = async () => {
    if (!validateStep(currentStep)) return;

    setSaveProgress(0);
    setDeploymentStatus('deploying');

    try {
      const progressInterval = setInterval(() => {
        setSaveProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const slug = toolData.slug || toolData.name.toLowerCase().replace(/\s+/g, "-");
      
      const newTool = await createTool({
        ...toolData,
        slug,
      }, "user_default");

      await addToolVersion(newTool.id, {
        ...versionData,
        inputSchema: JSON.parse(versionData.inputSchema),
        outputSchema: JSON.parse(versionData.outputSchema),
      }, "user_default");

      clearInterval(progressInterval);
      setSaveProgress(100);
      setDeploymentStatus('success');

      setTimeout(() => {
        setDeploymentStatus('idle');
        setSaveProgress(0);
        setIsCreating(false);
        selectTool(newTool.id);
        setIsTesting(true);
      }, 2000);

    } catch (error) {
      setDeploymentStatus('error');
      console.error('Failed to create tool:', error);
    }
  };

  const handleUpdateTool = async () => {
    if (!selectedTool) return;

    try {
      await updateTool(selectedTool.id, toolData);
      setIsEditing(false);
    } catch (err) {
      console.error("Failed to update tool:", err);
    }
  };

  const handleAddVersion = async () => {
    if (!selectedTool) return;

    try {
      await addToolVersion(selectedTool.id, {
        ...versionData,
        inputSchema: JSON.parse(versionData.inputSchema),
        outputSchema: JSON.parse(versionData.outputSchema),
      });
    } catch (err) {
      console.error("Failed to add version:", err);
    }
  };

  const handleActivateVersion = async (versionId: string) => {
    if (!selectedTool) return;

    try {
      await activateToolVersion(selectedTool.id, versionId);
    } catch (err) {
      console.error("Failed to activate version:", err);
    }
  };

  const handleExecuteTool = async () => {
    if (!selectedTool || !executionInput.trim()) return;

    setIsExecuting(true);
    setExecutionError(null);
    setExecutionResult(null);

    try {
      const input = JSON.parse(executionInput);
      const result = await executeTool(selectedTool.id, input, "user_default");
      setExecutionResult(result);
    } catch (error) {
      setExecutionError(error.message);
    } finally {
      setIsExecuting(false);
    }
  };

  const incrementVersion = (version: string): string => {
    const parts = version.split(".");
    if (parts.length !== 3) return "1.0.0";
    
    const patch = parseInt(parts[2]) + 1;
    return `${parts[0]}.${parts[1]}.${patch}`;
  };

  const formatTimestamp = (timestamp: Date): string => {
    return new Date(timestamp).toLocaleString();
  };

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="w-full max-w-7xl mx-auto bg-background p-6 rounded-xl">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              {isCreating ? "Create Tool" : selectedTool?.name || "Tool Builder"}
            </h1>
            <p className="text-muted-foreground">
              {isCreating 
                ? "Build a powerful tool for your AI agents" 
                : "Configure and manage your tool"}
            </p>
          </div>
          {selectedTool && (
            <div className="flex items-center space-x-2">
              <Badge variant={selectedTool.isActive ? "default" : "secondary"}>
                {selectedTool.isActive ? "Active" : "Inactive"}
              </Badge>
              <Badge variant="outline">v{selectedTool.currentVersion}</Badge>
            </div>
          )}
        </div>
      </div>

      {/* Tool Selection or Creation */}
      {!isCreating && !selectedTool && (
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Your Tools</h2>
            <Button onClick={() => setIsCreating(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create New Tool
            </Button>
          </div>

          {loading ? (
            <div className="flex justify-center p-8">
              <RefreshCw className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : tools.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tools.map((tool) => (
                <Card
                  key={tool.id}
                  className="cursor-pointer hover:border-primary transition-colors"
                  onClick={() => selectTool(tool.id)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <Badge variant={tool.isActive ? "default" : "outline"}>
                        {tool.category}
                      </Badge>
                      <Badge
                        variant={
                          tool.executionType === "sync" ? "secondary" : "outline"
                        }
                      >
                        {tool.executionType}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg">{tool.name}</CardTitle>
                    <CardDescription className="text-sm">
                      {tool.slug}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-2">
                      {tool.description}
                    </p>
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Version: {tool.currentVersion}</span>
                      <span>
                        Updated:{" "}
                        {new Date(tool.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="border-dashed border-2 p-8">
              <div className="flex flex-col items-center justify-center text-center">
                <Wrench className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Tools Found</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first tool to extend your agents' capabilities.
                </p>
                <Button onClick={() => setIsCreating(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Tool
                </Button>
              </div>
            </Card>
          )}
        </div>
      )}

      {/* Tool Creation/Editing Wizard */}
      {(isCreating || (selectedTool && !isTesting)) && (
        <>
          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <React.Fragment key={step.id}>
                  <div className="flex flex-col items-center">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div
                            className={`w-12 h-12 rounded-full flex items-center justify-center border-2 ${
                              index === currentStep
                                ? "bg-primary text-primary-foreground border-primary"
                                : index < currentStep
                                ? "bg-primary/20 text-primary border-primary"
                                : "bg-muted text-muted-foreground border-muted"
                            }`}
                          >
                            {index < currentStep ? (
                              <Check className="h-5 w-5" />
                            ) : (
                              step.icon
                            )}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{step.description}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <span
                      className={`mt-2 text-sm text-center ${
                        index === currentStep
                          ? "font-medium text-foreground"
                          : "text-muted-foreground"
                      }`}
                    >
                      {step.title}
                    </span>
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`h-1 w-full max-w-24 ${
                        index < currentStep ? "bg-primary" : "bg-muted"
                      }`}
                    />
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>

          {/* Step Content */}
          <Card className="border shadow-sm">
            <CardContent className="pt-6">
              {/* Step 0: Basic Info */}
              {currentStep === 0 && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Basic Information</h2>
                  <div className="space-y-6">
                    <div>
                      <Label htmlFor="tool-name">Tool Name *</Label>
                      <Input
                        id="tool-name"
                        placeholder="e.g., Calculator"
                        value={toolData.name}
                        onChange={(e) => updateToolFormData("name", e.target.value)}
                        className={validationErrors.name ? "border-red-500" : ""}
                      />
                      {validationErrors.name && (
                        <p className="text-red-500 text-sm mt-1">{validationErrors.name}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="tool-slug">
                        Slug <span className="text-xs text-muted-foreground">(optional)</span>
                      </Label>
                      <Input
                        id="tool-slug"
                        placeholder="calculator"
                        value={toolData.slug}
                        onChange={(e) => updateToolFormData("slug", e.target.value)}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        URL-friendly identifier. Will be generated from name if left blank.
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="tool-description">Description *</Label>
                      <Textarea
                        id="tool-description"
                        placeholder="Describe what this tool does and how it works..."
                        className={`min-h-[100px] ${validationErrors.description ? "border-red-500" : ""}`}
                        value={toolData.description}
                        onChange={(e) => updateToolFormData("description", e.target.value)}
                      />
                      {validationErrors.description && (
                        <p className="text-red-500 text-sm mt-1">{validationErrors.description}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="tool-category">Category</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                        {toolCategories.map((category) => (
                          <Card
                            key={category.id}
                            className={`cursor-pointer border-2 transition-all ${
                              toolData.category === category.id
                                ? "border-primary bg-primary/5"
                                : "border-border hover:border-primary/50"
                            }`}
                            onClick={() => updateToolFormData("category", category.id)}
                          >
                            <CardHeader className="pb-2">
                              <div className="flex items-center justify-between">
                                <div className="p-2 rounded-md bg-primary/10">
                                  {category.icon}
                                </div>
                                {toolData.category === category.id && (
                                  <Check className="h-4 w-4 text-primary" />
                                )}
                              </div>
                              <CardTitle className="text-base">{category.name}</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <p className="text-xs text-muted-foreground mb-2">
                                {category.description}
                              </p>
                              <div className="space-y-1">
                                {category.examples.slice(0, 2).map((example, idx) => (
                                  <div key={idx} className="text-xs text-muted-foreground">
                                    • {example}
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="execution-type">Execution Type</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                        {executionTypes.map((type) => (
                          <Card
                            key={type.id}
                            className={`cursor-pointer border-2 transition-all ${
                              toolData.executionType === type.id
                                ? "border-primary bg-primary/5"
                                : "border-border hover:border-primary/50"
                            }`}
                            onClick={() => updateToolFormData("executionType", type.id)}
                          >
                            <CardHeader className="pb-2">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  {type.icon}
                                  <CardTitle className="text-base">{type.name}</CardTitle>
                                </div>
                                {toolData.executionType === type.id && (
                                  <Check className="h-4 w-4 text-primary" />
                                )}
                              </div>
                            </CardHeader>
                            <CardContent>
                              <p className="text-xs text-muted-foreground mb-1">
                                {type.description}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Max duration: {type.maxDuration}
                              </p>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="tool-active"
                        checked={toolData.isActive}
                        onCheckedChange={(checked) => updateToolFormData("isActive", checked)}
                      />
                      <Label htmlFor="tool-active">Active (tool can be executed)</Label>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 1: Schema Definition */}
              {currentStep === 1 && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Schema Definition</h2>
                  <p className="text-muted-foreground mb-6">
                    Define the input and output schemas for your tool using JSON Schema format.
                  </p>
                  <div className="space-y-6">
                    <div>
                      <Label htmlFor="input-schema" className="mb-2 block">
                        Input Schema (JSON Schema) *
                      </Label>
                      <div className={`border rounded-md ${validationErrors.inputSchema ? "border-red-500" : ""}`}>
                        <Editor
                          height="300px"
                          defaultLanguage="json"
                          value={versionData.inputSchema}
                          onChange={(value) => updateVersionFormData("inputSchema", value || "{}")}
                          options={{
                            minimap: { enabled: false },
                            scrollBeyondLastLine: false,
                            formatOnPaste: true,
                            formatOnType: true
                          }}
                        />
                      </div>
                      {validationErrors.inputSchema && (
                        <p className="text-red-500 text-sm mt-1">{validationErrors.inputSchema}</p>
                      )}
                      <p className="text-xs text-muted-foreground mt-1">
                        Define the expected input parameters for your tool.
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="output-schema" className="mb-2 block">
                        Output Schema (JSON Schema) *
                      </Label>
                      <div className={`border rounded-md ${validationErrors.outputSchema ? "border-red-500" : ""}`}>
                        <Editor
                          height="300px"
                          defaultLanguage="json"
                          value={versionData.outputSchema}
                          onChange={(value) => updateVersionFormData("outputSchema", value || "{}")}
                          options={{
                            minimap: { enabled: false },
                            scrollBeyondLastLine: false,
                            formatOnPaste: true,
                            formatOnType: true
                          }}
                        />
                      </div>
                      {validationErrors.outputSchema && (
                        <p className="text-red-500 text-sm mt-1">{validationErrors.outputSchema}</p>
                      )}
                      <p className="text-xs text-muted-foreground mt-1">
                        Define the expected output format for your tool.
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="tool-version">Version</Label>
                      <Input
                        id="tool-version"
                        placeholder="1.0.0"
                        value={versionData.version}
                        onChange={(e) => updateVersionFormData("version", e.target.value)}
                        className="max-w-[200px]"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Semantic versioning (e.g., 1.0.0)
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Handler Configuration */}
              {currentStep === 2 && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Handler Configuration</h2>
                  <p className="text-muted-foreground mb-6">
                    Configure how your tool will be executed.
                  </p>

                  {validationErrors.handler && (
                    <Alert variant="destructive" className="mb-4">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{validationErrors.handler}</AlertDescription>
                    </Alert>
                  )}

                  <Tabs defaultValue="internal" className="w-full">
                    <TabsList className="mb-4">
                      <TabsTrigger value="internal">Built-in Handler</TabsTrigger>
                      <TabsTrigger value="http">HTTP Handler</TabsTrigger>
                    </TabsList>

                    <TabsContent value="internal" className="space-y-4">
                      <div>
                        <Label htmlFor="internal-handler">Built-in Handler</Label>
                        <Select
                          value={versionData.internalHandlerRef}
                          onValueChange={(value) => {
                            updateVersionFormData("internalHandlerRef", value);
                            updateVersionFormData("handlerUrl", ""); // Clear HTTP handler
                          }}
                        >
                          <SelectTrigger id="internal-handler">
                            <SelectValue placeholder="Select a built-in handler" />
                          </SelectTrigger>
                          <SelectContent>
                            {builtinHandlers.map((handler) => (
                              <SelectItem key={handler.id} value={handler.id}>
                                <div className="flex items-center">
                                  <Terminal className="h-4 w-4 mr-2" />
                                  <div>
                                    <div className="font-medium">{handler.name}</div>
                                    <div className="text-xs text-muted-foreground">
                                      {handler.description}
                                    </div>
                                  </div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-muted-foreground mt-1">
                          Use a pre-built handler for common functionality.
                        </p>
                      </div>

                      <Card className="bg-muted/50">
                        <CardHeader>
                          <CardTitle className="text-sm">Built-in Handler Benefits</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="text-xs space-y-1 text-muted-foreground">
                            <li>• Faster execution (no network calls)</li>
                            <li>• Built-in error handling and validation</li>
                            <li>• Automatic scaling and optimization</li>
                            <li>• No external dependencies</li>
                          </ul>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="http" className="space-y-4">
                      <div>
                        <Label htmlFor="handler-url">Handler URL</Label>
                        <Input
                          id="handler-url"
                          placeholder="https://api.example.com/tools/my-tool"
                          value={versionData.handlerUrl}
                          onChange={(e) => {
                            updateVersionFormData("handlerUrl", e.target.value);
                            updateVersionFormData("internalHandlerRef", ""); // Clear internal handler
                          }}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          The URL that will be called when this tool is executed.
                        </p>
                      </div>

                      <Card className="bg-muted/50">
                        <CardHeader>
                          <CardTitle className="text-sm">HTTP Handler Requirements</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="text-xs space-y-1 text-muted-foreground">
                            <li>• Must accept POST requests with JSON body</li>
                            <li>• Input will be sent as request body</li>
                            <li>• Must return JSON matching output schema</li>
                            <li>• Timeout: 30 seconds for sync, 10 minutes for async</li>
                            <li>• Must handle errors gracefully</li>
                          </ul>
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </Tabs>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="version-active"
                      checked={versionData.isActive}
                      onCheckedChange={(checked) => updateVersionFormData("isActive", checked)}
                    />
                    <Label htmlFor="version-active">Activate this version</Label>
                  </div>
                </div>
              )}

              {/* Step 3: Security & Permissions */}
              {currentStep === 3 && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Security & Permissions</h2>
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Access Control</CardTitle>
                        <CardDescription>
                          Configure who can use this tool
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <input type="checkbox" id="public-access" defaultChecked />
                            <Label htmlFor="public-access">Allow all organization members</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input type="checkbox" id="agent-access" defaultChecked />
                            <Label htmlFor="agent-access">Allow agent execution</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input type="checkbox" id="api-access" />
                            <Label htmlFor="api-access">Allow API access</Label>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Rate Limiting</CardTitle>
                        <CardDescription>
                          Control execution frequency
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <Label htmlFor="rate-limit">Executions per minute</Label>
                          <Input
                            id="rate-limit"
                            type="number"
                            placeholder="60"
                            className="max-w-[200px]"
                          />
                        </div>
                        <div>
                          <Label htmlFor="burst-limit">Burst limit</Label>
                          <Input
                            id="burst-limit"
                            type="number"
                            placeholder="10"
                            className="max-w-[200px]"
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}

              {/* Step 4: Test & Deploy */}
              {currentStep === 4 && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Test & Deploy Tool</h2>
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Tool Summary</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-xs font-medium">Name:</p>
                            <p className="text-sm">{toolData.name}</p>
                          </div>
                          <div>
                            <p className="text-xs font-medium">Category:</p>
                            <p className="text-sm">{toolData.category}</p>
                          </div>
                          <div>
                            <p className="text-xs font-medium">Execution Type:</p>
                            <p className="text-sm">{toolData.executionType}</p>
                          </div>
                          <div>
                            <p className="text-xs font-medium">Version:</p>
                            <p className="text-sm">{versionData.version}</p>
                          </div>
                          <div className="col-span-2">
                            <p className="text-xs font-medium">Handler:</p>
                            <p className="text-sm">
                              {versionData.handlerUrl || versionData.internalHandlerRef || "No handler configured"}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Test Execution</CardTitle>
                        <CardDescription>
                          Test your tool with sample input
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <Label htmlFor="test-input" className="mb-2 block">
                            Test Input (JSON)
                          </Label>
                          <div className="border rounded-md">
                            <Editor
                              height="200px"
                              defaultLanguage="json"
                              value={executionInput}
                              onChange={(value) => setExecutionInput(value || "{}")}
                              options={{
                                minimap: { enabled: false },
                                scrollBeyondLastLine: false,
                              }}
                            />
                          </div>
                        </div>

                        {executionError && (
                          <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Execution Error</AlertTitle>
                            <AlertDescription>{executionError}</AlertDescription>
                          </Alert>
                        )}

                        {executionResult && (
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">Execution Result</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-2">
                                <div className="flex justify-between items-center">
                                  <Badge variant={executionResult.status === "success" ? "default" : "destructive"}>
                                    {executionResult.status}
                                  </Badge>
                                  <span className="text-xs text-muted-foreground">
                                    {formatDuration(executionResult.executionTime)}
                                  </span>
                                </div>
                                <div className="border rounded-md">
                                  <Editor
                                    height="200px"
                                    defaultLanguage="json"
                                    value={JSON.stringify(executionResult.output || executionResult.error, null, 2)}
                                    options={{
                                      readOnly: true,
                                      minimap: { enabled: false },
                                      scrollBeyondLastLine: false,
                                    }}
                                  />
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        )}
                      </CardContent>
                      <CardFooter>
                        <Button
                          onClick={handleExecuteTool}
                          disabled={isExecuting || !selectedTool}
                          className="ml-auto"
                        >
                          {isExecuting ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Executing...
                            </>
                          ) : (
                            <>
                              <Play className="h-4 w-4 mr-2" />
                              Test Tool
                            </>
                          )}
                        </Button>
                      </CardFooter>
                    </Card>

                    {deploymentStatus === 'deploying' && (
                      <Card>
                        <CardContent className="pt-6">
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Creating tool...</span>
                              <span>{saveProgress}%</span>
                            </div>
                            <Progress value={saveProgress} className="w-full" />
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {deploymentStatus === 'success' && (
                      <Alert>
                        <CheckCircle2 className="h-4 w-4" />
                        <AlertTitle>Success!</AlertTitle>
                        <AlertDescription>
                          Your tool has been successfully created and is ready to use.
                        </AlertDescription>
                      </Alert>
                    )}

                    {deploymentStatus === 'error' && (
                      <Alert variant="destructive">
                        <XCircle className="h-4 w-4" />
                        <AlertTitle>Creation Failed</AlertTitle>
                        <AlertDescription>
                          There was an error creating your tool. Please try again.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
              )}
            </CardContent>

            <CardFooter className="flex justify-between border-t p-6">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={currentStep === 0 || deploymentStatus === 'deploying'}
              >
                Back
              </Button>

              <div className="flex space-x-2">
                {currentStep < steps.length - 1 ? (
                  <Button 
                    onClick={handleNext} 
                    className="flex items-center gap-1"
                    disabled={deploymentStatus === 'deploying'}
                  >
                    Next <ChevronRight className="h-4 w-4" />
                  </Button>
                ) : (
                  <Button 
                    onClick={handleCreateTool} 
                    className="flex items-center gap-1"
                    disabled={deploymentStatus === 'deploying'}
                  >
                    {deploymentStatus === 'deploying' ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Rocket className="h-4 w-4" />
                        Create Tool
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardFooter>
          </Card>
        </>
      )}

      {/* Tool Testing & Management Interface */}
      {selectedTool && isTesting && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold">{selectedTool.name}</h2>
              <p className="text-muted-foreground">{selectedTool.description}</p>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditing(true);
                  setIsTesting(false);
                  setCurrentStep(0);
                }}
              >
                <Settings className="h-4 w-4 mr-2" />
                Edit Tool
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  selectTool(null);
                  setIsTesting(false);
                }}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Back to List
              </Button>
            </div>
          </div>

          <Tabs defaultValue="test" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="test">Test Tool</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="versions">Versions</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
              <TabsTrigger value="events">Real-time Events</TabsTrigger>
            </TabsList>

            <TabsContent value="test" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Test Execution</CardTitle>
                    <CardDescription>
                      Execute the tool with custom input
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="test-input" className="mb-2 block">
                        Input JSON
                      </Label>
                      <div className="border rounded-md">
                        <Editor
                          height="200px"
                          defaultLanguage="json"
                          value={executionInput}
                          onChange={(value) => setExecutionInput(value || "{}")}
                          options={{
                            minimap: { enabled: false },
                            scrollBeyondLastLine: false,
                          }}
                        />
                      </div>
                    </div>

                    {executionError && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Error</AlertTitle>
                        <AlertDescription>{executionError}</AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                  <CardFooter>
                    <Button
                      onClick={handleExecuteTool}
                      disabled={isExecuting}
                      className="ml-auto"
                    >
                      {isExecuting ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Executing...
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-2" />
                          Execute Tool
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Execution Result</CardTitle>
                    <CardDescription>
                      Output from the last execution
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {executionResult ? (
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <Badge variant={executionResult.status === "success" ? "default" : "destructive"}>
                            {executionResult.status}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {formatDuration(executionResult.executionTime)}
                          </span>
                        </div>
                        <div className="border rounded-md">
                          <Editor
                            height="200px"
                            defaultLanguage="json"
                            value={JSON.stringify(executionResult.output || executionResult.error, null, 2)}
                            options={{
                              readOnly: true,
                              minimap: { enabled: false },
                              scrollBeyondLastLine: false,
                            }}
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        No execution result yet. Run a test to see the output.
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Input Schema</CardTitle>
                    <CardDescription>
                      Expected input parameters
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="border rounded-md">
                      <Editor
                        height="200px"
                        defaultLanguage="json"
                        value={selectedVersion ? JSON.stringify(selectedVersion.inputSchema, null, 2) : "{}"}
                        options={{
                          readOnly: true,
                          minimap: { enabled: false },
                          scrollBeyondLastLine: false,
                        }}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Output Schema</CardTitle>
                    <CardDescription>
                      Expected output format
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="border rounded-md">
                      <Editor
                        height="200px"
                        defaultLanguage="json"
                        value={selectedVersion ? JSON.stringify(selectedVersion.outputSchema, null, 2) : "{}"}
                        options={{
                          readOnly: true,
                          minimap: { enabled: false },
                          scrollBeyondLastLine: false,
                        }}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              {analytics && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
                      <Activity className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{formatNumber(analytics.totalExecutions)}</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{(analytics.successRate * 100).toFixed(1)}%</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Avg Execution Time</CardTitle>
                      <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{formatDuration(analytics.avgExecutionTime)}</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Usage Trend</CardTitle>
                      <BarChart3 className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">+{analytics.executionsByDay.length > 1 ? 
                        ((analytics.executionsByDay[0].count - analytics.executionsByDay[1].count) / analytics.executionsByDay[1].count * 100).toFixed(0) : 0}%</div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </TabsContent>

            <TabsContent value="versions" className="space-y-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Tool Versions</CardTitle>
                    <CardDescription>
                      Manage different versions of this tool
                    </CardDescription>
                  </div>
                  <Button onClick={() => {}}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Version
                  </Button>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Version</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Handler</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell className="font-medium">1.0.0</TableCell>
                        <TableCell>
                          <Badge>Active</Badge>
                        </TableCell>
                        <TableCell>Built-in</TableCell>
                        <TableCell>
                          {new Date().toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button variant="ghost" size="sm">
                              Edit
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Execution History</CardTitle>
                  <CardDescription>Recent tool executions</CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-4">
                      {executionHistory.map((execution, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start mb-2">
                            <Badge variant={execution.status === 'success' ? 'default' : 'destructive'}>
                              {execution.status}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {new Date(execution.started_at).toLocaleString()}
                            </span>
                          </div>
                          <div className="text-sm space-y-1">
                            <div>
                              <span className="font-medium">Input:</span>
                              <code className="ml-2 text-xs bg-muted px-1 rounded">
                                {JSON.stringify(execution.input)}
                              </code>
                            </div>
                            {execution.output && (
                              <div>
                                <span className="font-medium">Output:</span>
                                <code className="ml-2 text-xs bg-muted px-1 rounded">
                                  {JSON.stringify(execution.output)}
                                </code>
                              </div>
                            )}
                            <div className="text-xs text-muted-foreground">
                              Duration: {formatDuration(execution.execution_time)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="events" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Real-time Events</CardTitle>
                  <CardDescription>Live stream of tool events</CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-2">
                      {realTimeEvents.map((event, index) => (
                        <div key={index} className="text-sm p-2 bg-muted rounded">
                          <div className="flex justify-between">
                            <Badge variant="outline" className="text-xs">
                              {event.type}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {new Date(event.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                          <pre className="mt-1 text-xs overflow-auto">
                            {JSON.stringify(event.data, null, 2)}
                          </pre>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
}