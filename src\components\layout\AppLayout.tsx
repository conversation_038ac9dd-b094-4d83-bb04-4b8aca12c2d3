"use client";

import React, { ReactNode } from "react";
import Sidebar from "@/components/layout/Sidebar";
import { ThemeSwitcher } from "@/components/theme-switcher";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

interface AppLayoutProps {
  children: ReactNode;
}

export default function AppLayout({ children }: AppLayoutProps) {
  const pathname = usePathname();
  
  // Get the current page title based on the pathname
  const getPageTitle = () => {
    if (pathname === "/") return "Dashboard";
    if (pathname.startsWith("/agents")) return "Agent Builder";
    if (pathname.startsWith("/workflows")) return "Workflow Designer";
    if (pathname.startsWith("/providers")) return "Provider Integration";
    if (pathname.startsWith("/analytics")) return "Analytics";
    if (pathname.startsWith("/team")) return "Team";
    if (pathname.startsWith("/settings")) return "Settings";
    if (pathname.startsWith("/help")) return "Help";
    
    // Extract the page name from the pathname
    return pathname.split("/").pop()?.replace(/-/g, " ") || "Dashboard";
  };

  // Get the page description based on the pathname
  const getPageDescription = () => {
    if (pathname === "/") return "Welcome to SynapseAI - AI Orchestration Platform";
    if (pathname.startsWith("/agents")) return "Create and manage AI agents";
    if (pathname.startsWith("/workflows")) return "Design and orchestrate workflows";
    if (pathname.startsWith("/providers")) return "Configure AI providers and integrations";
    if (pathname.startsWith("/analytics")) return "Monitor performance and usage metrics";
    if (pathname.startsWith("/team")) return "Manage team members and permissions";
    if (pathname.startsWith("/settings")) return "Configure platform settings";
    if (pathname.startsWith("/help")) return "Get help and documentation";
    
    return "";
  };

  const pageTitle = getPageTitle();
  const pageDescription = getPageDescription();

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className={cn(
          "flex items-center justify-between px-6 py-4 border-b border-border",
          "bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
        )}>
          <div>
            <h1 className="text-2xl font-bold text-foreground">{pageTitle}</h1>
            <p className="text-sm text-muted-foreground">
              {pageDescription}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <ThemeSwitcher />
          </div>
        </header>
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
}