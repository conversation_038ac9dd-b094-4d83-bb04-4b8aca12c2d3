"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { toolAPI, CreateToolRequest, UpdateToolRequest, CreateToolVersionRequest, ExecuteToolRequest } from '@/lib/api/tools';
import { Tool, ToolVersion, ToolExecutionResult, ToolAuditLog } from '@/lib/types/tool';
import { useEventBus } from './event-bus-provider';
import { APIError } from '@/lib/api/base';

interface ToolContextType {
  // State
  tools: Tool[];
  loading: boolean;
  error: string | null;
  selectedTool: Tool | null;
  selectedVersion: ToolVersion | null;
  executionResults: ToolExecutionResult[];
  auditLogs: ToolAuditLog[];
  
  // Tool operations
  createTool: (toolData: CreateToolRequest) => Promise<Tool>;
  updateTool: (id: string, updates: UpdateToolRequest) => Promise<Tool>;
  deleteTool: (id: string) => Promise<boolean>;
  duplicateTool: (id: string, name?: string) => Promise<Tool>;
  selectTool: (id: string | null) => Promise<void>;
  
  // Version operations
  addToolVersion: (toolId: string, versionData: CreateToolVersionRequest) => Promise<ToolVersion>;
  updateToolVersion: (toolId: string, versionId: string, updates: Partial<CreateToolVersionRequest>) => Promise<ToolVersion>;
  deleteToolVersion: (toolId: string, versionId: string) => Promise<boolean>;
  activateToolVersion: (toolId: string, versionId: string) => Promise<boolean>;
  selectVersion: (versionId: string | null) => Promise<void>;
  
  // Execution operations
  executeTool: (toolId: string, input: Record<string, any>, options?: Partial<ExecuteToolRequest>) => Promise<ToolExecutionResult>;
  executeToolAsync: (toolId: string, input: Record<string, any>, options?: Partial<ExecuteToolRequest>) => Promise<{ executionId: string; status: string }>;
  getExecutionResult: (toolId: string, executionId: string) => Promise<ToolExecutionResult>;
  cancelExecution: (toolId: string, executionId: string) => Promise<boolean>;
  
  // Testing and validation
  testTool: (toolId: string, input: Record<string, any>, versionId?: string) => Promise<ToolExecutionResult>;
  validateToolSchema: (toolId: string, versionId: string, schemas: { inputSchema?: Record<string, any>; outputSchema?: Record<string, any> }) => Promise<{ valid: boolean; errors?: string[]; warnings?: string[] }>;
  validateToolInput: (toolId: string, input: Record<string, any>) => Promise<{ valid: boolean; errors?: string[] }>;
  
  // Deployment and management
  deployTool: (id: string, environment: 'development' | 'staging' | 'production') => Promise<{ deploymentId: string; status: string; endpoint?: string }>;
  getToolHealth: (id: string) => Promise<{ status: string; lastCheck: string; responseTime: number; errorRate: number; uptime: number }>;
  
  // Utility functions
  refreshTools: () => Promise<void>;
  refreshSelectedTool: () => Promise<void>;
  clearError: () => void;
}

const ToolContext = createContext<ToolContextType | undefined>(undefined);

export function ToolProvider({ children }: { children: ReactNode }) {
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);
  const [selectedVersion, setSelectedVersion] = useState<ToolVersion | null>(null);
  const [executionResults, setExecutionResults] = useState<ToolExecutionResult[]>([]);
  const [auditLogs, setAuditLogs] = useState<ToolAuditLog[]>([]);
  const { subscribe } = useEventBus();

  // Load tools on mount
  useEffect(() => {
    refreshTools();
  }, []);

  // Subscribe to real-time events
  useEffect(() => {
    const unsubscribeTool = subscribe('tool-events', (message) => {
      switch (message.type) {
        case 'tool_call_start':
        case 'tool_call_result':
        case 'tool_call_error':
          if (selectedTool && message.data.toolId === selectedTool.id) {
            refreshSelectedTool();
          }
          break;
        case 'state_update':
          if (message.data.toolId) {
            refreshTools();
          }
          break;
      }
    });

    return () => {
      unsubscribeTool();
    };
  }, [subscribe, selectedTool]);

  const handleError = useCallback((err: any) => {
    const errorMessage = err instanceof Error ? err.message : 
                        (err as APIError)?.message || 'An unexpected error occurred';
    setError(errorMessage);
    console.error('Tool Provider Error:', err);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refreshTools = useCallback(async () => {
    try {
      setLoading(true);
      clearError();
      
      const response = await toolAPI.listTools({
        limit: 100,
        sortBy: 'updatedAt',
        sortOrder: 'desc'
      });
      
      if (response.success && response.data) {
        setTools(response.data);
      } else {
        throw new Error(response.error || 'Failed to load tools');
      }
    } catch (err) {
      handleError(err);
    } finally {
      setLoading(false);
    }
  }, [handleError, clearError]);

  const refreshSelectedTool = useCallback(async () => {
    if (!selectedTool) return;
    
    try {
      // Refresh tool data
      const toolResponse = await toolAPI.getTool(selectedTool.id);
      if (toolResponse.success && toolResponse.data) {
        setSelectedTool(toolResponse.data);
      }

      // Refresh execution results
      const resultsResponse = await toolAPI.listExecutions(selectedTool.id, { limit: 50 });
      if (resultsResponse.success && resultsResponse.data) {
        setExecutionResults(resultsResponse.data);
      }

      // Refresh audit logs
      const logsResponse = await toolAPI.getToolAuditLogs(selectedTool.id, { limit: 50 });
      if (logsResponse.success && logsResponse.data) {
        setAuditLogs(logsResponse.data);
      }
    } catch (err) {
      handleError(err);
    }
  }, [selectedTool, handleError]);

  const selectTool = useCallback(async (id: string | null) => {
    if (!id) {
      setSelectedTool(null);
      setSelectedVersion(null);
      setExecutionResults([]);
      setAuditLogs([]);
      return;
    }

    try {
      clearError();
      
      // Get tool details
      const toolResponse = await toolAPI.getTool(id);
      if (!toolResponse.success || !toolResponse.data) {
        throw new Error(toolResponse.error || 'Tool not found');
      }
      
      setSelectedTool(toolResponse.data);

      // Get active version
      try {
        const versionResponse = await toolAPI.getActiveToolVersion(id);
        if (versionResponse.success && versionResponse.data) {
          setSelectedVersion(versionResponse.data);
        }
      } catch (err) {
        // No active version is okay
        setSelectedVersion(null);
      }

      // Load execution results
      const resultsResponse = await toolAPI.listExecutions(id, { limit: 50 });
      if (resultsResponse.success && resultsResponse.data) {
        setExecutionResults(resultsResponse.data);
      }

      // Load audit logs
      const logsResponse = await toolAPI.getToolAuditLogs(id, { limit: 50 });
      if (logsResponse.success && logsResponse.data) {
        setAuditLogs(logsResponse.data);
      }
    } catch (err) {
      handleError(err);
    }
  }, [handleError, clearError]);

  const selectVersion = useCallback(async (versionId: string | null) => {
    if (!versionId || !selectedTool) {
      setSelectedVersion(null);
      return;
    }

    try {
      clearError();
      
      const response = await toolAPI.getToolVersion(selectedTool.id, versionId);
      if (response.success && response.data) {
        setSelectedVersion(response.data);
      } else {
        throw new Error(response.error || 'Version not found');
      }
    } catch (err) {
      handleError(err);
    }
  }, [selectedTool, handleError, clearError]);

  const createTool = useCallback(async (toolData: CreateToolRequest): Promise<Tool> => {
    try {
      setLoading(true);
      clearError();
      
      const response = await toolAPI.createTool(toolData);
      
      if (response.success && response.data) {
        setTools(prev => [response.data!, ...prev]);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create tool');
      }
    } catch (err) {
      handleError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [handleError, clearError]);

  const updateTool = useCallback(async (id: string, updates: UpdateToolRequest): Promise<Tool> => {
    try {
      setLoading(true);
      clearError();
      
      const response = await toolAPI.updateTool(id, updates);
      
      if (response.success && response.data) {
        setTools(prev => prev.map(tool => 
          tool.id === id ? response.data! : tool
        ));
        
        if (selectedTool && selectedTool.id === id) {
          setSelectedTool(response.data);
        }
        
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to update tool');
      }
    } catch (err) {
      handleError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [selectedTool, handleError, clearError]);

  const deleteTool = useCallback(async (id: string): Promise<boolean> => {
    try {
      setLoading(true);
      clearError();
      
      const response = await toolAPI.deleteTool(id);
      
      if (response.success) {
        setTools(prev => prev.filter(tool => tool.id !== id));
        
        if (selectedTool && selectedTool.id === id) {
          setSelectedTool(null);
          setSelectedVersion(null);
          setExecutionResults([]);
          setAuditLogs([]);
        }
        
        return true;
      } else {
        throw new Error(response.error || 'Failed to delete tool');
      }
    } catch (err) {
      handleError(err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [selectedTool, handleError, clearError]);

  const duplicateTool = useCallback(async (id: string, name?: string): Promise<Tool> => {
    try {
      setLoading(true);
      clearError();
      
      const response = await toolAPI.duplicateTool(id, name);
      
      if (response.success && response.data) {
        setTools(prev => [response.data!, ...prev]);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to duplicate tool');
      }
    } catch (err) {
      handleError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [handleError, clearError]);

  const addToolVersion = useCallback(async (toolId: string, versionData: CreateToolVersionRequest): Promise<ToolVersion> => {
    try {
      clearError();
      
      const response = await toolAPI.createToolVersion(toolId, versionData);
      
      if (response.success && response.data) {
        // Refresh the selected tool if it's the one being updated
        if (selectedTool && selectedTool.id === toolId) {
          await refreshSelectedTool();
          setSelectedVersion(response.data);
        }
        
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to add tool version');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [selectedTool, refreshSelectedTool, handleError, clearError]);

  const updateToolVersion = useCallback(async (
    toolId: string, 
    versionId: string, 
    updates: Partial<CreateToolVersionRequest>
  ): Promise<ToolVersion> => {
    try {
      clearError();
      
      const response = await toolAPI.updateToolVersion(toolId, versionId, updates);
      
      if (response.success && response.data) {
        if (selectedTool && selectedTool.id === toolId) {
          await refreshSelectedTool();
          if (selectedVersion && selectedVersion.id === versionId) {
            setSelectedVersion(response.data);
          }
        }
        
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to update tool version');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [selectedTool, selectedVersion, refreshSelectedTool, handleError, clearError]);

  const deleteToolVersion = useCallback(async (toolId: string, versionId: string): Promise<boolean> => {
    try {
      clearError();
      
      const response = await toolAPI.deleteToolVersion(toolId, versionId);
      
      if (response.success) {
        if (selectedTool && selectedTool.id === toolId) {
          await refreshSelectedTool();
          if (selectedVersion && selectedVersion.id === versionId) {
            setSelectedVersion(null);
          }
        }
        
        return true;
      } else {
        throw new Error(response.error || 'Failed to delete tool version');
      }
    } catch (err) {
      handleError(err);
      return false;
    }
  }, [selectedTool, selectedVersion, refreshSelectedTool, handleError, clearError]);

  const activateToolVersion = useCallback(async (toolId: string, versionId: string): Promise<boolean> => {
    try {
      clearError();
      
      const response = await toolAPI.activateToolVersion(toolId, versionId);
      
      if (response.success) {
        if (selectedTool && selectedTool.id === toolId) {
          await refreshSelectedTool();
          // Get the updated version
          const versionResponse = await toolAPI.getToolVersion(toolId, versionId);
          if (versionResponse.success && versionResponse.data) {
            setSelectedVersion(versionResponse.data);
          }
        }
        
        return true;
      } else {
        throw new Error(response.error || 'Failed to activate tool version');
      }
    } catch (err) {
      handleError(err);
      return false;
    }
  }, [selectedTool, refreshSelectedTool, handleError, clearError]);

  const executeTool = useCallback(async (
    toolId: string, 
    input: Record<string, any>, 
    options: Partial<ExecuteToolRequest> = {}
  ): Promise<ToolExecutionResult> => {
    try {
      clearError();
      
      const response = await toolAPI.executeTool(toolId, { input, ...options });
      
      if (response.success && response.data) {
        // Update execution results if this is the selected tool
        if (selectedTool && selectedTool.id === toolId) {
          await refreshSelectedTool();
        }
        
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to execute tool');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [selectedTool, refreshSelectedTool, handleError, clearError]);

  const executeToolAsync = useCallback(async (
    toolId: string, 
    input: Record<string, any>, 
    options: Partial<ExecuteToolRequest> = {}
  ) => {
    try {
      clearError();
      
      const response = await toolAPI.executeToolAsync(toolId, { input, ...options });
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to execute tool asynchronously');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [handleError, clearError]);

  const getExecutionResult = useCallback(async (toolId: string, executionId: string): Promise<ToolExecutionResult> => {
    try {
      clearError();
      
      const response = await toolAPI.getExecutionResult(toolId, executionId);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to get execution result');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [handleError, clearError]);

  const cancelExecution = useCallback(async (toolId: string, executionId: string): Promise<boolean> => {
    try {
      clearError();
      
      const response = await toolAPI.cancelExecution(toolId, executionId);
      
      if (response.success) {
        if (selectedTool && selectedTool.id === toolId) {
          await refreshSelectedTool();
        }
        return true;
      } else {
        throw new Error(response.error || 'Failed to cancel execution');
      }
    } catch (err) {
      handleError(err);
      return false;
    }
  }, [selectedTool, refreshSelectedTool, handleError, clearError]);

  const testTool = useCallback(async (toolId: string, input: Record<string, any>, versionId?: string): Promise<ToolExecutionResult> => {
    try {
      clearError();
      
      const response = await toolAPI.testTool(toolId, { input, versionId });
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to test tool');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [handleError, clearError]);

  const validateToolSchema = useCallback(async (
    toolId: string, 
    versionId: string, 
    schemas: { inputSchema?: Record<string, any>; outputSchema?: Record<string, any> }
  ) => {
    try {
      clearError();
      
      const response = await toolAPI.validateToolSchema(toolId, versionId, schemas);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to validate tool schema');
      }
    } catch (err) {
      handleError(err);
      return { valid: false, errors: ['Validation failed'] };
    }
  }, [handleError, clearError]);

  const validateToolInput = useCallback(async (toolId: string, input: Record<string, any>) => {
    try {
      clearError();
      
      const response = await toolAPI.validateToolInput(toolId, input);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to validate tool input');
      }
    } catch (err) {
      handleError(err);
      return { valid: false, errors: ['Validation failed'] };
    }
  }, [handleError, clearError]);

  const deployTool = useCallback(async (
    id: string, 
    environment: 'development' | 'staging' | 'production'
  ) => {
    try {
      clearError();
      
      const response = await toolAPI.deployTool(id, environment);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to deploy tool');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [handleError, clearError]);

  const getToolHealth = useCallback(async (id: string) => {
    try {
      clearError();
      
      const response = await toolAPI.getToolHealth(id);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to get tool health');
      }
    } catch (err) {
      handleError(err);
      throw err;
    }
  }, [handleError, clearError]);

  const value: ToolContextType = {
    // State
    tools,
    loading,
    error,
    selectedTool,
    selectedVersion,
    executionResults,
    auditLogs,
    
    // Tool operations
    createTool,
    updateTool,
    deleteTool,
    duplicateTool,
    selectTool,
    
    // Version operations
    addToolVersion,
    updateToolVersion,
    deleteToolVersion,
    activateToolVersion,
    selectVersion,
    
    // Execution operations
    executeTool,
    executeToolAsync,
    getExecutionResult,
    cancelExecution,
    
    // Testing and validation
    testTool,
    validateToolSchema,
    validateToolInput,
    
    // Deployment and management
    deployTool,
    getToolHealth,
    
    // Utility functions
    refreshTools,
    refreshSelectedTool,
    clearError
  };

  return <ToolContext.Provider value={value}>{children}</ToolContext.Provider>;
}

export function useTool() {
  const context = useContext(ToolContext);
  if (context === undefined) {
    throw new Error('useTool must be used within a ToolProvider');
  }
  return context;
}