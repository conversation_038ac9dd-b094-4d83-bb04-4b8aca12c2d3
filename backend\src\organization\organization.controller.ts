import { <PERSON>, Get, Post, Body, Patch, Param, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { OrganizationService } from './organization.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Organizations')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('organizations')
export class OrganizationController {
  constructor(private organizationService: OrganizationService) {}

  @Get('current')
  @ApiOperation({ summary: 'Get current organization' })
  getCurrentOrganization(@Request() req) {
    return this.organizationService.findById(req.user.organizationId);
  }

  @Get('usage')
  @ApiOperation({ summary: 'Get organization usage statistics' })
  getUsageStats(@Request() req) {
    return this.organizationService.getUsageStats(req.user.organizationId);
  }

  @Patch('settings')
  @ApiOperation({ summary: 'Update organization settings' })
  updateSettings(@Request() req, @Body() settings: any) {
    return this.organizationService.updateSettings(req.user.organizationId, settings);
  }

  @Patch('branding')
  @ApiOperation({ summary: 'Update organization branding' })
  updateBranding(@Request() req, @Body() branding: any) {
    return this.organizationService.updateBranding(req.user.organizationId, branding);
  }
}