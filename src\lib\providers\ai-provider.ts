import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';

export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface AIResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  cost: number;
  model: string;
  provider: string;
}

export interface ProviderConfig {
  primary: string;
  fallbacks: string[];
  maxRetries: number;
  timeout: number;
}

class AIProviderService {
  private openai: OpenAI;
  private anthropic: Anthropic;
  private providers: Map<string, any> = new Map();

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    });

    this.providers.set('openai', this.openai);
    this.providers.set('anthropic', this.anthropic);
  }

  async generateResponse(
    messages: AIMessage[],
    model: string,
    config: ProviderConfig
  ): Promise<AIResponse> {
    const provider = this.getProviderFromModel(model);
    
    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        const providerName = attempt === 0 ? provider : config.fallbacks[attempt - 1];
        if (!providerName) throw new Error('No more fallback providers');

        return await this.callProvider(providerName, messages, model);
      } catch (error) {
        if (attempt === config.maxRetries) throw error;
        console.warn(`Provider ${provider} failed, trying fallback:`, error.message);
      }
    }

    throw new Error('All providers failed');
  }

  private async callProvider(
    providerName: string,
    messages: AIMessage[],
    model: string
  ): Promise<AIResponse> {
    switch (providerName) {
      case 'openai':
        return await this.callOpenAI(messages, model);
      case 'anthropic':
        return await this.callAnthropic(messages, model);
      default:
        throw new Error(`Unknown provider: ${providerName}`);
    }
  }

  private async callOpenAI(messages: AIMessage[], model: string): Promise<AIResponse> {
    const response = await this.openai.chat.completions.create({
      model: model,
      messages: messages,
      temperature: 0.7,
      max_tokens: 4000,
    });

    const usage = response.usage!;
    const cost = this.calculateOpenAICost(model, usage.prompt_tokens, usage.completion_tokens);

    return {
      content: response.choices[0].message.content!,
      usage: {
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        totalTokens: usage.total_tokens,
      },
      cost,
      model,
      provider: 'openai',
    };
  }

  private async callAnthropic(messages: AIMessage[], model: string): Promise<AIResponse> {
    const systemMessage = messages.find(m => m.role === 'system')?.content || '';
    const userMessages = messages.filter(m => m.role !== 'system');

    const response = await this.anthropic.messages.create({
      model: model,
      max_tokens: 4000,
      system: systemMessage,
      messages: userMessages.map(m => ({
        role: m.role as 'user' | 'assistant',
        content: m.content,
      })),
    });

    const usage = response.usage;
    const cost = this.calculateAnthropicCost(model, usage.input_tokens, usage.output_tokens);

    return {
      content: response.content[0].type === 'text' ? response.content[0].text : '',
      usage: {
        promptTokens: usage.input_tokens,
        completionTokens: usage.output_tokens,
        totalTokens: usage.input_tokens + usage.output_tokens,
      },
      cost,
      model,
      provider: 'anthropic',
    };
  }

  private calculateOpenAICost(model: string, inputTokens: number, outputTokens: number): number {
    const pricing = {
      'gpt-4': { input: 0.03, output: 0.06 },
      'gpt-4-turbo': { input: 0.01, output: 0.03 },
      'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
    };

    const rates = pricing[model] || pricing['gpt-4'];
    return (inputTokens * rates.input + outputTokens * rates.output) / 1000;
  }

  private calculateAnthropicCost(model: string, inputTokens: number, outputTokens: number): number {
    const pricing = {
      'claude-3-opus-20240229': { input: 0.015, output: 0.075 },
      'claude-3-sonnet-20240229': { input: 0.003, output: 0.015 },
      'claude-3-haiku-20240307': { input: 0.00025, output: 0.00125 },
    };

    const rates = pricing[model] || pricing['claude-3-sonnet-20240229'];
    return (inputTokens * rates.input + outputTokens * rates.output) / 1000;
  }

  private getProviderFromModel(model: string): string {
    if (model.startsWith('gpt-')) return 'openai';
    if (model.startsWith('claude-')) return 'anthropic';
    return 'openai'; // default
  }

  async streamResponse(
    messages: AIMessage[],
    model: string,
    onChunk: (chunk: string) => void
  ): Promise<AIResponse> {
    const provider = this.getProviderFromModel(model);

    if (provider === 'openai') {
      return await this.streamOpenAI(messages, model, onChunk);
    } else if (provider === 'anthropic') {
      return await this.streamAnthropic(messages, model, onChunk);
    }

    throw new Error(`Streaming not supported for provider: ${provider}`);
  }

  private async streamOpenAI(
    messages: AIMessage[],
    model: string,
    onChunk: (chunk: string) => void
  ): Promise<AIResponse> {
    const stream = await this.openai.chat.completions.create({
      model: model,
      messages: messages,
      temperature: 0.7,
      max_tokens: 4000,
      stream: true,
    });

    let fullContent = '';
    let usage = { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 };

    for await (const chunk of stream) {
      const delta = chunk.choices[0]?.delta?.content || '';
      if (delta) {
        fullContent += delta;
        onChunk(delta);
      }

      if (chunk.usage) {
        usage = chunk.usage;
      }
    }

    const cost = this.calculateOpenAICost(model, usage.prompt_tokens, usage.completion_tokens);

    return {
      content: fullContent,
      usage: {
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        totalTokens: usage.total_tokens,
      },
      cost,
      model,
      provider: 'openai',
    };
  }

  private async streamAnthropic(
    messages: AIMessage[],
    model: string,
    onChunk: (chunk: string) => void
  ): Promise<AIResponse> {
    const systemMessage = messages.find(m => m.role === 'system')?.content || '';
    const userMessages = messages.filter(m => m.role !== 'system');

    const stream = await this.anthropic.messages.create({
      model: model,
      max_tokens: 4000,
      system: systemMessage,
      messages: userMessages.map(m => ({
        role: m.role as 'user' | 'assistant',
        content: m.content,
      })),
      stream: true,
    });

    let fullContent = '';
    let usage = { input_tokens: 0, output_tokens: 0 };

    for await (const chunk of stream) {
      if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
        const delta = chunk.delta.text;
        fullContent += delta;
        onChunk(delta);
      }

      if (chunk.type === 'message_delta' && chunk.usage) {
        usage.output_tokens = chunk.usage.output_tokens;
      }
    }

    const cost = this.calculateAnthropicCost(model, usage.input_tokens, usage.output_tokens);

    return {
      content: fullContent,
      usage: {
        promptTokens: usage.input_tokens,
        completionTokens: usage.output_tokens,
        totalTokens: usage.input_tokens + usage.output_tokens,
      },
      cost,
      model,
      provider: 'anthropic',
    };
  }
}

export const aiProvider = new AIProviderService();