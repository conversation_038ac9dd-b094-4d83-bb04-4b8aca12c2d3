"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/components/auth/AuthProvider";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Users,
  UserPlus,
  Settings,
  Shield,
  Mail,
  MoreHorizontal,
  Edit,
  Trash2,
  Crown,
  Building2,
  Calendar,
  Activity,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface User {
  id: string;
  email: string;
  profile: {
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  roles: Array<{
    id: string;
    name: string;
    level: "SUPER_ADMIN" | "ORG_ADMIN" | "DEVELOPER" | "VIEWER";
  }>;
  lastLogin?: string;
  createdAt: string;
  isActive: boolean;
}

interface InviteData {
  email: string;
  role: string;
  message?: string;
}

export default function UserManagement() {
  const { user: currentUser, organization, hasPermission } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [editUserDialogOpen, setEditUserDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [inviteData, setInviteData] = useState<InviteData>({
    email: "",
    role: "VIEWER",
    message: "",
  });

  const API_BASE = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

  useEffect(() => {
    if (hasPermission("users:read")) {
      fetchUsers();
    }
  }, [hasPermission]);

  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem("accessToken");
      const response = await fetch(`${API_BASE}/users`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      } else {
        setError("Failed to fetch users");
      }
    } catch (err) {
      setError("Failed to fetch users");
    } finally {
      setLoading(false);
    }
  };

  const handleInviteUser = async () => {
    try {
      const token = localStorage.getItem("accessToken");
      const response = await fetch(`${API_BASE}/users/invite`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(inviteData),
      });

      if (response.ok) {
        setInviteDialogOpen(false);
        setInviteData({ email: "", role: "VIEWER", message: "" });
        fetchUsers();
      } else {
        const error = await response.json();
        setError(error.message || "Failed to invite user");
      }
    } catch (err) {
      setError("Failed to invite user");
    }
  };

  const handleUpdateUserRole = async (userId: string, roleId: string) => {
    try {
      const token = localStorage.getItem("accessToken");
      const response = await fetch(`${API_BASE}/users/${userId}/role`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ roleId }),
      });

      if (response.ok) {
        fetchUsers();
      } else {
        setError("Failed to update user role");
      }
    } catch (err) {
      setError("Failed to update user role");
    }
  };

  const handleDeactivateUser = async (userId: string) => {
    try {
      const token = localStorage.getItem("accessToken");
      const response = await fetch(`${API_BASE}/users/${userId}/deactivate`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        fetchUsers();
      } else {
        setError("Failed to deactivate user");
      }
    } catch (err) {
      setError("Failed to deactivate user");
    }
  };

  const getRoleBadgeVariant = (level: string) => {
    switch (level) {
      case "SUPER_ADMIN":
        return "destructive";
      case "ORG_ADMIN":
        return "default";
      case "DEVELOPER":
        return "secondary";
      case "VIEWER":
        return "outline";
      default:
        return "outline";
    }
  };

  const getRoleIcon = (level: string) => {
    switch (level) {
      case "SUPER_ADMIN":
        return <Crown className="w-3 h-3" />;
      case "ORG_ADMIN":
        return <Shield className="w-3 h-3" />;
      case "DEVELOPER":
        return <Settings className="w-3 h-3" />;
      case "VIEWER":
        return <Users className="w-3 h-3" />;
      default:
        return <Users className="w-3 h-3" />;
    }
  };

  if (!hasPermission("users:read")) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center space-y-4">
          <Shield className="w-12 h-12 text-muted-foreground mx-auto" />
          <h3 className="text-lg font-semibold">Access Denied</h3>
          <p className="text-muted-foreground">
            You don't have permission to view user management.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 bg-background">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            User Management
          </h1>
          <p className="text-muted-foreground">
            Manage users and permissions for {organization?.name}
          </p>
        </div>

        {hasPermission("users:invite") && (
          <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <UserPlus className="w-4 h-4 mr-2" />
                Invite User
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Invite New User</DialogTitle>
                <DialogDescription>
                  Send an invitation to join your organization
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="invite-email">Email Address</Label>
                  <Input
                    id="invite-email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={inviteData.email}
                    onChange={(e) =>
                      setInviteData({ ...inviteData, email: e.target.value })
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="invite-role">Role</Label>
                  <Select
                    value={inviteData.role}
                    onValueChange={(value) =>
                      setInviteData({ ...inviteData, role: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="VIEWER">Viewer</SelectItem>
                      <SelectItem value="DEVELOPER">Developer</SelectItem>
                      {hasPermission("users:admin") && (
                        <SelectItem value="ORG_ADMIN">
                          Organization Admin
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="invite-message">
                    Personal Message (Optional)
                  </Label>
                  <Input
                    id="invite-message"
                    placeholder="Welcome to our team!"
                    value={inviteData.message}
                    onChange={(e) =>
                      setInviteData({ ...inviteData, message: e.target.value })
                    }
                  />
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setInviteDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button onClick={handleInviteUser}>Send Invitation</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="users" className="space-y-6">
        <TabsList>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="roles">Roles & Permissions</TabsTrigger>
          <TabsTrigger value="activity">Activity Log</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Users className="w-5 h-5 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Total Users
                    </p>
                    <p className="text-2xl font-bold">{users.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Shield className="w-5 h-5 text-green-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Admins
                    </p>
                    <p className="text-2xl font-bold">
                      {
                        users.filter((u) =>
                          u.roles.some((r) => r.level === "ORG_ADMIN"),
                        ).length
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Settings className="w-5 h-5 text-purple-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Developers
                    </p>
                    <p className="text-2xl font-bold">
                      {
                        users.filter((u) =>
                          u.roles.some((r) => r.level === "DEVELOPER"),
                        ).length
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Activity className="w-5 h-5 text-orange-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Active Today
                    </p>
                    <p className="text-2xl font-bold">
                      {
                        users.filter(
                          (u) =>
                            u.lastLogin &&
                            new Date(u.lastLogin).toDateString() ===
                              new Date().toDateString(),
                        ).length
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle>Organization Members</CardTitle>
              <CardDescription>
                Manage user access and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Last Login</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="w-8 h-8">
                            <AvatarImage src={user.profile.avatar} />
                            <AvatarFallback>
                              {user.profile.firstName[0]}
                              {user.profile.lastName[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">
                              {user.profile.firstName} {user.profile.lastName}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {user.email}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {user.roles.map((role) => (
                            <Badge
                              key={role.id}
                              variant={getRoleBadgeVariant(role.level)}
                              className="flex items-center gap-1"
                            >
                              {getRoleIcon(role.level)}
                              {role.name}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        {user.lastLogin ? (
                          <div className="text-sm">
                            <p>
                              {new Date(user.lastLogin).toLocaleDateString()}
                            </p>
                            <p className="text-muted-foreground">
                              {new Date(user.lastLogin).toLocaleTimeString()}
                            </p>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Never</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={user.isActive ? "default" : "secondary"}
                        >
                          {user.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {hasPermission("users:update") &&
                          user.id !== currentUser?.id && (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedUser(user);
                                    setEditUserDialogOpen(true);
                                  }}
                                >
                                  <Edit className="w-4 h-4 mr-2" />
                                  Edit Role
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleDeactivateUser(user.id)}
                                  className="text-destructive"
                                >
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Deactivate
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles">
          <Card>
            <CardHeader>
              <CardTitle>Roles & Permissions</CardTitle>
              <CardDescription>
                Manage role-based access control
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Role definitions */}
                <div className="grid gap-4">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Crown className="w-5 h-5 text-red-500" />
                      <h3 className="font-semibold">Super Admin</h3>
                      <Badge variant="destructive">System Level</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      Full system access across all organizations
                    </p>
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="outline">All Permissions</Badge>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Shield className="w-5 h-5 text-blue-500" />
                      <h3 className="font-semibold">Organization Admin</h3>
                      <Badge>Organization Level</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      Full access within the organization
                    </p>
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="outline">User Management</Badge>
                      <Badge variant="outline">Agent Management</Badge>
                      <Badge variant="outline">Workflow Management</Badge>
                      <Badge variant="outline">Analytics</Badge>
                      <Badge variant="outline">Billing</Badge>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Settings className="w-5 h-5 text-purple-500" />
                      <h3 className="font-semibold">Developer</h3>
                      <Badge variant="secondary">Development Level</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      Create and manage agents, tools, and workflows
                    </p>
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="outline">Create Agents</Badge>
                      <Badge variant="outline">Create Tools</Badge>
                      <Badge variant="outline">Create Workflows</Badge>
                      <Badge variant="outline">View Analytics</Badge>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="w-5 h-5 text-green-500" />
                      <h3 className="font-semibold">Viewer</h3>
                      <Badge variant="outline">Read Only</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      View-only access to agents, tools, and workflows
                    </p>
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="outline">View Agents</Badge>
                      <Badge variant="outline">View Tools</Badge>
                      <Badge variant="outline">View Workflows</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle>Activity Log</CardTitle>
              <CardDescription>
                Recent user activity and system events
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="w-12 h-12 mx-auto mb-4" />
                <p>Activity logging will be implemented here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
