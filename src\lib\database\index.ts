import { Pool, PoolClient } from 'pg';
import Redis from 'ioredis';

// Database connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Redis connection for sessions and caching
export const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379', {
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
});

// Database query wrapper with organization scoping
export class Database {
  private client: PoolClient | null = null;
  private organizationId: string | null = null;

  constructor(organizationId?: string) {
    this.organizationId = organizationId || null;
  }

  async connect(): Promise<void> {
    this.client = await pool.connect();
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      this.client.release();
      this.client = null;
    }
  }

  async query<T = any>(text: string, params: any[] = []): Promise<T[]> {
    if (!this.client) {
      await this.connect();
    }
    
    try {
      const result = await this.client!.query(text, params);
      return result.rows;
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  // Organization-scoped query helper
  async orgQuery<T = any>(
    table: string,
    conditions: string = '',
    params: any[] = [],
    select: string = '*'
  ): Promise<T[]> {
    if (!this.organizationId) {
      throw new Error('Organization ID required for scoped queries');
    }

    const whereClause = conditions 
      ? `WHERE organization_id = $1 AND ${conditions}`
      : 'WHERE organization_id = $1';
    
    const query = `SELECT ${select} FROM ${table} ${whereClause}`;
    return this.query<T>(query, [this.organizationId, ...params]);
  }

  // Insert with organization scoping
  async orgInsert<T = any>(
    table: string,
    data: Record<string, any>,
    returning: string = '*'
  ): Promise<T> {
    if (!this.organizationId) {
      throw new Error('Organization ID required for scoped inserts');
    }

    data.organization_id = this.organizationId;
    
    const columns = Object.keys(data);
    const values = Object.values(data);
    const placeholders = values.map((_, i) => `$${i + 1}`).join(', ');
    
    const query = `
      INSERT INTO ${table} (${columns.join(', ')})
      VALUES (${placeholders})
      RETURNING ${returning}
    `;
    
    const result = await this.query<T>(query, values);
    return result[0];
  }

  // Update with organization scoping
  async orgUpdate<T = any>(
    table: string,
    id: string,
    data: Record<string, any>,
    returning: string = '*'
  ): Promise<T> {
    if (!this.organizationId) {
      throw new Error('Organization ID required for scoped updates');
    }

    const columns = Object.keys(data);
    const values = Object.values(data);
    const setClause = columns.map((col, i) => `${col} = $${i + 3}`).join(', ');
    
    const query = `
      UPDATE ${table}
      SET ${setClause}, updated_at = NOW()
      WHERE organization_id = $1 AND id = $2
      RETURNING ${returning}
    `;
    
    const result = await this.query<T>(query, [this.organizationId, id, ...values]);
    return result[0];
  }

  // Delete with organization scoping
  async orgDelete(table: string, id: string): Promise<boolean> {
    if (!this.organizationId) {
      throw new Error('Organization ID required for scoped deletes');
    }

    const query = `DELETE FROM ${table} WHERE organization_id = $1 AND id = $2`;
    const result = await this.query(query, [this.organizationId, id]);
    return result.length > 0;
  }

  // Transaction wrapper
  async transaction<T>(callback: (db: Database) => Promise<T>): Promise<T> {
    const client = await pool.connect();
    const transactionDb = new Database(this.organizationId);
    transactionDb.client = client;

    try {
      await client.query('BEGIN');
      const result = await callback(transactionDb);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }
}

// Session management utilities
export class SessionManager {
  private redis: Redis;
  private organizationId: string;

  constructor(organizationId: string) {
    this.redis = redis;
    this.organizationId = organizationId;
  }

  async createSession(
    userId: string,
    type: 'agent' | 'tool' | 'hybrid' | 'hitl',
    context: Record<string, any> = {},
    expiresIn: number = 3600 // 1 hour default
  ): Promise<string> {
    const sessionId = `session:${this.organizationId}:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
    
    const sessionData = {
      id: sessionId,
      organizationId: this.organizationId,
      userId,
      type,
      context,
      memory: {},
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + expiresIn * 1000).toISOString(),
    };

    await this.redis.setex(sessionId, expiresIn, JSON.stringify(sessionData));
    
    // Also store in database for persistence
    const db = new Database(this.organizationId);
    await db.orgInsert('sessions', {
      id: sessionId,
      user_id: userId,
      type,
      context,
      expires_at: sessionData.expiresAt,
    });
    
    return sessionId;
  }

  async getSession(sessionId: string): Promise<any | null> {
    const cached = await this.redis.get(sessionId);
    if (cached) {
      return JSON.parse(cached);
    }

    // Fallback to database
    const db = new Database(this.organizationId);
    const sessions = await db.orgQuery('sessions', 'id = $1', [sessionId]);
    
    if (sessions.length === 0) {
      return null;
    }

    const session = sessions[0];
    
    // Re-cache if not expired
    if (new Date(session.expires_at) > new Date()) {
      const ttl = Math.floor((new Date(session.expires_at).getTime() - Date.now()) / 1000);
      await this.redis.setex(sessionId, ttl, JSON.stringify(session));
    }

    return session;
  }

  async updateSessionMemory(sessionId: string, memory: Record<string, any>): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    session.memory = { ...session.memory, ...memory };
    session.updatedAt = new Date().toISOString();

    const ttl = Math.floor((new Date(session.expiresAt).getTime() - Date.now()) / 1000);
    if (ttl > 0) {
      await this.redis.setex(sessionId, ttl, JSON.stringify(session));
    }

    // Update database
    const db = new Database(this.organizationId);
    await db.orgUpdate('sessions', sessionId, { memory: session.memory });
  }

  async extendSession(sessionId: string, additionalTime: number = 3600): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    const newExpiresAt = new Date(Date.now() + additionalTime * 1000).toISOString();
    session.expiresAt = newExpiresAt;

    await this.redis.setex(sessionId, additionalTime, JSON.stringify(session));

    // Update database
    const db = new Database(this.organizationId);
    await db.orgUpdate('sessions', sessionId, { expires_at: newExpiresAt });
  }

  async deleteSession(sessionId: string): Promise<void> {
    await this.redis.del(sessionId);
    
    const db = new Database(this.organizationId);
    await db.orgDelete('sessions', sessionId);
  }
}

// Quota enforcement
export class QuotaManager {
  private db: Database;

  constructor(organizationId: string) {
    this.db = new Database(organizationId);
  }

  async checkQuota(resourceType: string, requestedAmount: number = 1): Promise<boolean> {
    const quotas = await this.db.orgQuery(
      'quotas',
      'resource_type = $1',
      [resourceType]
    );

    if (quotas.length === 0) {
      return true; // No quota set
    }

    const quota = quotas[0];
    return (quota.current_usage + requestedAmount) <= quota.limit_value;
  }

  async consumeQuota(resourceType: string, amount: number = 1): Promise<void> {
    const hasQuota = await this.checkQuota(resourceType, amount);
    if (!hasQuota) {
      throw new Error(`Quota exceeded for ${resourceType}`);
    }

    await this.db.query(
      `UPDATE quotas 
       SET current_usage = current_usage + $1, updated_at = NOW()
       WHERE organization_id = $2 AND resource_type = $3`,
      [amount, this.db['organizationId'], resourceType]
    );
  }

  async trackUsage(
    resourceType: string,
    resourceId: string | null,
    quantity: number,
    cost?: number,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    await this.db.orgInsert('billing_usage', {
      resource_type: resourceType,
      resource_id: resourceId,
      quantity,
      cost,
      metadata,
    });

    // Update quota usage
    await this.consumeQuota(resourceType, quantity);
  }
}

export { pool };