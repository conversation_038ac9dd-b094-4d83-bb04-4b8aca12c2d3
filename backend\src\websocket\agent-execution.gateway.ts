import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { AgentService } from '../agent/agent.service';

@Injectable()
@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL?.split(',') || ['http://localhost:3000'],
    credentials: true,
  },
  namespace: '/agent-execution',
})
export class AgentExecutionGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(AgentExecutionGateway.name);
  private connectedClients = new Map<string, { socket: Socket; userId: string; organizationId: string }>();

  constructor(
    private jwtService: JwtService,
    private agentService: AgentService,
  ) {}

  async handleConnection(client: Socket) {
    try {
      const token = client.handshake.auth.token || client.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token);
      const userId = payload.sub;
      const organizationId = payload.organizationId;

      this.connectedClients.set(client.id, { socket: client, userId, organizationId });
      
      // Join organization room for broadcasting
      client.join(`org:${organizationId}`);
      
      this.logger.log(`Client connected: ${client.id} (User: ${userId}, Org: ${organizationId})`);
      
      client.emit('connected', { status: 'connected', userId, organizationId });
    } catch (error) {
      this.logger.error('Connection authentication failed:', error.message);
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    const clientInfo = this.connectedClients.get(client.id);
    if (clientInfo) {
      this.logger.log(`Client disconnected: ${client.id} (User: ${clientInfo.userId})`);
      this.connectedClients.delete(client.id);
    }
  }

  @SubscribeMessage('execute_agent')
  async handleAgentExecution(
    @MessageBody() data: { agentId: string; message: string; sessionId?: string },
    @ConnectedSocket() client: Socket,
  ) {
    const clientInfo = this.connectedClients.get(client.id);
    if (!clientInfo) {
      client.emit('error', { message: 'Unauthorized' });
      return;
    }

    const { userId, organizationId } = clientInfo;
    const { agentId, message, sessionId } = data;

    try {
      // Emit execution started
      client.emit('execution_started', {
        agentId,
        message,
        sessionId,
        timestamp: new Date().toISOString(),
      });

      // Execute agent with streaming
      const result = await this.agentService.chat(
        agentId,
        organizationId,
        message,
        sessionId,
        (chunk: string) => {
          // Stream chunks to client
          client.emit('text_chunk', {
            agentId,
            sessionId,
            chunk,
            timestamp: new Date().toISOString(),
          });
        }
      );

      // Emit execution completed
      client.emit('execution_completed', {
        agentId,
        sessionId,
        result: result.response,
        usage: result.usage,
        cost: result.cost,
        executionTime: result.executionTime,
        timestamp: new Date().toISOString(),
      });

      // Broadcast to organization (for admin monitoring)
      this.server.to(`org:${organizationId}`).emit('agent_activity', {
        type: 'execution_completed',
        agentId,
        userId,
        cost: result.cost,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      this.logger.error(`Agent execution failed: ${error.message}`);
      
      client.emit('execution_error', {
        agentId,
        sessionId,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('join_session')
  async handleJoinSession(
    @MessageBody() data: { sessionId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const clientInfo = this.connectedClients.get(client.id);
    if (!clientInfo) return;

    client.join(`session:${data.sessionId}`);
    client.emit('session_joined', { sessionId: data.sessionId });
  }

  @SubscribeMessage('leave_session')
  async handleLeaveSession(
    @MessageBody() data: { sessionId: string },
    @ConnectedSocket() client: Socket,
  ) {
    client.leave(`session:${data.sessionId}`);
    client.emit('session_left', { sessionId: data.sessionId });
  }

  // Method to broadcast events from services
  broadcastToSession(sessionId: string, event: string, data: any) {
    this.server.to(`session:${sessionId}`).emit(event, data);
  }

  broadcastToOrganization(organizationId: string, event: string, data: any) {
    this.server.to(`org:${organizationId}`).emit(event, data);
  }

  broadcastToUser(userId: string, event: string, data: any) {
    for (const [clientId, clientInfo] of this.connectedClients) {
      if (clientInfo.userId === userId) {
        clientInfo.socket.emit(event, data);
      }
    }
  }
}