import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { Database } from '../database';
import { NextRequest, NextResponse } from 'next/server';

export interface User {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  email_verified: boolean;
}

export interface OrganizationMember {
  id: string;
  organization_id: string;
  user_id: string;
  role: 'super_admin' | 'org_admin' | 'developer' | 'viewer';
  permissions: Record<string, boolean>;
  joined_at: string;
}

export interface AuthContext {
  user: User;
  organization: {
    id: string;
    name: string;
    slug: string;
    plan: string;
  };
  role: string;
  permissions: Record<string, boolean>;
}

export class AuthService {
  private jwtSecret: string;
  private jwtExpiresIn: string;

  constructor() {
    this.jwtSecret = process.env.JWT_SECRET!;
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '7d';
  }

  // Register new user
  async register(
    email: string,
    password: string,
    name: string,
    organizationName?: string
  ): Promise<{ user: User; token: string; organization: any }> {
    const db = new Database();
    
    try {
      await db.connect();

      // Check if user already exists
      const existingUsers = await db.query(
        'SELECT id FROM users WHERE email = $1',
        [email]
      );

      if (existingUsers.length > 0) {
        throw new Error('User already exists');
      }

      // Hash password
      const passwordHash = await bcrypt.hash(password, 12);

      // Create user
      const user = await db.query(
        `INSERT INTO users (email, password_hash, name)
         VALUES ($1, $2, $3)
         RETURNING id, email, name, avatar_url, email_verified`,
        [email, passwordHash, name]
      );

      const newUser = user[0];

      // Create or join organization
      let organization;
      if (organizationName) {
        // Create new organization
        const orgSlug = organizationName.toLowerCase().replace(/[^a-z0-9]/g, '-');
        organization = await db.query(
          `INSERT INTO organizations (name, slug)
           VALUES ($1, $2)
           RETURNING id, name, slug, plan`,
          [organizationName, orgSlug]
        );

        // Add user as org admin
        await db.query(
          `INSERT INTO organization_members (organization_id, user_id, role)
           VALUES ($1, $2, 'org_admin')`,
          [organization[0].id, newUser.id]
        );
      } else {
        // Join default organization or create personal org
        const personalOrgName = `${name}'s Organization`;
        const orgSlug = `${name.toLowerCase().replace(/[^a-z0-9]/g, '-')}-org`;
        
        organization = await db.query(
          `INSERT INTO organizations (name, slug)
           VALUES ($1, $2)
           RETURNING id, name, slug, plan`,
          [personalOrgName, orgSlug]
        );

        await db.query(
          `INSERT INTO organization_members (organization_id, user_id, role)
           VALUES ($1, $2, 'org_admin')`,
          [organization[0].id, newUser.id]
        );
      }

      // Generate JWT token
      const token = this.generateToken(newUser.id, organization[0].id);

      return {
        user: newUser,
        token,
        organization: organization[0]
      };
    } finally {
      await db.disconnect();
    }
  }

  // Login user
  async login(email: string, password: string): Promise<{ user: User; token: string; organization: any }> {
    const db = new Database();
    
    try {
      await db.connect();

      // Get user with organization info
      const users = await db.query(
        `SELECT u.*, om.organization_id, om.role, o.name as org_name, o.slug as org_slug, o.plan as org_plan
         FROM users u
         JOIN organization_members om ON u.id = om.user_id
         JOIN organizations o ON om.organization_id = o.id
         WHERE u.email = $1
         LIMIT 1`,
        [email]
      );

      if (users.length === 0) {
        throw new Error('Invalid credentials');
      }

      const user = users[0];

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      if (!isValidPassword) {
        throw new Error('Invalid credentials');
      }

      // Generate JWT token
      const token = this.generateToken(user.id, user.organization_id);

      return {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar_url: user.avatar_url,
          email_verified: user.email_verified
        },
        token,
        organization: {
          id: user.organization_id,
          name: user.org_name,
          slug: user.org_slug,
          plan: user.org_plan
        }
      };
    } finally {
      await db.disconnect();
    }
  }

  // Generate JWT token
  private generateToken(userId: string, organizationId: string): string {
    return jwt.sign(
      { userId, organizationId },
      this.jwtSecret,
      { expiresIn: this.jwtExpiresIn }
    );
  }

  // Verify JWT token
  async verifyToken(token: string): Promise<AuthContext> {
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as any;
      
      const db = new Database();
      await db.connect();

      const users = await db.query(
        `SELECT u.*, om.organization_id, om.role, om.permissions,
                o.name as org_name, o.slug as org_slug, o.plan as org_plan
         FROM users u
         JOIN organization_members om ON u.id = om.user_id
         JOIN organizations o ON om.organization_id = o.id
         WHERE u.id = $1 AND om.organization_id = $2`,
        [decoded.userId, decoded.organizationId]
      );

      if (users.length === 0) {
        throw new Error('Invalid token');
      }

      const user = users[0];
      await db.disconnect();

      return {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar_url: user.avatar_url,
          email_verified: user.email_verified
        },
        organization: {
          id: user.organization_id,
          name: user.org_name,
          slug: user.org_slug,
          plan: user.org_plan
        },
        role: user.role,
        permissions: user.permissions || {}
      };
    } catch (error) {
      throw new Error('Invalid token');
    }
  }

  // Refresh token
  async refreshToken(token: string): Promise<string> {
    const authContext = await this.verifyToken(token);
    return this.generateToken(authContext.user.id, authContext.organization.id);
  }
}

// RBAC Permission System
export class PermissionService {
  private static rolePermissions: Record<string, string[]> = {
    super_admin: ['*'], // All permissions
    org_admin: [
      'agents:create', 'agents:read', 'agents:update', 'agents:delete',
      'tools:create', 'tools:read', 'tools:update', 'tools:delete',
      'hybrids:create', 'hybrids:read', 'hybrids:update', 'hybrids:delete',
      'sessions:read', 'sessions:delete',
      'providers:create', 'providers:read', 'providers:update', 'providers:delete',
      'templates:create', 'templates:read', 'templates:update', 'templates:delete',
      'analytics:read', 'billing:read', 'users:read', 'users:invite'
    ],
    developer: [
      'agents:create', 'agents:read', 'agents:update',
      'tools:create', 'tools:read', 'tools:update',
      'hybrids:create', 'hybrids:read', 'hybrids:update',
      'sessions:read', 'providers:read', 'templates:read', 'templates:create'
    ],
    viewer: [
      'agents:read', 'tools:read', 'hybrids:read',
      'sessions:read', 'templates:read', 'analytics:read'
    ]
  };

  static hasPermission(role: string, permission: string, customPermissions?: Record<string, boolean>): boolean {
    // Check custom permissions first
    if (customPermissions && customPermissions.hasOwnProperty(permission)) {
      return customPermissions[permission];
    }

    // Check role-based permissions
    const rolePerms = this.rolePermissions[role] || [];
    
    // Super admin has all permissions
    if (rolePerms.includes('*')) {
      return true;
    }

    return rolePerms.includes(permission);
  }

  static canAccessResource(
    role: string,
    resource: string,
    action: string,
    customPermissions?: Record<string, boolean>
  ): boolean {
    const permission = `${resource}:${action}`;
    return this.hasPermission(role, permission, customPermissions);
  }
}

// Middleware for API route protection
export function withAuth(handler: Function) {
  return async (req: NextRequest) => {
    try {
      const authHeader = req.headers.get('authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }

      const token = authHeader.split(' ')[1];
      const authService = new AuthService();
      const authContext = await authService.verifyToken(token);

      // Add auth context to request
      (req as any).auth = authContext;

      return handler(req);
    } catch (error) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
  };
}

// Middleware for permission checking
export function requirePermission(permission: string) {
  return function(handler: Function) {
    return withAuth(async (req: NextRequest) => {
      const authContext = (req as any).auth as AuthContext;
      
      const hasPermission = PermissionService.hasPermission(
        authContext.role,
        permission,
        authContext.permissions
      );

      if (!hasPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      return handler(req);
    });
  };
}

// Middleware for resource access checking
export function requireResourceAccess(resource: string, action: string) {
  return function(handler: Function) {
    return withAuth(async (req: NextRequest) => {
      const authContext = (req as any).auth as AuthContext;
      
      const canAccess = PermissionService.canAccessResource(
        authContext.role,
        resource,
        action,
        authContext.permissions
      );

      if (!canAccess) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      return handler(req);
    });
  };
}

export const authService = new AuthService();