import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>y, IsObject, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAgentDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty()
  @IsString()
  model: string;

  @ApiProperty()
  @IsString()
  systemPrompt: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  tools?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  memory?: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  settings?: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateAgentDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  model?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  systemPrompt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  tools?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  memory?: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  settings?: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class ChatDto {
  sessionId(id: string, organizationId: any, message: string, sessionId: any) {
    throw new Error('Method not implemented.');
  }
  @ApiProperty()
  @IsString()
  message: string;
}