import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateWorkflowDto, UpdateWorkflowDto, ExecuteWorkflowDto } from './dto/workflow.dto';
import { WorkflowEngineService } from './workflow-engine.service';

@Injectable()
export class WorkflowService {
  constructor(
    private prisma: PrismaService,
    private workflowEngine: WorkflowEngineService,
  ) {}

  async create(organizationId: string, dto: CreateWorkflowDto) {
    return this.prisma.workflow.create({
      data: {
        ...dto,
        organizationId,
        settings: dto.settings || {},
        nodes: {
          create: dto.nodes || [],
        },
        edges: {
          create: dto.edges || [],
        },
      },
      include: {
        nodes: true,
        edges: true,
      },
    });
  }

  async findAll(organizationId: string) {
    return this.prisma.workflow.findMany({
      where: { organizationId },
      include: {
        nodes: true,
        edges: true,
        executions: {
          take: 5,
          orderBy: { startedAt: 'desc' },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: string, organizationId: string) {
    return this.prisma.workflow.findFirst({
      where: { id, organizationId },
      include: {
        nodes: {
          include: {
            agent: true,
            tool: true,
          },
        },
        edges: true,
        executions: {
          orderBy: { startedAt: 'desc' },
        },
      },
    });
  }

  async update(id: string, organizationId: string, dto: UpdateWorkflowDto) {
    return this.prisma.workflow.update({
      where: { id },
      data: {
        ...dto,
        updatedAt: new Date(),
      },
      include: {
        nodes: true,
        edges: true,
      },
    });
  }

  async remove(id: string, organizationId: string) {
    return this.prisma.workflow.delete({
      where: { id },
    });
  }

  async execute(id: string, organizationId: string, dto: ExecuteWorkflowDto, userId: string) {
    const workflow = await this.findOne(id, organizationId);
    if (!workflow) {
      throw new BadRequestException('Workflow not found');
    }

    if (!workflow.isActive) {
      throw new BadRequestException('Workflow is not active');
    }

    const execution = await this.workflowEngine.executeWorkflow(
      id,
      organizationId,
      dto.input,
      userId,
    );

    return {
      executionId: execution.id,
      status: execution.status,
      startedAt: execution.startedAt,
    };
  }

  async getExecution(executionId: string) {
    return this.workflowEngine.getExecution(executionId);
  }

  async getWorkflowExecutions(workflowId: string, organizationId: string) {
    return this.workflowEngine.getWorkflowExecutions(workflowId, organizationId);
  }


}