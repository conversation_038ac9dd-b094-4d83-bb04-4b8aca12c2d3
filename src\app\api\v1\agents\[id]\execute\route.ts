import { NextRequest, NextResponse } from 'next/server';
import { requireResourceAccess } from '@/lib/auth';
import { AgentManager } from '@/lib/agents';

export const POST = requireResourceAccess('agents', 'read')(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const authContext = (request as any).auth;
    const { input, session_id, stream, context } = await request.json();

    if (!input) {
      return NextResponse.json(
        { error: 'Input is required' },
        { status: 400 }
      );
    }

    const agentManager = new AgentManager(authContext.organization.id);
    
    const execution = await agentManager.executeAgent(
      params.id,
      input,
      authContext.user.id,
      session_id,
      { stream, context }
    );

    return NextResponse.json({
      success: true,
      data: execution
    });

  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'Failed to execute agent' },
      { status: 500 }
    );
  }
});