#!/bin/sh
set -e

echo "Starting SynapseAI Production Server..."

# Run database migrations
echo "Running database migrations..."
npx prisma migrate deploy

# Seed initial data if needed
if [ "$SEED_DATABASE" = "true" ]; then
  echo "Seeding database..."
  npx prisma db seed
fi

# Start backend server in background
echo "Starting backend server..."
cd backend && node dist/main.js &
BACKEND_PID=$!

# Start frontend server
echo "Starting frontend server..."
cd ..
node server.js &
FRONTEND_PID=$!

# Wait for any process to exit
wait -n

# Exit with status of process that exited first
exit $?